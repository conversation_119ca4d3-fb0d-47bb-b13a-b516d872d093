#!/usr/bin/env bash

# Copies all necessary files and config needed for the app to build and run
DIR="$(dirname $(readlink -f $0))"
FACETEC_DIR="facetec-web-sdk"
cd "$DIR/.."

update_git_submodules() {
    git submodule init
    git submodule update --remote
}

if [ -d "public/$FACETEC_DIR" ]; then
    echo -e "INFO: Skipped cloning sdk and running git submodule update, already cloned \n"
    update_git_submodules
    rm -rf "public/$FACETEC_DIR"
    cp -r "$FACETEC_DIR" public
    exit 0
fi

update_git_submodules

echo -e "INFO: Copying "$FACETEC_DIR" to public for preview and prod builds \n"
cp -r "$FACETEC_DIR" public

if [ -d "public/$FACETEC_DIR" ]; then
    echo -e "INFO: Copying to /public successful \n"
fi
