#!/usr/bin/env bash

info() {
  echo '<<< Starting local server and serving build >>>'
}

usage() {
  echo "
  Usage: [-p port] [-h host] [-e environment] [-i instrument-code]

  Options:
  -e environment: 'production', 'staging', 'development'
  -i instrument-code: 'true' or 'false', default is false
  -p port: 8080, default is 9000
  -h host: 0.0.0.0 default is localhost
  "
}

parse_arguments() {
  while getopts ":p:h:i:m:e:" opt; do
    case $opt in
    e)
      export VITE_ENVIRONMENT="$OPTARG"
      # Check if the environment is allowed
      if [[ ! " ${allowed_environments[@]} " =~ " ${VITE_ENVIRONMENT} " ]]; then
        echo -e "Invalid environment: $VITE_ENVIRONMENT" >&2
        echo "Allowed values: ${allowed_environments[@]}"
        usage
        exit 1
      fi
      ;;
    p)
      export VITE_PORT="$OPTARG"
      ;;
    h)
      export VITE_HOST="$OPTARG"
      ;;
    i)
      export VITE_INSTRUMENT_CODE="$OPTARG"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      usage
      exit 1
      ;;
    :)
      echo "Option -$OPTARG requires an argument." >&2
      exit 1
      ;;
    esac
  done
}

load_env_variables() {
  if [ -f "../.env.local" ]; then
    echo "Loading environment variables from .env.local"
    # Load variables from .env.local but don't override existing ones (CLI takes precedence)
    while IFS= read -r line; do
      # Skip comments and empty lines
      if [[ $line =~ ^[[:space:]]*# ]] || [[ -z $line ]]; then
        continue
      fi

      # Extract variable name and value
      var_name=$(echo "$line" | cut -d'=' -f1)
      var_value=$(echo "$line" | cut -d'=' -f2-)

      # Only set if not already set (preserves CLI overrides)
      if [[ -z "${!var_name}" ]]; then
        export "$var_name=$var_value"
      else
        echo "  $var_name already set (CLI override), keeping existing value"
      fi
    done < <(grep -v '^#' ../.env.local | grep -v '^$')
  fi
}

# Define allowed values for options
allowed_environments=("development" "staging" "production")
allowed_variations=("full")

# Parse options and do argument values check
parse_arguments "$@"

readonly MY_TT_DIR="$(dirname "$0")"
cd $MY_TT_DIR

# Load .env.local environment variables
load_env_variables

if [[ -n $VITE_ENVIRONMENT  ]]; then
  echo -e "Building $VITE_ENVIRONMENT build with $VITE_ENVIRONMENT APIs    "
  # Generates a production
  ./build.sh $VITE_ENVIRONMENT $VITE_BUILD_VARIATION $VITE_INSTRUMENT_CODE
  # Serves the generated production bundle locally
  info
  VITE_PORT=$VITE_PORT VITE_HOST=$VITE_HOST npm run serve
else
  # Vite does not need to generate a bundle,
  # everything is handled by the vite dev server
  echo "<<< Starting local dev server >>>"
  npm run dev
fi
echo
