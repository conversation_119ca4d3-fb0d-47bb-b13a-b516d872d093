import { ASSET } from '../constants/Assets'
import style from '../style/BannerMessage.module.scss'
import { BannerMessageProps } from '../types/Banner.types'
import Icon from './Icon'

/**
 * Renders a message with specified props
 */
const BannerMessage = ({
  children,
  className,
  hideIcon,
  variant,
  fontSize,
}: BannerMessageProps) => {
  return (
    <article className={`${style['banner-message']} ${className ?? ''}`}>
      <div
        className={
          style[`banner-message__container${variant ? `--${variant}` : ''}`]
        }
      >
        {!hideIcon && (
          <Icon
            className={style[`banner-message__icon`]}
            fileName={ASSET.infoCircle}
          />
        )}
        <article
          className={
            style[`banner-message__text${fontSize ? `--${fontSize}` : ''}`]
          }
        >
          {children}
        </article>
      </div>
    </article>
  )
}

export default BannerMessage
