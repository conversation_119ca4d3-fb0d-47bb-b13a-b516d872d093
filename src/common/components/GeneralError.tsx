import { showReportDialog } from '@sentry/react'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import { ASSET } from '../constants/Assets'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/GeneralError.module.scss'
import { GeneralErrorProps } from '../types/GeneralError.types'
import Button from './Button'
import Icon from './Icon'

/**
 * @note Do **not** include sensitive data as an `errorMessage`, the error
 * message should be vague as possible when shown to the user!
 *
 * General error component, displays an error with default arguments
 */
const GeneralError = ({
  hideErrorTitle,
  errorMessage,
  hideNavButton,
}: GeneralErrorProps) => {
  const { isAuthenticated, context } = useAccountService()
  const t = useTranslate()

  const reportCrash = () => {
    if (isAuthenticated) {
      showReportDialog({
        user: {
          name: `${context?.user_details?.first_name} ${context?.user_details?.last_name}`,
          email: context?.user_details?.email,
        },
      })
    } else {
      showReportDialog()
    }
  }

  return (
    <main className={style['general-error']}>
      <section className={style[`general-error__container`]}>
        <Icon
          fileName={ASSET.infoCircle}
          className={style[`general-error__icon`]}
        />
        {!hideErrorTitle && (
          <p className={style[`general-error__user-text`]}>
            {t('ERROR_GENERIC')}
          </p>
        )}
        {errorMessage && (
          <p className={style[`general-error__error-msg`]}>{errorMessage}</p>
        )}
        <article className={style[`general-error__btn-container`]}>
          <Button onClick={reportCrash}>{t('CONTACT_US_BTN_LABEL')}</Button>
          {!hideNavButton && (
            <Button onClick={() => window.location.reload()}>{'Reload'}</Button>
          )}
        </article>
      </section>
    </main>
  )
}

export default GeneralError
