import { RangeSliderProps } from '../types/RangeSlider.types'
import Range from './Range'
import SliderBox from './SliderBox'

/**
 * Renders a slider box and a range input
 */
const RangeSlider = ({
  label,
  value,
  onChange,
  steps,
  className,
  formatter,
  prefix,
  disabled,
  disabledSliderTooltipText,
  disabledIncrement,
  disabledDecrement,
  variant,
  boxValueDataTestID,
  incrementButtonDataTestID,
  decrementButtonDataTestID,
  sliderTestID,
  trackSlider,
  enabledSteps,
}: RangeSliderProps) => {
  const sharedProps = {
    onChange: onChange,
    steps,
    title: label,
    value: value,
    className,
    formatter,
    prefix,
    disabled,
    disabledSliderTooltipText,
    disabledIncrement,
    disabledDecrement,
    variant,
  }

  return (
    <SliderBox
      {...sharedProps}
      boxValueDataTestID={boxValueDataTestID}
      incrementButtonDataTestID={incrementButtonDataTestID}
      decrementButtonDataTestID={decrementButtonDataTestID}
      sliderTestID={sliderTestID}
      trackSlider={trackSlider}
    >
      <Range
        {...sharedProps}
        trackRangeId={trackSlider?.sliderId}
        enabledSteps={enabledSteps}
      />
    </SliderBox>
  )
}

export default RangeSlider
