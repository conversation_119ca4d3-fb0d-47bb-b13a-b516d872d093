import style from '../style/DividerProgress.module.scss'
import { DividerProgressProps } from '../types/DividerProgress.types'
import Divider from './Divider'

/**
 *  Renders row of dividers to serve as a progress bar
 */
const DividerProgress = ({
  steps = 1,
  className,
  activeForm,
}: DividerProgressProps) => {
  return (
    <article className={`${style[`divider-progress`]} ${className ?? ''}`}>
      {Array.from({ length: steps })?.map((_, index) => (
        <Divider
          key={`${index}-divider-progress`}
          active={index < activeForm}
        />
      ))}
    </article>
  )
}

export default DividerProgress
