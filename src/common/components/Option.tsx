import { OptionProps } from '../types/Option.types'

/**
 * Used for dropdown menus to render option items
 */
const Option = <T,>({
  value,
  children,
  className,
  dataTestID,
  onClick,
}: OptionProps<T>) => {
  return (
    <li
      onClick={() => onClick?.(value)}
      data-testid={dataTestID}
      className={`${className ?? ''}`}
    >
      {children}
    </li>
  )
}

export default Option
