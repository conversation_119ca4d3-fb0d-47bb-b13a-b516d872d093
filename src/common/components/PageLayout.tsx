import style from '../style/PageLayout.module.scss'
import { PageLayoutProps } from '../types/Page.types'
import Header from './Header'

/**
 * Flex page layout with a header if a `headerTitle` is passed in
 */
const PageLayout = ({
  children,
  headerTitle,
  headerTextColor,
  containerWidth,
  containerHeight,
  headerVariant,
  layoutVariant,
  containerMt,
}: PageLayoutProps) => {
  return (
    <main
      className={
        style[`page-layout${layoutVariant ? `--${layoutVariant}` : ''}`]
      }
    >
      <section
        className={`
          ${
            style[
              `page-layout__container${containerWidth ? `--${containerWidth}` : ''}`
            ]
          }
          ${style[`page-layout__container${containerHeight ? `--${containerHeight}` : ''}`]}
          ${
            style[
              `page-layout__container${containerMt ? `--${containerMt}` : ''}`
            ]
          }
          `}
      >
        {headerTitle && (
          <Header
            title={headerTitle}
            textColor={headerTextColor}
            variant={headerVariant}
          />
        )}
        {children}
      </section>
    </main>
  )
}

export default PageLayout
