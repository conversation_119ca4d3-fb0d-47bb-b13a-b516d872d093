import style from '../style/InputGroup.module.scss'
import { InputGroupProps } from '../types/InputGroup.types'

/**
 * Groups input fields. Passing in `noStyle` ignores the
 * main container styling and just uses this component as a wrapper
 */
const InputGroup = ({
  children,
  dataTestID,
  noStyle,
  groupLabel,
  className,
  variant,
  borderColor,
}: InputGroupProps) => {
  return (
    <main
      className={`${style[`input-group${variant ? `--${variant}-${borderColor}` : ''}`]} ${className ?? ''}`}
    >
      <h3
        className={`${style[`input-group__label${variant ? `--${variant}` : ''}`]}`}
      >
        {groupLabel}
      </h3>

      <section
        className={
          noStyle ? '' : `${style[`input-group__container`]} ${className ?? ''}`
        }
        data-testid={dataTestID}
      >
        {children}
      </section>
    </main>
  )
}

export default InputGroup
