import style from '../../style/DateSliderBox.module.scss'
import { DateSliderBoxProps } from '../../types/DateSliderBox.types'
import { monthNumberToString } from '../../utils/UtilFunctions'
import DateBoxChunk from './DateBoxChunk'

/**
 * Renders a date header for month and year and range slider. Moving the range
 * slider updates the year, and adjusting the year in the header also moves the
 * slider
 */
const DateSliderBox = ({
  onClickIncrementMonths,
  onClickDecrementMonths,
  onClickIncrementYears,
  onClickDecrementYears,
  children,
  month,
  year,
  monthLabel,
  yearLabel,
  locale,
  disabledIncrementMonth,
  disabledDecrementMonth,
  disabledDecrementYear,
  disabledIncrementYear,
}: DateSliderBoxProps) => {
  return (
    <article className={style['date-slider-box']}>
      <article className={style[`date-slider-box__inner`]}>
        <DateBoxChunk
          label={monthLabel}
          onClickIncrement={onClickIncrementMonths}
          onClickDecrement={onClickDecrementMonths}
          dateNumber={month}
          monthNumberToString={monthNumberToString}
          disabledIncrement={disabledIncrementMonth}
          disabledDecrement={disabledDecrementMonth}
          locale={locale}
        />
        <DateBoxChunk
          label={yearLabel}
          onClickIncrement={onClickIncrementYears}
          onClickDecrement={onClickDecrementYears}
          dateNumber={year}
          disabledDecrement={disabledDecrementYear}
          disabledIncrement={disabledIncrementYear}
          locale={locale}
          renderRight
        />
      </article>
      {children}
    </article>
  )
}

export default DateSliderBox
