import { ASSET } from '../../constants/Assets'
import style from '../../style/BoxDoubleIncrements.module.scss'
import { BoxDoubleIncrementsProps } from '../../types/BoxDoubleIncrements.types'
import Divider from '../Divider'
import Icon from '../Icon'

/**
 * Renders increment and decrement buttons with arrow icons
 */
const BoxDoubleIncrements = ({
  className = '',
  onClickIncrement,
  onClickDecrement,
  disabledIncrement,
  disabledDecrement,
  renderRight,
}: BoxDoubleIncrementsProps) => {
  return (
    <section className={`${style.incrementBox} ${className}`}>
      <article className={style[`incrementBox__container`]}>
        <Icon
          fileName={ASSET.icononboardinarrowforward}
          className={`
          ${style['incrementBox__icon-up']}
          ${style['incrementBox__icon']}
          ${
            style[
              `incrementBox__icon-up${renderRight ? 'right' : 'left'}${disabledIncrement ? '--disabled' : ''}`
            ]
          }`}
          onClick={onClickIncrement}
        />
        <Divider className={`${style[`incrementBox__divider`]}`} />
        <Icon
          fileName={ASSET.icononboardinarrowforward}
          className={`
          ${style['incrementBox__icon-down']}
          ${style['incrementBox__icon']}
          ${
            style[
              `incrementBox__icon-down${renderRight ? 'right' : 'left'}${disabledDecrement ? '--disabled' : ''}`
            ]
          }
          `}
          onClick={onClickDecrement}
        />
      </article>
    </section>
  )
}

export default BoxDoubleIncrements
