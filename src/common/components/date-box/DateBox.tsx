import style from '../../style/DateBox.module.scss'
import { DateBoxProps } from '../../types/DateBox.types'

/**
 * Renders a date box with a label and a passed in date as a
 * number. Also supports passing in a formatter function to format the passed in date
 */
const DateBox = ({
  dateNumber,
  dateLabel,
  dateConvertFunction,
  locale,
  formatting,
  className = '',
}: DateBoxProps) => {
  return (
    <article className={`${style[`date-box`]} ${className}`}>
      <h2 className={style[`date-box__label`]}>{dateLabel}</h2>
      <p className={style[`date-box__main-content`]}>
        {dateConvertFunction && dateNumber !== undefined
          ? dateConvertFunction(dateNumber, locale, formatting)
          : dateNumber}
      </p>
    </article>
  )
}

export default DateBox
