import style from '../../style/DateBoxChunk.module.scss'
import { DateBoxChunkProps } from '../../types/DateBoxChunk.types'
import BoxDoubleIncrements from './BoxDoubleIncrements'
import DateBox from './DateBox'

/**
 * Renders an increment and decrement buttons on top of each other
 * and a month or year
 */
const DateBoxChunk = ({
  onClickIncrement,
  onClickDecrement,
  dateNumber,
  label,
  monthNumberToString,
  className = '',
  renderRight,
  locale,
  disabledIncrement,
  disabledDecrement,
}: DateBoxChunkProps) => {
  return (
    <div className={`${style['date-box-chunk']} ${className}`}>
      {!renderRight && (
        <BoxDoubleIncrements
          onClickDecrement={onClickDecrement}
          onClickIncrement={onClickIncrement}
          disabledIncrement={disabledIncrement}
          disabledDecrement={disabledDecrement}
        />
      )}
      <DateBox
        dateConvertFunction={monthNumberToString}
        locale={locale}
        formatting={'long'}
        dateNumber={dateNumber}
        dateLabel={label}
        className={style[`date-box-chunk__date-box`]}
      />
      {renderRight && (
        <BoxDoubleIncrements
          onClickDecrement={onClickDecrement}
          onClickIncrement={onClickIncrement}
          disabledIncrement={disabledIncrement}
          disabledDecrement={disabledDecrement}
          renderRight={renderRight}
        />
      )}
    </div>
  )
}

export default DateBoxChunk
