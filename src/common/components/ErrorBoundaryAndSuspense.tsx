import { Suspense } from 'react'
import { ANIMATION } from '../constants/Animations'
import { ErrorBoundaryAndSuspenseProps } from '../types/ErrorBoundaryAndSuspense.types'
import LottieAnimation from './LottieAnimation'
import SentryErrorBoundary from './SentryErrorBoundary'

/**
 * Wraps a component in a `ErrorBoundary` and `Suspense` component
 * and displays a fallback UI if the component fails to render.
 *
 * While a component is suspended it renders a loading animation
 */
const ErrorBoundaryAndSuspense = ({
  children,
  fallbackErrorComponent,
  hideNavButton,
}: ErrorBoundaryAndSuspenseProps) => {
  return (
    <SentryErrorBoundary
      fallbackErrorComponent={fallbackErrorComponent}
      hideNavButton={hideNavButton}
    >
      <Suspense
        fallback={
          <LottieAnimation
            animationName={ANIMATION?.splashLogo}
            autoplay
            loop
            style={{
              scale: '0.3',
            }}
          />
        }
      >
        {children}
      </Suspense>
    </SentryErrorBoundary>
  )
}

export default ErrorBoundaryAndSuspense
