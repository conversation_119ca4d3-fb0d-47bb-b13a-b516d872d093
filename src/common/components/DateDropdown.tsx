import dayjs from 'dayjs'
import { TESTING_IDS } from '../../../cypress/support/ui-component-ids'
import { regex } from '../constants/Regex'
import style from '../style/DateDropdown.module.scss'
import { DateDropdownProps } from '../types/DateDropdown.types'
import {
  DECEMBER,
  DEFAULT_YEAR_MAX,
  DEFAULT_YEAR_MIN,
  FEBRUARY,
  JANUARY,
  START_DAY,
} from '../utils/consts'
import {
  adjustDayIfFebruary,
  daysInMonthAndYear,
  destructToNumbers,
  generateRange,
  monthNumberToString,
} from '../utils/UtilFunctions'
import Dropdown from './Dropdown'

/**
 * Dropdown date component with YYYY-MM-DD format, leap years are handled
 * within the component
 */
const DateDropdown = ({
  mode,
  onChange,
  value,
  yearFrom,
  yearTo,
  monthFrom,
  monthTo,
  dayFrom,
  dayTo,
  format = 'YYYY-MM-DD',
  locale = 'en-US',
  monthFormatting = 'long',
  label,
  yearLabel,
  readOnly = false,
  ...rest
}: DateDropdownProps) => {
  const day = destructToNumbers(value)?.day ?? START_DAY
  const month = destructToNumbers(value)?.month ?? JANUARY
  const year = destructToNumbers(value)?.year ?? DEFAULT_YEAR_MIN

  return (
    <article
      {...rest}
      data-test-id={TESTING_IDS.dateDropdown}
      className={style['date-dropdown']}
    >
      {mode !== 'monthYear' && (
        <Dropdown
          optional
          label={label}
          readOnly={readOnly}
          className={style['date-dropdown__day']}
          searchBy={['label', 'value']}
          itemKey={{
            displayKey: 'label',
          }}
          value={{
            value: day.toString(),
            label: day.toString(),
          }}
          options={generateRange(
            dayFrom ?? START_DAY,
            dayTo ?? daysInMonthAndYear({ month, year })
          ).map((day) => {
            const dayString = day.toString()
            return {
              value: dayString,
              label: dayString,
            }
          })}
          onChange={(option) => {
            onChange(
              dayjs()
                .year(year)
                .month(month)
                .date(Number.parseInt(option.value, 10))
                .format(format)
            )
          }}
          dataTestID={TESTING_IDS.dateDropdownDay}
          restrictionRegex={regex.stringDigits}
        />
      )}
      <Dropdown
        optional
        readOnly={readOnly}
        label={mode === 'monthYear' ? label : ''}
        className={style['date-dropdown__month']}
        options={generateRange(monthFrom ?? JANUARY, monthTo ?? DECEMBER).map(
          (month) => ({
            value: month,
            //Month +1 not to break compatibility with other components
            label: monthNumberToString(month + 1, locale, monthFormatting),
          })
        )}
        value={{
          value: month,
          //Month +1 not to break compatibility with other components
          label: monthNumberToString(month + 1, locale, monthFormatting),
        }}
        searchBy={['label']}
        itemKey={{
          displayKey: 'label',
        }}
        onChange={(option) => {
          const adjustedDay = adjustDayIfFebruary({
            day,
            month: option.value,
            year,
            februaryMonthIndex: FEBRUARY,
          })

          onChange(
            dayjs()
              .year(year)
              .month(option.value)
              .date(adjustedDay)
              .format(format)
          )
        }}
        dataTestID={TESTING_IDS.dateDropdownMonth}
      />
      <Dropdown
        optional
        label={yearLabel}
        readOnly={readOnly}
        className={style['date-dropdown__year']}
        options={generateRange(
          yearFrom ?? DEFAULT_YEAR_MIN,
          yearTo ?? DEFAULT_YEAR_MAX
        ).map((year) => {
          const yearString = year.toString()
          return {
            value: yearString,
            label: yearString,
          }
        })}
        value={{
          value: year?.toString(),
          label: year?.toString(),
        }}
        onChange={(option) => {
          onChange(
            dayjs()
              .year(Number.parseInt(option.value, 10))
              .month(month)
              .date(day)
              .format(format)
          )
        }}
        searchBy={['label', 'value']}
        itemKey={{
          displayKey: 'label',
        }}
        dataTestID={TESTING_IDS.dateDropdownYear}
        alternateLabel
        restrictionRegex={regex.stringDigits}
      />
    </article>
  )
}

export default DateDropdown
