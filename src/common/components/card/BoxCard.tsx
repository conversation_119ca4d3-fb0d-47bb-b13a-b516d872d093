import style from '../../style/BoxCard.module.scss'
import { SharedCardProps } from '../../types/Card.types'
import { Card, CardContent, CardHeader, CardInfo } from './Card'

/**
 * A box-styled card component that displays information with a header and content.
 */
export const BoxCard = ({
  icon,
  title,
  subtitle,
  variant,
  className,
  alternative,
  ...props
}: SharedCardProps & { alternative?: boolean }) => {
  return (
    <Card
      variant={variant}
      className={`${style[`boxCard`]} ${className}`}
      {...props}
    >
      <CardHeader
        variant={variant}
        icon={icon}
        className={style[`boxCard__header`]}
      >
        <CardInfo
          variant={variant}
          showArrow
          className={style[`boxCard__info`]}
        />
      </CardHeader>

      <CardContent
        variant={variant}
        className={style[`boxCard__body`]}
        title={title}
        titleProps={{
          className: style[`boxCard__title`],
        }}
        subtitle={subtitle}
        subtitleProps={{
          className: `${style[`boxCard__subtitle`]} ${alternative ? style[`boxCard__subtitle--alt`] : ''}`,
        }}
      />
    </Card>
  )
}
