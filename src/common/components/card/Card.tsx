import { NavLink } from 'react-router'
import { UI_TEST_ID } from '../../constants/DataTestIDs'
import style from '../../style/Card.module.scss'
import {
  BoxProps,
  CardContentProps,
  CardHeaderProps,
  CardProps,
  CardVariants,
} from '../../types/Card.types'
import { CardInfoProps } from '../../types/CardInfo.types'
import Icon from '../Icon'
import Card<PERSON>lert from './CardAlert'
import CardArrow from './CardArrow'

/**
 * Card components for building card UIs.
 */
export const Card = ({
  children,
  dataTestID,
  className,
  variant,
  active,
  disabled,
  rounded,
  href,
  ...props
}: CardProps) => {
  const cardClasses = (isActive: boolean) => {
    return [
      style['card'],
      variant && style[`card--${variant}`],
      rounded && style[`card--${rounded}`],

      disabled && style[`card--disabled`],
      isActive && style[`card--active`],

      className && className,
    ]
      .filter(Boolean)
      .join(' ')
  }
  return (
    <>
      {href ? (
        <NavLink
          {...props}
          to={href}
          data-testid={dataTestID ?? UI_TEST_ID?.commonCardNavigation}
          className={({ isActive }) =>
            `
          ${cardClasses(isActive)}
          ${!disabled && !isActive ? style[`card--enable-interact`] : ''} 
          ${variant && !disabled && style[`card--${variant}--enable-interact`]}
          `
          }
        >
          {children}
        </NavLink>
      ) : (
        <article
          {...props}
          data-testid={dataTestID ?? UI_TEST_ID?.commonCard}
          className={`
            ${cardClasses(active ?? false)}
            ${!disabled && !active && props?.onClick ? style[`card--enable-interact`] : ''} 
            ${variant && !disabled && props?.onClick && style[`card--${variant}--enable-interact`]}
          `}
        >
          {children}
        </article>
      )}
    </>
  )
}

/**
 * Header component for Card, containing optional icon and content.
 * Provides styling variants and customizable icon properties.
 */
export const CardHeader = ({
  className,
  iconProps: { className: iconClassName, iconSize, ...iconRest } = {},
  icon,
  variant,
  children,
  ...props
}: CardHeaderProps) => (
  <div
    {...props}
    data-testid={UI_TEST_ID?.cardHeader}
    className={`${style[`card__header`]} ${className ? className : ''} ${variant ? style[`card__header--${variant}`] : ''}`}
  >
    {icon && (
      <Icon
        {...iconRest}
        fileName={icon}
        className={`
          ${style[`card__icon`]}
          ${style[`card__icon--${iconSize}`]}
          ${style[`${iconClassName ? iconClassName : ''}`]}
        `}
      />
    )}
    {children}
  </div>
)

/** CardContent renders the main content area of a card, including title and subtitle.
 */
export const CardContent = ({
  title,
  subtitle,
  titleProps: { className: titleClassName, ...titleRest } = {},
  subtitleProps: { className: subtitleClassName, ...subtitleRest } = {},
  children,
  className,
  variant,
  ...rest
}: CardContentProps) => (
  <div
    data-testid={UI_TEST_ID?.cardHeader}
    className={`${style[`card__body`]} ${className ? className : ''} ${variant ? style[`card__body--${variant}`] : ''}`}
    {...rest}
  >
    {title && (
      <h3
        {...titleRest}
        className={`${style[`card__title`]} ${titleClassName ? titleClassName : ''} ${variant ? style[`card__title--${variant}`] : ''}`}
      >
        {title}
      </h3>
    )}
    {subtitle && (
      <h4
        {...subtitleRest}
        className={`${style[`card__subtitle`]} ${subtitleClassName ? subtitleClassName : ''} ${variant ? style[`card__subtitle--${variant}`] : ''}`}
      >
        {subtitle}
      </h4>
    )}
    {children}
  </div>
)

/**
 * CardFooter renders the footer section of a card.
 */
export const CardFooter = ({
  className,
  children,
  variant,
  ...props
}: BoxProps & CardVariants) => (
  <div
    {...props}
    className={`${style[`card__footer`]} ${className ? className : ''} ${variant ? style[`card__footer--${variant}`] : ''}`}
  >
    {children}
  </div>
)

/**
 * CardInfo displays additional info in a card's footer.
 * Optionally shows an alert and an arrow, with custom content and styling.
 */
export const CardInfo = ({
  className,
  showArrow = true,
  children,
  cardArrowProps,
  cardAlertProps,
  variant,
  ...props
}: CardInfoProps) => (
  <div
    {...props}
    className={`${style['card__info']} ${className ? className : ''} ${variant ? style[`card__info--${variant}`] : ''}`}
  >
    {cardAlertProps?.alert ? <CardAlert {...cardAlertProps} /> : null}
    {showArrow && <CardArrow {...cardArrowProps} />}
    {children}
  </div>
)
