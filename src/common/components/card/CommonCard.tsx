import {
  CardVariantType,
  IconSizes,
  SharedCardProps,
} from '../../types/Card.types'
import { CardInfoProps } from '../../types/CardInfo.types'
import { Card, CardContent, CardFooter, CardHeader, CardInfo } from './Card'

type CommonCardProps = {
  variant?: CardVariantType
  cardInfoProps?: CardInfoProps
  iconSize?: IconSizes
} & SharedCardProps

/**
 * CommonCard renders a card with optional icon, title, subtitle, and footer info.
 */
const CommonCard = ({
  icon,
  title,
  subtitle,
  variant,
  iconSize,
  cardInfoProps,
  ...props
}: CommonCardProps) => {
  return (
    <Card variant={variant} {...props}>
      {icon && (
        <CardHeader iconProps={{ iconSize }} icon={icon} variant={variant} />
      )}

      <CardContent variant={variant} title={title} subtitle={subtitle} />

      <CardFooter variant={variant}>
        <CardInfo {...cardInfoProps} variant={variant} />
      </CardFooter>
    </Card>
  )
}

export default CommonCard
