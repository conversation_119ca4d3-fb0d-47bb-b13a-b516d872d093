import { useState } from 'react'
import { UI_TEST_ID } from '../../constants/DataTestIDs'
import style from '../../style/ExtendedContentCard.module.scss'
import { SharedCardProps } from '../../types/Card.types'
import { CardAlertType } from '../../types/CardInfo.types'
import CommonCard from './CommonCard'

type ExtendedContentCardProps = {
  autoExpand?: boolean
  expandClickDisabled?: boolean
  extendedCardVariant?: 'payout'
  className?: string
  alert?: CardAlertType
  showArrow?: boolean
} & SharedCardProps

/** Extended content wrapper for the `Card` component, renders an additional component alongside the card. */
const ExtendedContentCard = ({
  onClick,
  extendedCardVariant,
  children,
  autoExpand = false,
  expandClickDisabled,
  rounded,
  dataTestID,
  showArrow,
  alert,
  ...restProps
}: ExtendedContentCardProps) => {
  const [expand, setExpand] = useState(autoExpand)
  const toggleExpand = () => {
    onClick?.()

    setExpand((prev) => !prev)
  }

  const cardClasses = [
    style.extendedCard,
    style[`extendedCard--${rounded}`],
    extendedCardVariant && style[`extendedCard--${extendedCardVariant}`],
    restProps?.className ?? '',
  ]
    .filter(Boolean)
    .join(' ')

  return (
    <article
      data-testid={dataTestID ?? UI_TEST_ID?.commonCardExtended}
      className={cardClasses}
    >
      <CommonCard
        {...restProps}
        onClick={expandClickDisabled ? undefined : toggleExpand}
        rounded={'off'}
        cardInfoProps={{
          showArrow: showArrow ?? true,
          cardAlertProps: {
            alert,
          },
          cardArrowProps: { rotateArrow: expand ? 'up' : 'down' },
        }}
      />
      {expand && children}
    </article>
  )
}

export default ExtendedContentCard
