import { SharedCardProps } from '../../types/Card.types'
import { Card, CardContent, CardFooter, CardHeader } from './Card'

type StatCardProps = {
  statDate?: string
} & SharedCardProps

/**
 * StatCard renders a card for displaying statistics.
 */
const StatCard = ({
  icon,
  title,
  subtitle,
  statDate,
  iconSize,
  ...props
}: StatCardProps) => {
  return (
    <Card {...props}>
      <CardHeader iconProps={{ iconSize }} icon={icon} />

      <CardContent title={title} subtitle={subtitle} />

      <CardFooter>
        <span>{statDate}</span>
      </CardFooter>
    </Card>
  )
}

export default StatCard
