import { ASSET } from '../../constants/Assets'
import { UI_TEST_ID } from '../../constants/DataTestIDs'
import style from '../../style/Card.module.scss'
import { CardAlertProps, CardAlertType } from '../../types/CardInfo.types'
import Icon from '../Icon'

const alertIcons: Record<CardAlertType, string> = {
  completed: ASSET.iconaccountcheckedreencirclemall,
  warn: ASSET.infoamber,
  error: ASSET.icononboardininfocirclemall,
  pending: ASSET.yellowClock,
  not_reviewed: ASSET.pending,
  rejected: ASSET.iconaccounterrorcirclelare,
  approved: ASSET.iconaccountcheckedreencirclemall,
  cancelled: ASSET.closeDark,
}
/**
 * Renders an alert component based on the provided alert type.
 */
const CardAlert = ({ alert, className }: CardAlertProps) => {
  if (!alert) return null
  const iconFileName = alertIcons?.[alert]
  if (iconFileName) {
    return (
      <Icon
        dataTestId={UI_TEST_ID?.cardAlert}
        className={`${style[`card__alert`]} ${style[`card__alert--${alert}`]} ${className ? className : ''}`}
        fileName={iconFileName}
      />
    )
  }

  if (typeof alert === 'number' && alert > 0) {
    return (
      <div
        data-testid={UI_TEST_ID?.cardAlert}
        className={`${style[`card__alert`]} ${style[`card__alert--number`]} ${className ? className : ''}`}
      >
        {alert}
      </div>
    )
  }

  return null
}

export default CardAlert
