import { ANIMATION } from '../constants/Animations'
import style from '../style/Suspense.module.scss'
import LottieAnimation from './LottieAnimation'

/**
 * Used for lazy loading as a fallback component, displays a
 * loading animation while the lazy loaded component loads
 */
const SuspenseLoader = () => {
  return (
    <div className={style[`spinner-centered`]}>
      <LottieAnimation
        animationName={ANIMATION?.splashLogo}
        autoplay
        loop
        className="splash-image"
      />
    </div>
  )
}

export default SuspenseLoader
