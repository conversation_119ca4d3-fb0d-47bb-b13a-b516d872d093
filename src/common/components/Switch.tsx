import style from '../style/Switch.module.scss'
import { SwitchProps } from '../types/Swtitch.types'

/**
 * Switch built from HTML checkbox element
 */
const Switch = ({ checked, onChange, label }: SwitchProps) => {
  return (
    <div className={style.switch}>
      <label className={style['switch__body']}>
        <input
          className={style[`switch__input`]}
          checked={checked}
          onChange={(event) => onChange(event.target.checked)}
          type="checkbox"
        />
        <span className={style[`switch__toggle`]}>
          <span className={style[`switch__toggle--off`]}>OFF</span>
          <span className={style[`switch__toggle--on`]}>ON</span>
        </span>
      </label>
      <div className={style[`switch__label`]}>{label}</div>
    </div>
  )
}

export default Switch
