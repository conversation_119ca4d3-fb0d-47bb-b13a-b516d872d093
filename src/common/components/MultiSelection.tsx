import React, { useState } from 'react'
import style from '../style/MultiSelection.module.scss'
import {
  MultiSelectionProps,
  SelectionItem,
  SelectionProps,
} from '../types/MultiSelection.types'

//FIXME: REACT ANTI PATTERN THIS SHOULD BE A SEPARATE COMPONENT!!!
/**
 * Select item that contains the data of the selection
 */

const Selection = ({
  title,
  desc,
  activeClassName,
  onSelectionClick,
}: SelectionProps) => {
  //Allow multiple items to be active concurrently
  const [active, setActive] = useState(false)
  const multipleActiveItems = () => (active ? 'active' : 'inactive')

  return (
    <div
      className={style[`multi-selection__list-item`]}
      onClick={(data) => {
        setActive((prev) => !prev)
        onSelectionClick?.(data)
      }}
    >
      <div>
        <p className={style['multi-selection__title']}>{title}</p>
        {desc && <p className={style['multi-selection__sub']}>{desc}</p>}
      </div>
      <div
        className={
          style[
            `multi-selection__action--${
              //If activeClassName is passed in then the component is in unique
              //mode, meaning only one item can be selected, otherwise multiple
              //items can be selected
              activeClassName ? activeClassName : multipleActiveItems()
            }`
          ]
        }
      />
    </div>
  )
}

/**
 * Renders a multi selection component with cards as items,
 * contains a checkmark icon to indicate if the selection is selected or not.
 */

const MultiSelection = ({
  multiSelectionCardLabel,
  onSelection,
  setMultiSelectionData,
  multiSelectionData,
  allowMultipleItems,
}: MultiSelectionProps) => {
  // This function is called when the users clicks on a selection, then the object of that selection is selected and gets returned in the onSelection prop function of this component.
  const selectItem = (item: SelectionItem) => {
    setMultiSelectionData({
      ...multiSelectionData,
      activeSelection: item,
    })
    //getActiveStyles(item)
    onSelection(item)
  }

  // This function activates the selected selection and deactivate other selections
  const getActiveStyles = (selection: SelectionItem) => {
    return selection?.id === multiSelectionData?.activeSelection?.id
      ? 'active'
      : 'inactive'
  }

  /**
   * Checks if the passed in argument has a title, so it can render
   * it as label for selection item. Otherwise we assume it is the currencies
   * from the currency API so a title symbol is added next to the label
   *
   * @note TODO: This is a quick patch. Change this soon as possible when the
   * design is a bit concrete!
   */
  const checkIfSelectionHasTitle = (selection: SelectionItem) =>
    selection?.title ? selection?.title : selection

  // This function renders all the values which this component gets through props in the form of an array.
  const renderMultiSelections = multiSelectionData?.data?.map(
    (selection, index) => {
      return (
        <React.Fragment key={`${selection?.title}${index}`}>
          <Selection
            title={checkIfSelectionHasTitle(selection) as string}
            desc={selection?.desc}
            activeClassName={
              allowMultipleItems ? undefined : getActiveStyles(selection)
            }
            onSelectionClick={() => selectItem(selection)}
          />
        </React.Fragment>
      )
    }
  )

  return (
    <div className={style['multi-selection']}>
      <p className={style['multi-selection__label']}>
        {multiSelectionCardLabel}
      </p>
      <div className={style['multi-selection__container']}>
        {renderMultiSelections}
      </div>
    </div>
  )
}

export default MultiSelection
