import { track } from '../analytics/Analytics'
import { SliderEvent } from '../analytics/EventData'
import { ObjectIdProperty } from '../analytics/ObjectId'
import { ASSET } from '../constants/Assets'
import style from '../style/SliderInput.module.scss'
import { SliderBoxProps } from '../types/SliderBox.types'
import Icon from './Icon'
import InputLabel from './InputLabel'
import PageContent from './PageContent'

/**
 * Renders a box with a draggable slider below it, the box displays
 * the slider's value. The slider box is wrapped in a tooltip, which displays
 * text information when the slider is disabled when hovered over. Clicking the
 * box drags the slider by one step.
 */
const SliderBox = ({
  formatter,
  prefix,
  onChange,
  steps,
  title,
  value = 0,
  disabled = false,
  children,
  disabledIncrement,
  disabledDecrement,
  boxValueDataTestID,
  incrementButtonDataTestID,
  decrementButtonDataTestID,
  sliderTestID,
  trackSlider,
  variant,
  disabledSliderTooltipText,
}: SliderBoxProps) => {
  /**
   * Tracks the slider click events
   */
  const trackSliderClick = ({
    trackId,
    eventDescription,
    value,
  }: {
    trackId?: ObjectIdProperty
    eventDescription?: string
    value: number
  }) => {
    void track({
      event: SliderEvent.button_clicked,
      properties: {
        object_id: trackId,
        description: eventDescription,
        label: title,
        // See what to do with value, it comes from other elements
        object_value: value,
      },
    })
  }

  /**
   * Indicates whether the value should be increased
   * otherwise it is decremented by default
   *
   * Generates an array index that is used by the `steps` array to
   * update the slider value
   *
   * Returns the value from the `steps` array, if that value
   * does not exist it returns the min value if `increase` is true, otherwise
   * the max value of the array
   */
  const updateSliderValue = (increase?: boolean) => {
    const currentIndex = steps?.indexOf(value)

    if (increase) {
      return steps[(currentIndex + 1) % steps?.length]
    }

    return currentIndex === 0
      ? steps[steps?.length - 1]
      : steps[currentIndex - 1]
  }

  const increaseSliderValueByOne = () => {
    onChange?.(updateSliderValue(true))
    trackSliderClick({
      trackId: trackSlider?.incBtnId,
      eventDescription: trackSlider?.incBtnDesc,
      value: updateSliderValue(true),
    })
  }
  const decreaseSliderValueByOne = () => {
    onChange?.(updateSliderValue())
    trackSliderClick({
      trackId: trackSlider?.decBtnId,
      eventDescription: trackSlider?.decBtnDesc,
      value: updateSliderValue(),
    })
  }

  const updateSliderValueByOneBox = () => {
    onChange?.(updateSliderValue(true))
    trackSliderClick({
      trackId: trackSlider?.incBtnBoxId,
      eventDescription: trackSlider?.incBtnBoxDesc,
      value: updateSliderValue(true),
    })
  }

  return (
    <section className={style[`sliderInput__main-container`]}>
      <InputLabel
        label={title}
        className={`${style[`sliderInput${disabled ? '--disabled' : ''}`]}`}
      />
      {disabled && disabledSliderTooltipText && (
        <PageContent
          mainContent={disabledSliderTooltipText}
          className={style['sliderInput__disabled-msg']}
        />
      )}
      <article
        className={`${style[`sliderInput${disabled ? '--disabled' : ''}`]}`}
        data-testid={sliderTestID}
      >
        <div className={style['sliderInput__container']}>
          <div
            className={`${style[`sliderInput__controls${variant ? `--${variant}` : ''}`]} 
            ${style[`sliderInput__controls--${disabledDecrement || disabled ? 'disabled' : ''}`]}`}
            onClick={decreaseSliderValueByOne}
            data-testid={decrementButtonDataTestID}
          >
            <Icon
              className={`${
                style[`sliderInput__icon${variant ? `--${variant}` : ''}`]
              } ${style['sliderInput__icon--down']}`}
              fileName={ASSET.icononboardinarrowforward}
            />
          </div>
          <div
            className={style['sliderInput__inner']}
            onClick={disabled ? undefined : updateSliderValueByOneBox}
          >
            <p className={style['sliderInput__prefix']}>{prefix}</p>
            <p
              className={
                style[`sliderInput__value${disabled ? '--disabled' : ''}`]
              }
              data-testid={boxValueDataTestID}
            >
              {formatter ? formatter(value) : value}
            </p>
          </div>
          <div
            className={`${style[`sliderInput__controls${variant ? `--${variant}` : ''}`]}  
            ${style[`sliderInput__controls--${disabledIncrement || disabled ? 'disabled' : ''}`]}`}
            onClick={increaseSliderValueByOne}
            data-testid={incrementButtonDataTestID}
          >
            <Icon
              className={`${
                style[`sliderInput__icon${variant ? `--${variant}` : ''}`]
              } ${style['sliderInput__icon--up']}`}
              fileName={ASSET.icononboardinarrowforward}
            />
          </div>
        </div>
        {children}
      </article>
    </section>
  )
}

export default SliderBox
