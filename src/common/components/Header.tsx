import style from '../style/Header.module.scss'
import { HeaderProps } from '../types/Header.types'

/**
 * Renders a header with a title text
 */
const Header = ({
  title,
  className,
  containerClassName,
  textColor = 'default',
  variant,
}: HeaderProps) => {
  return (
    <header
      className={`${style[`header${variant ? `--${variant}` : ''}`]} ${containerClassName}`}
    >
      <h1
        className={`${style[`header__text--${textColor}`]}  ${className ?? ''}`}
      >
        {title}
      </h1>
    </header>
  )
}

export default Header
