import style from '../style/ToggleButton.module.scss'
import { ToggleProps } from '../types/InputTypes.type'

/**
 * Renders a toggle button that contains active and inactive style
 */
const ToggleButton = ({
  label,
  onChange,
  toggled,
  testID,
  icon,
}: ToggleProps) => {
  return (
    <article
      onClick={onChange}
      className={style[`toggle-button`]}
      data-testid={testID}
    >
      <div
        className={
          style[`toggle-button__container${toggled ? '--active' : ''}`]
        }
      >
        <p className={style['toggle-button__label']}>{label}</p>
        {icon}
      </div>
    </article>
  )
}

export default ToggleButton
