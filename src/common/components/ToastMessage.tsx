import style from '../style/ToastMessage.module.scss'
import { ToastMessageProps } from '../types/ToastMessage.types'

/**
 * Used for rendering content for react-toastify toast message
 */
const ToastMessage = ({
  title,
  content,
  textColor = '',
}: ToastMessageProps) => {
  return (
    <article className={style[`toast-message--${textColor}`]}>
      {title && <p className={style['toast-message__title']}>{title}</p>}
      {content && <p className={style['toast-message__content']}>{content}</p>}
    </article>
  )
}

export default ToastMessage
