import style from '../style/TitleAndClickableText.module.scss'
import { TitleAndClickableTextProps } from '../types/TitleAndClickableText.types'

const TitleAndClickableText = ({
  title,
  subTitle,
  clickableText,
  onClickText,
  customComponent,
}: TitleAndClickableTextProps) => {
  return (
    <section className={style['title-and-clickable-text']}>
      <h3 className={style['title-and-clickable-text__title']}>{title}</h3>
      {customComponent ? (
        customComponent
      ) : (
        <>
          <p className={style['title-and-clickable-text__sub-title']}>
            {subTitle}
          </p>
          <p
            onClick={onClickText}
            className={style['title-and-clickable-text__clickable-text']}
          >
            {clickableText}
          </p>
        </>
      )}
    </section>
  )
}

export default TitleAndClickableText
