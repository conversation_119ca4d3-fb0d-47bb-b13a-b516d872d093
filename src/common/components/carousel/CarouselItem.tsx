import style from '../../style/Carousel.module.scss'
import { CarouselItemProps } from '../../types/CarouselItem.types'
import LottieAnimation from '../LottieAnimation'
import WebpImage from '../WebpImage'

/**
 * Carousel item that contains the main content of the carousel.
 * Images and lottie animations are supported
 */
const CarouselItem = ({
  webpImageName,
  backupImageName,
  animationName,
  title,
  content,
}: CarouselItemProps) => {
  return (
    <article className={style['carousel__item']}>
      <div className={style['carousel__item-container']}>
        {animationName ? (
          <LottieAnimation
            animationName={animationName}
            autoplay
            loop
            className={style[`carousel__image`]}
          />
        ) : (
          <WebpImage
            className={style['carousel__image']}
            webpName={webpImageName ?? ''}
            alternativeImageName={backupImageName ?? ''}
            alt={webpImageName ?? ''}
          />
        )}
        <h1 className={style['carousel__title']}>{title ?? ''}</h1>
        <p className={style['carousel__content']}>{content ?? ''}</p>
      </div>
    </article>
  )
}

export default CarouselItem
