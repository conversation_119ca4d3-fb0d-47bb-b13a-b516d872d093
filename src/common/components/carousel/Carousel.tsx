import { useEffect, useRef, useState } from 'react'
import style from '../../style/Carousel.module.scss'
import { CarouselProps, CarouselSlide } from '../../types/Carousel.types'
import CarouselControls from './CarouselControls'
import CarouselIndicators from './CarouselIndicators'
import CarouselItem from './CarouselItem'

/**
 * Carousel component that displays a list of slides. Supports
 * automatic slide change and slide change via carousel controls. Carousel
 * controls and indicators are optional and can be hidden.
 */
const Carousel = ({
  slides,
  startSlide = 0,
  autoSlide,
  slideChangeIntervalMilliseconds = 5000,
  hideControls,
  hideIndicators,
}: CarouselProps) => {
  //State
  const [currentSlideIndex, setCurrentSlideIndex] = useState(startSlide)
  const slideInterval = useRef<NodeJS.Timeout | null>(null)

  //Slide indexes
  const firstSlide = 0
  const lastSlide = slides.length - 1
  //Slide index changing
  const goToNextSlide =
    currentSlideIndex < lastSlide ? currentSlideIndex + 1 : firstSlide
  const gotoPreviousSlide =
    currentSlideIndex > firstSlide ? currentSlideIndex - 1 : lastSlide

  /**
   * Stops the slider from auto-sliding
   */
  const stopSliderTimer = () => {
    if (slideInterval.current) {
      clearInterval(slideInterval.current)
    }
  }

  /**
   * Starts the slider so it can automatically switch slides on interval
   */
  const startSlideTimer = () => {
    stopSliderTimer()
    slideInterval.current = setInterval(() => {
      setCurrentSlideIndex((currentSlideIndex) =>
        //if the slider reaches the end then start from the first one
        currentSlideIndex < lastSlide ? currentSlideIndex + 1 : firstSlide
      )
    }, slideChangeIntervalMilliseconds)
  }

  /**
   * Checks if the current slide is the last one
   */
  const isLastSlide = () => currentSlideIndex === lastSlide

  /**
   * Checks if the current slide is the first one
   */
  const isFirstSlide = () => currentSlideIndex === firstSlide

  /**
   * Navigates to the previous slide
   */
  const previousSlide = () => {
    if (autoSlide) {
      //Restarts the timer when the user clicks the previous slide buttons
      startSlideTimer()
    }
    //When the first slide is reached and the user clicks the previous button,
    //the last slide is displayed
    setCurrentSlideIndex(gotoPreviousSlide)
  }

  /**
   * Navigates to the next slide
   */
  const nextSlide = () => {
    if (autoSlide) {
      //Restarts the timer when the user clicks the next slide buttons
      startSlideTimer()
    }
    //When the last slide is reached and the user clicks the next button,
    //the slideshow starts from the beginning
    setCurrentSlideIndex(goToNextSlide)
  }

  const selectSlide = (index: number) => {
    if (autoSlide) {
      startSlideTimer()
    }
    //Selects a slide and makes it current
    setCurrentSlideIndex(index)
  }

  const renderCarouselItems = () =>
    slides?.map((slide: CarouselSlide, index: number) => (
      <CarouselItem
        key={index}
        webpImageName={slide?.imageWebp}
        backupImageName={slide?.imagePng}
        title={slide?.title}
        content={slide?.content}
        animationName={slide?.animation}
      />
    ))

  /**
   * Uses CSS transform to move the slider to the left or right by
   * multiplying the current number of the slide by 100
   */
  const moveSlider = (currentSlideIndex: number) => {
    return { transform: `translate(${-currentSlideIndex * 100}%)` }
  }

  useEffect(() => {
    if (autoSlide) {
      startSlideTimer()
    }
    return () => stopSliderTimer()
  }, [autoSlide])

  return (
    <section className={style.carousel}>
      <div
        className={style['carousel__inner']}
        style={moveSlider(currentSlideIndex)}
      >
        {renderCarouselItems()}
      </div>
      {!hideIndicators && (
        <CarouselIndicators
          slides={slides}
          currentSlideIndex={currentSlideIndex}
          selectSlide={selectSlide}
        />
      )}
      {!hideControls && (
        <CarouselControls
          previousSlide={previousSlide}
          nextSlide={nextSlide}
          hideNextControlArrow={isLastSlide()}
          hidePreviousControlArrow={isFirstSlide()}
        />
      )}
    </section>
  )
}

export default Carousel
