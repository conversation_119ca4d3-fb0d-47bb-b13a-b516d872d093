import style from '../../style/Carousel.module.scss'
import { CarouselSlide } from '../../types/Carousel.types'
import { CarouselIndicatorsProps } from '../../types/CarouselIndicators.types'

/**
 * Renders carrousel indicators that show the current active slide.
 * The indicators are clickable, so that the user can select the slide they want.
 */
const CarrouselIndicators = ({
  slides,
  currentSlideIndex,
  selectSlide,
}: CarouselIndicatorsProps) => {
  const renderSlidesIndicator = () =>
    slides?.map((slide: CarouselSlide, slideIndex: number) => (
      <div
        onClick={() => selectSlide(slideIndex)}
        key={`${slideIndex}${slide?.title}`}
        className={`${style[`carousel__indicator-item${slideIndex === currentSlideIndex ? '--active' : ''}`]}`}
      />
    ))

  return (
    <div className={style['carousel__indicator']}>
      {renderSlidesIndicator()}
    </div>
  )
}

export default CarrouselIndicators
