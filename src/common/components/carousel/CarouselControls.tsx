import { ASSET } from '../../constants/Assets'
import style from '../../style/Carousel.module.scss'
import { CarouselControlsProps } from '../../types/CarouselControls.types'
import Icon from '../Icon'

/**
 * Renders the carousel controls for navigating to the previous and next slide
 */
const CarouselControls = ({
  previousSlide,
  nextSlide,
  hidePreviousControlArrow,
  hideNextControlArrow,
}: CarouselControlsProps) => {
  return (
    <article className={style[`carousel__control-container`]}>
      {!hidePreviousControlArrow && (
        <span onClick={previousSlide}>
          <Icon
            className={`${style['carousel__control']} ${style['carousel__control-left']}`}
            fileName={ASSET.icononboardinarrowforward}
          />
        </span>
      )}
      {!hideNextControlArrow && (
        <span onClick={nextSlide}>
          <Icon
            className={`${style['carousel__control']} ${style['carousel__control-right']}`}
            fileName={ASSET.icononboardinarrowforward}
          />
        </span>
      )}
    </article>
  )
}

export default CarouselControls
