import { Trans } from 'react-i18next'
import { UserDetails } from '../../features/authentication/types/AuthMachineTypes.type'

/**
 * Generates an info banner message by using user's age in `age-month` format.
 * Uses verified age if available
 */
const InfoBannerUserAge = ({ user_details }: { user_details: UserDetails }) => {
  if (user_details) {
    return (
      <Trans
        i18nKey={'INFO_BANNER_USER_AGE'}
        values={{
          yearsOld: user_details?.age.age,
          monthsOld: user_details?.age.month,
        }}
      />
    )
  }

  return null
}

export default InfoBannerUserAge
