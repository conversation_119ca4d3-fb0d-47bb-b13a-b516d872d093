import i18n, { languages } from '../../config/i18n'
import Dropdown from './Dropdown'

/**
 * Renders a language dropdown component
 */
const LanguageDropdown = ({ className }: { className?: string }) => {
  return (
    <Dropdown
      options={languages}
      value={{
        value: i18n.language,
        name: languages.find((lng) => lng.value === i18n.language)?.name,
        icon: languages.find((lng) => lng.value === i18n.language)?.icon,
        fullName: languages.find((lng) => lng.value === i18n.language)
          ?.fullName,
      }}
      onChange={(option) => {
        i18n.changeLanguage(option.value)
      }}
      itemKey={{
        displayKey: 'name',
      }}
      searchBy={['name']}
      className={className}
    />
  )
}

export default LanguageDropdown
