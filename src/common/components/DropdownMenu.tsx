import style from '../style/DropdownMenu.module.scss'
import { DropdownMenuProps } from '../types/DropdownMenu.types'

/**
 * Renders a dropdown menu
 */
const DropdownMenu = ({
  children,
  isOpen,
  className = '',
}: DropdownMenuProps) => {
  return (
    <main
      className={`${style['dropdown-menu']} ${className ?? ''} ${style[`dropdown-menu--${isOpen ? 'open' : ''}`]}`}
    >
      <section className={style[`dropdown-menu__inner`]}>
        {isOpen && children}
      </section>
    </main>
  )
}

export default DropdownMenu
