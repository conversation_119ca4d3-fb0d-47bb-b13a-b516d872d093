import { UI_TEST_ID } from '../constants/DataTestIDs'
import style from '../style/FilterBody.module.scss'
import { FilterBodyProps } from '../types/FilterBody.types'
import Icon from './Icon'

/**
 * Temporary card-like component only meant for the
 * filter component, this can replace the `<Card />` component
 * as a standard card component
 */
const FilterBody = ({
  filterIcon,
  mainText,
  resetFilterIcon,
  secondaryText,
  onClick,
  onClickReset,
  hideResetFilters,
}: FilterBodyProps) => {
  return (
    <article className={style['filter-body']}>
      <p className={style['filter-body__main-text']}>{mainText}</p>

      <div className={style['filter-body__sec-container']}>
        {hideResetFilters && (
          <div
            className={style['filter-body__reset-container']}
            onClick={onClickReset}
            data-testid={UI_TEST_ID.resetFilters}
          >
            <p className={style['filter-body__sec-text']}>{secondaryText}</p>
            <Icon
              className={style['filter-body__reset-icon']}
              fileName={resetFilterIcon}
            />
          </div>
        )}

        <Icon
          className={style['filter-body__filter-icon']}
          fileName={filterIcon}
          onClick={onClick}
          dataTestId={UI_TEST_ID.filterToggle}
        />
      </div>
    </article>
  )
}

export default FilterBody
