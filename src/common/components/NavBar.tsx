import { useEffect } from 'react'
import { NavigateFunction, useLocation } from 'react-router'
import AccountSummaryBody from '../../features/authentication/components/AccountSummaryBody'
import LogoutButton from '../../features/authentication/components/LogoutButtonAndConfirmModal'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import { AUTH_CONSTANTS } from '../../features/authentication/utils/consts'
import { useEmbeddedTon } from '../../features/dashboard/hooks/useEmbededTon'
import {
  ACCOUNT_MENU,
  DASHBOARD_NAVIGATION,
  PRIVATE,
  PUBLIC,
} from '../../routes/Route'
import { ASSET } from '../constants/Assets'
import { UI_TEST_ID } from '../constants/DataTestIDs'
import { useCustomNavigation } from '../hooks/useCustomNavigation'
import { useDeviceScreen } from '../hooks/useDeviceScreen'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/NavBar.module.scss'
import Button from './Button'
import Icon from './Icon'
import LanguageDropdown from './LanguageDropdown'
import MobileBarItems from './MobileBarItems'

/**
 * Top navigation bar displayed only on desktop. It render's user's
 * information, navigation items and a logout button if the user is logged in otherwise
 * a login button
 */
const NavBar = () => {
  const { isEmbedded, isOnPublicPage } = useEmbeddedTon()

  const t = useTranslate()
  const navigate = useCustomNavigation()
  const location = useLocation()
  const {
    isAuthenticated,
    context: { user_details },
  } = useAccountService()

  const navigateToSignIn = () => navigate(PUBLIC.SIGN_IN)
  const navigateToSignUp = () => navigate(PUBLIC.SIGN_UP)
  const navigateToHome = () =>
    navigate(
      isAuthenticated ? DASHBOARD_NAVIGATION.FUNDED_PROGRESS : PUBLIC.HOME
    )

  useDesktopRoutes({ navigate, location: location as unknown as Location })

  if (isEmbedded && !isOnPublicPage) {
    return null
  }

  return (
    <nav className={style.navBar}>
      <div className={style.navBar__container}>
        <Icon
          fileName={ASSET.myTontineWhiteR}
          className={style.navBar__icon}
          onClick={navigateToHome}
        />

        {isAuthenticated && (
          <MobileBarItems
            isAuthenticated={isAuthenticated}
            className={style['navBar__nav-items']}
            completedKyc={Boolean(user_details?.kyc_status?.L2?.passed_level)}
          />
        )}

        {isAuthenticated && (
          <article className={style['navBar__acc-container']}>
            <AccountSummaryBody className={style['navBar__acc-sum']} />
            <LogoutButton
              variant="logout"
              icon={ASSET.iconaccountmenuloouticon}
            />
          </article>
        )}

        {!isAuthenticated && (
          <>
            <LanguageDropdown className={style['navBar__language-dropdown']} />

            <article className={style['navBar__auth-section']}>
              <Button
                variant="login"
                onClick={navigateToSignIn}
                dataTestID={UI_TEST_ID.loginBtnDesktop}
                className={
                  style[
                    `navBar__login--${
                      location.pathname === PUBLIC.SIGN_IN ? 'hidden' : ''
                    }`
                  ]
                }
              >
                {t('BUTTON_LABEL.SIGN_IN')}
              </Button>
              <Button
                variant="signup-desktop"
                onClick={navigateToSignUp}
                dataTestID={UI_TEST_ID.signUpHomeBtn}
                className={
                  style[
                    `navBar__signup--${
                      location.pathname === PUBLIC.SIGN_UP ||
                      //Hides sign up button when in referral code redeem mode
                      location?.pathname?.includes(
                        AUTH_CONSTANTS.REFERRAL_CODE_PREFIX
                      )
                        ? 'hidden'
                        : ''
                    }`
                  ]
                }
              >
                {t('BUTTON_LABEL.SIGN_UP')}
              </Button>
            </article>
          </>
        )}
      </div>
    </nav>
  )
}

/**
 * Makes sure that on Desktop the root route and the sub route are highlighted
 * at the same time.
 */
const useDesktopRoutes = ({
  navigate,
  location,
}: {
  navigate: NavigateFunction
  location: Location
}) => {
  const { isMobileOrTablet } = useDeviceScreen()

  useEffect(() => {
    if (!isMobileOrTablet) {
      if (location.pathname === PRIVATE.ACCOUNT) {
        navigate(ACCOUNT_MENU.PERSONAL_DETAILS, { replace: true })
      }
      if (location.pathname === PRIVATE.MYTT_DASHBOARD) {
        navigate(DASHBOARD_NAVIGATION.FUNDED_PROGRESS, { replace: true })
      }
    }
  }, [location?.pathname, navigate, isMobileOrTablet])
}

export default NavBar
