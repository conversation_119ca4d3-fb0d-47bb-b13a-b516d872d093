import style from '../style/StaticToolTip.module.scss'
import { StaticToolTipProps } from '../types/StaticToolTip.types'

/**
 * Static tool tip component
 */
const StaticToolTip = ({
  text,
  rightTip,
  color,
  className,
  onClick,
}: StaticToolTipProps) => {
  return (
    <section
      className={`${className ?? `${style[`static-tooltip`]}`} 
      ${style[`static-tooltip${color ? `--${color}` : ''}`]}`}
      onClick={onClick}
    >
      <div
        className={`
        ${style[`static-tooltip__tip`]} 
        ${
          style[
            `static-tooltip__tip${rightTip ? '--right' : '--center'}${color ? `--${color}` : ''}`
          ]
        }
        `}
      >
        {text}
      </div>
    </section>
  )
}

export default StaticToolTip
