import dayjs from 'dayjs'
import { useState } from 'react'
import style from '../../features/banking/style/Filters.module.scss'
import { ASSET } from '../constants/Assets'
import { UI_TEST_ID } from '../constants/DataTestIDs'
import { useFilters } from '../hooks/useFilters'
import { FiltersProps } from '../types/Filter.types'
import { filterRangeTypes } from '../utils/consts'
import DateDropdown from './DateDropdown'
import Dropdown from './Dropdown'
import FilterBody from './FilterBody'

/**
 * Date filters, takes in an array and returns a filtered array, by `filterKey`,
 * the filter key can be nested, like `transaction.time`
 */
const Filters = <T extends Array<unknown>>({
  onFiltersApplied,
  onResetFilters,
  array,
  filterKey,
  defaultFromDate,
  defaultToDate,
  children,
  defaultRangeType,
  t,
}: FiltersProps<T>) => {
  const [showFilters, setShowFilters] = useState(false)
  const {
    fromDate,
    toDate,
    rangeOption,
    resetFilters,
    handleFromDate,
    handleRangeType,
    handleToDate,
  } = useFilters({
    defaultFromDate,
    defaultToDate,
    defaultRangeOption: {
      ...filterRangeTypes[4],
      type: defaultRangeType ?? 'customRange',
    },
    array,
    filterKey,
    onFiltersApplied,
    onResetFilters,
  })

  return (
    <section className={style.filters}>
      <FilterBody
        mainText={t('STATEMENTS_PAGE_FILTER')}
        filterIcon={ASSET.filter}
        resetFilterIcon={ASSET.resetFilter}
        secondaryText={t('RESET_FILTERS')}
        onClick={() => setShowFilters((prev) => !prev)}
        onClickReset={resetFilters}
        hideResetFilters={showFilters}
      />

      {showFilters && (
        <section
          className={style['filters__container']}
          data-testid={UI_TEST_ID.filterContainer}
        >
          <article
            className={style['filters__date-range']}
            data-testid={UI_TEST_ID.filtersDateRange}
          >
            <Dropdown
              label={t('FILTER_DATE_RANGE_LABEL')}
              options={filterRangeTypes.map((option) => ({
                ...option,
                label: t(option.label),
              }))}
              value={rangeOption ?? filterRangeTypes[4]}
              itemKey={{
                displayKey: 'label',
              }}
              searchBy={['label']}
              onChange={handleRangeType}
              className={style['filters__date-dropdown']}
              optional
              dataTestID={UI_TEST_ID.dateRangeFilter}
            />

            {children}
          </article>

          {rangeOption?.type === 'customRange' && (
            <article className={style['filters__date-range']}>
              <DateDropdown
                label={t('FROM_DATE_RANGE_INPUT')}
                mode="monthYear"
                monthFormatting="short"
                onChange={handleFromDate}
                value={fromDate?.dateStringISO}
                monthFrom={dayjs(defaultFromDate).month()}
                monthTo={dayjs(defaultToDate).month()}
                yearFrom={dayjs(defaultFromDate).year()}
                yearTo={dayjs(defaultToDate).year()}
              />
              <DateDropdown
                label={t('BANKING.PAYOUT_HISTORY_FILTER_TO')}
                mode="monthYear"
                monthFormatting="short"
                onChange={handleToDate}
                value={toDate?.dateStringISO}
                monthFrom={dayjs(defaultFromDate).month()}
                monthTo={dayjs(defaultToDate).month()}
                yearFrom={dayjs(defaultFromDate).year()}
                yearTo={dayjs(defaultToDate).year()}
              />
            </article>
          )}
        </section>
      )}
    </section>
  )
}

export default Filters
