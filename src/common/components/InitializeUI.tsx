import { ReactNode, useEffect, useLayoutEffect } from 'react'
import { useLocation } from 'react-router'
import { useLegalMachine } from '../../features/agreements/hooks/useLegalMachine'
import { allAgreements } from '../../features/agreements/utils/consts'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import { MagicLoginResponse } from '../../features/authentication/types/AuthMachineTypes.type'
import { getAuthToken } from '../../features/authentication/utils/AuthFunctions'
import { useBankingService } from '../../features/banking/hooks/useBankingService'
import { useLocalization } from '../hooks/useLocalization'
import { useTracking } from '../hooks/useTracking'
import { MAGIC_LOGIN_PARAM } from '../utils/consts'
import { parseMagicLink } from '../utils/UtilFunctions'
import SuspenseLoader from './SuspenseLoader'

/**
 * Checks if the UI should wait for the banking data or not
 */
const shouldInitWithBanking = ({
  invAccountStatus,
  bankingData,
}: {
  invAccountStatus: 'opened' | 'none' | 'pending' | 'rejected'
  bankingData?: object
}) => {
  if (invAccountStatus === 'opened') {
    return Boolean(bankingData)
  }

  // Skip waiting for banking info, if investment account is not opened
  return true
}

/**
 * Anything wrapped in this component will be rendered only when all data is
 * fetched in authenticated mode and anon mode only for agreements API call
 */
const InitializeUI = ({ children }: { children: ReactNode }) => {
  useLocalization()
  useTracking()
  const { isAuthenticated, context, currentState, states, send } =
    useAccountService()
  const { bankContext, sendBankEvent, currentBankState } = useBankingService()
  const { legalContext, sendLegalEvent } = useLegalMachine()
  const location = useLocation()
  const shouldCallInit =
    !isAuthenticated &&
    !getAuthToken() &&
    parseMagicLink(location?.pathname, MAGIC_LOGIN_PARAM)?.param !==
      MAGIC_LOGIN_PARAM

  /**
   * Checks if all necessary agreements are present for auth user
   */
  const isAllAgreementsHere =
    Object.keys(legalContext?.agreement ?? {}).length === allAgreements.length

  useLayoutEffect(() => {
    send({
      type: 'REFRESH_SESSION',
      payload: {
        successCallback: (data) => {
          const response = data as Exclude<MagicLoginResponse, 'forecastParams'>
          sendLegalEvent({
            type: 'FETCH_AGREEMENT',
            payload: {
              agreementTypes: allAgreements,
            },
          })
          sendBankEvent({
            type: 'GET_RETURNS',
            payload: {
              invAccOpen:
                response?.userAccountInfo?.investment_account_status ===
                'opened',
            },
          })
        },
      },
    })
  }, [])

  useEffect(() => {
    // only if not auth and not on /magic_login page
    if (shouldCallInit) {
      sendLegalEvent({
        type: 'FETCH_AGREEMENT',
        payload: {
          agreementTypes: ['TermsAndConditions'],
        },
      })
    }
  }, [sendLegalEvent, isAuthenticated, shouldCallInit])

  useEffect(() => {
    if (shouldCallInit) {
      sendBankEvent({
        type: 'GET_RETURNS',
      })
    }
  }, [sendBankEvent, isAuthenticated, shouldCallInit])

  // Only gets called in authenticated state
  if (
    isAuthenticated &&
    context?.user_details &&
    isAllAgreementsHere &&
    currentState !== 'REDEEMING_MAGIC_TOKEN' &&
    bankContext?.returns?.forecastRules &&
    shouldInitWithBanking({
      bankingData:
        bankContext?.returns?.forecastRules &&
        bankContext?.bankingInfo?.nominalBalance,
      invAccountStatus: context?.user_details?.investment_account_status,
    }) &&
    currentBankState !== 'FETCHING_BANK_INFO'
  ) {
    return children
  }

  // Necessary data when user is not authenticated
  if (
    legalContext?.agreement &&
    !isAuthenticated &&
    // Needed to show the user the loader instead of the screen flashing
    currentState !== states.REFRESHING_SESSION &&
    bankContext?.returns?.forecastRules
  ) {
    return children
  }

  // Checking machine state, if it is fetching or doing something is not a good
  // idea, because the app is in IDLE state which indicates finishing fetching
  // data so it can cause content flashing
  return <SuspenseLoader />
}

export default InitializeUI
