import {
  EmailIcon,
  EmailShareButton,
  FacebookIcon,
  FacebookShareButton,
  TelegramIcon,
  TelegramShareButton,
  TwitterShareButton,
  ViberIcon,
  ViberShareButton,
  WhatsappIcon,
  WhatsappShareButton,
  XIcon,
} from 'react-share'
import { track } from '../analytics/Analytics'
import { ReferralLinkEvent } from '../analytics/EventData'
import { ObjectIdProperty } from '../analytics/ObjectId'
import { ASSET } from '../constants/Assets'
import { useDeviceScreen } from '../hooks/useDeviceScreen'
import { SocialShareProps } from '../types/SocialShare.types'
import Icon from './Icon'
import SmsShare from './SmsShare'

/** Render social media icons when clicked share content */
const SocialShare = ({
  size,
  urlToShare,
  postTitle,
  postContent,
  facebookHashtag,
  hashTags,
  twitterAccountsToFollow,
  roundIcons,
}: SocialShareProps) => {
  const { isMobileOrTablet } = useDeviceScreen()
  const trackSocialShare = ({ objectId }: { objectId: ObjectIdProperty }) => {
    void track({
      event: ReferralLinkEvent.share,
      properties: {
        object_id: objectId,
        object_value: urlToShare,
      },
    })
  }

  return (
    <section
      style={{
        display: 'flex',
        gap: '10px',
        flexWrap: 'wrap',
        justifyContent: 'center',
      }}
    >
      <FacebookShareButton
        url={urlToShare}
        hashtag={`#${facebookHashtag}`}
        onClick={() => trackSocialShare({ objectId: 'facebook' })}
      >
        <FacebookIcon size={size} round={roundIcons} />
      </FacebookShareButton>

      <TwitterShareButton
        url={urlToShare}
        title={postTitle}
        hashtags={hashTags}
        related={twitterAccountsToFollow}
        onClick={() => trackSocialShare({ objectId: 'x' })}
      >
        <XIcon size={size} round={roundIcons} />
      </TwitterShareButton>

      <WhatsappShareButton
        url={urlToShare}
        title={postTitle}
        onClick={() => trackSocialShare({ objectId: 'whatsapp' })}
      >
        <WhatsappIcon size={size} round={roundIcons} />
      </WhatsappShareButton>

      {isMobileOrTablet && (
        <SmsShare
          contentToShare={urlToShare}
          onClick={() => trackSocialShare({ objectId: 'sms' })}
        >
          <div style={{ width: `${size}px`, height: `${size}px` }}>
            <Icon
              fileName={ASSET.smsRounded}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </SmsShare>
      )}

      <ViberShareButton
        url={urlToShare}
        title={postTitle}
        onClick={() => trackSocialShare({ objectId: 'viber' })}
      >
        <ViberIcon size={size} round={roundIcons} />
      </ViberShareButton>

      <TelegramShareButton
        url={urlToShare}
        title={postTitle}
        onClick={() => trackSocialShare({ objectId: 'telegram' })}
      >
        <TelegramIcon size={size} round={roundIcons} />
      </TelegramShareButton>

      <EmailShareButton
        url={urlToShare}
        subject={postTitle}
        body={postContent}
        onClick={() => trackSocialShare({ objectId: 'email' })}
      >
        <EmailIcon size={size} round={roundIcons} />
      </EmailShareButton>
    </section>
  )
}

export default SocialShare
