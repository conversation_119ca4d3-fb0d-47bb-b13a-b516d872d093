import { useState } from 'react'
import { Trans } from 'react-i18next'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import { ASSET } from '../constants/Assets'
import { useDeviceScreen } from '../hooks/useDeviceScreen'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/OpenInApp.module.scss'
import { useOpenApp, useOS } from '../utils/UtilFunctions'
import Button from './Button'
import Icon from './Icon'

/**
 * Component that prompts users to open the My Tontine app on mobile devices
 * for a better experience
 */
const OpenInApp = () => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()
  const { isAuthenticated } = useAccountService()
  const [visible, setVisible] = useState(true)
  const os = useOS()
  const handleOpenApp = useOpenApp(isMobileOrTablet)

  // Only show on Android devices
  if (!isMobileOrTablet || !visible || os !== 'android') return null

  return (
    <div className={style['open-in-app']}>
      {isAuthenticated && (
        <Icon
          fileName={ASSET.iconaccountcloetoatmeae}
          className={style['open-in-app__close-icon']}
          onClick={() => setVisible(false)}
        />
      )}
      <div className={style['open-in-app__wrapper']}>
        <img
          src={ASSET.tontineIconRounded}
          alt={t('APP_NAME')}
          className={style['open-in-app__logo']}
        />
        <div className={style['open-in-app__content']}>
          <h2 className={style['open-in-app__title']}>
            <Trans i18nKey={'OPEN_IN_APP.TITLE'} />
          </h2>
        </div>
        <Button
          onClick={handleOpenApp}
          className={style['open-in-app__open-button']}
          variant="back"
        >
          <span className={style['open-in-app__buttonLabel']}>
            {t('BUTTON_LABEL.OPEN')}
          </span>
        </Button>
      </div>
    </div>
  )
}

export default OpenInApp
