import { Trans } from 'react-i18next'
import style from '../../style/ConfirmationModal.module.scss'
import {
  ConfirmationModalBaseProps,
  ConfirmationModalHeaderProps,
} from '../../types/ConfirmationModal.types'
import Icon from '../Icon'
import LottieAnimation from '../LottieAnimation'

/**
 * Renders the header of a confirmation modal, displaying an icon or an animated
 * icon, and a translated title. If both `icon` and `animatedIcon` are provided,
 * only the animated icon will be displayed.
 */
const ConfirmationModalHeader = ({
  icon,
  animatedIcon,
  title,
  contentValues,
}: ConfirmationModalHeaderProps &
  Pick<ConfirmationModalBaseProps, 'contentValues'>) => (
  <>
    {icon && !animatedIcon && (
      <Icon fileName={icon} className={style.confirmationModal__icon} />
    )}
    {animatedIcon && (
      <LottieAnimation
        animationName={animatedIcon}
        autoplay
        loop
        className={style.confirmationModal__animation}
      />
    )}
    {title && (
      <p className={style.confirmationModal__title}>
        <Trans i18nKey={title} values={contentValues} />
      </p>
    )}
  </>
)

export default ConfirmationModalHeader
