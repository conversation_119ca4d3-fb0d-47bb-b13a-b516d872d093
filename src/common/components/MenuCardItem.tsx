import { useState } from 'react'
import { useAccountService } from '../../features/authentication/hooks/useAccountService'
import FaceScan from '../../features/authentication/pages/FaceScan'
import { isDisabled } from '../../features/DisabledLaunchFeatures'
import { ASSET } from '../constants/Assets'
import { useCustomNavigation } from '../hooks/useCustomNavigation'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/MenuCard.module.scss'
import { MenuCardItemProps } from '../types/MenuCardItem.types'
import { hasStrongAuth } from '../utils/UtilFunctions'
import Button from './Button'
import CommonCard from './card/CommonCard'
import ConfirmationModal from './confirmation-modal/ConfirmationModal'

/**
 * Renders a menu card item that serves as a link to a route on mobile devices,
 * displays alerts next to the menu item if the `alertCount` is greater than 0
 */
const MenuCardItem = ({
  mainText,
  alertCount,
  to,
  icon,
  writeProtected,
  warningModalTitle,
  warningModalContent,
  warningModalCancelButtonLabel,
  showArrow = true,
  disabled,
  dataTestID,
  cardVariant,
}: MenuCardItemProps) => {
  const {
    context: { permissions, user_details },
  } = useAccountService()

  const t = useTranslate()
  const navigate = useCustomNavigation()
  const [faceScan, setFaceScan] = useState(false)
  const [isOpen, setOpen] = useState(false)

  /**
   * @description Checks if the user has strong authentication type before they
   * can proceed making changes to their account. If they have strong
   * authentication, they will be navigated to the route, otherwise it will
   * display a waning modal prompting the user to enroll or re-authenticate to
   * acquire strong auth type
   */
  const checkAuth = () => {
    if (permissions === 'read' && writeProtected) {
      /**
       * Open a warning modal, prompting the user to do biometric authentication
       * (scan their face if they don't have strong authentication)
       */
      setOpen(true)
    }
  }

  const isNotStrongAuth =
    permissions && !hasStrongAuth(permissions) && writeProtected

  //If a user does not have strong auth token, then they are not navigated to
  //the write protected route, instead a warning modal renders prompting them to
  //do re-authentication to obtain a strong auth token, and we only return the
  //menu card body so the styling does not seem broken to the user
  return (
    <>
      {isNotStrongAuth && (
        <>
          <ConfirmationModal
            isOpen={isOpen}
            icon={ASSET.yellowShield}
            title={warningModalTitle ?? t('FUND_PENSION.WARNING_MODAL_TITLE')}
            content={
              warningModalContent ??
              t('ONBOARDING.MODAL_ERROR_LOGIN_MAGIC_LINK')
            }
          >
            <Button onClick={() => setFaceScan(true)} disabled={isDisabled}>
              {t('PROMPT_BUTTON.ENROLL_FACE')}
            </Button>
            <Button
              variant="alternative"
              onClick={() => {
                setFaceScan(false)
                setOpen(false)
              }}
            >
              {warningModalCancelButtonLabel ??
                t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
            </Button>
          </ConfirmationModal>
          {faceScan && (
            <FaceScan
              asModal
              // Face already enrolled, start re-auth if not start an enroll face scan
              scanType={user_details?.face_enrolled ? 'auth-scan' : 'match-id'}
              onClickExitScan={() => setFaceScan(false)}
              onSuccessfulScan={() => {
                // After successful scan navigate the user to the page they wanted
                // to see
                navigate(to ?? '')
              }}
            />
          )}
        </>
      )}

      <CommonCard
        className={style['menu-card__cardItem']}
        dataTestID={dataTestID}
        icon={icon}
        title={mainText ?? ''}
        href={isNotStrongAuth ? undefined : to}
        onClick={checkAuth}
        cardInfoProps={{
          showArrow,
          cardAlertProps: {
            alert: alertCount,
          },
        }}
        disabled={disabled || isDisabled}
        variant={cardVariant}
      />
    </>
  )
}

export default MenuCardItem
