import lottie, { RendererType } from 'lottie-web/build/player/lottie_light'
import { useEffect } from 'react'
import {
  generateUniqueId,
  selectDomElement,
} from '../../common/utils/UtilFunctions'
import { LottieAnimationProps } from '../types/LottieAnimation.types'

/**
 * Plays an lottie animation from a provided JSON file, using the
 * `lottie-react` library. The animation will loop by default, and will autoplay.
 */
const LottieAnimation = ({
  loop,
  autoplay,
  animationName,
  style,
  className,
  containerID = `lottie-container${generateUniqueId()}`,
}: LottieAnimationProps) => {
  useLottieAnimation({
    animationData: animationName,
    container: `#${containerID}`,
    loop,
    autoplay,
    renderer: 'svg',
  })

  return <div id={containerID} className={className} style={style} />
}

type LottieWebProps = {
  animationData?: object
  container: string
  renderer: RendererType
  loop?: boolean
  autoplay?: boolean
}

/**
 * Creates an instance of lottie player and loads an animation. The player
 * instance gets destroyed when the component unmounts
 */
const useLottieAnimation = ({
  animationData,
  container,
  renderer,
  loop,
  autoplay,
}: LottieWebProps): void => {
  useEffect(() => {
    const animationInstance = lottie.loadAnimation({
      // DOM element where the animation will be contained
      container: selectDomElement(container) as Element,
      renderer,
      animationData,
      loop,
      autoplay,
    })

    return () => {
      if (animationInstance) {
        animationInstance.destroy()
      }
    }
  }, [])
}

export default LottieAnimation
