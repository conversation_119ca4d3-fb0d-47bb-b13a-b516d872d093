import { Trans } from 'react-i18next'
import style from '../style/ErrorTextCoundown.module.scss'
import { ErrorTextCountdownProps } from '../types/ErrorTextCountdown.types'
import CountDownTimer from './CountDownTimer'

/**
 * Renders error text with the countdown component, for countdown error messages
 */
const ErrorTextCountdown = ({
  i18nKey,
  secondsToCountFrom,
  onCountDownFinished,
}: ErrorTextCountdownProps) => {
  return (
    <div className={style['error-text-countdown']}>
      <p>
        <Trans i18nKey={i18nKey} />
      </p>
      <CountDownTimer
        seconds={secondsToCountFrom}
        onCountdownFinished={onCountDownFinished}
      />
    </div>
  )
}

export default ErrorTextCountdown
