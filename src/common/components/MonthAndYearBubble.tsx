import style from '../style/MonthYearBubble.module.scss'
import { MonthAndYearBubbleProps } from '../types/MonthAndYearBubble.types'
import Divider from './Divider'

/**
 * MonthAndYearBubble component is used to display the month and
 * year value in a bubble format for the slider thumb, usually
 */
const MonthAndYearBubble = ({
  year,
  yearLabel,
  month,
  monthLabel,
}: MonthAndYearBubbleProps) => {
  return (
    <article className={style[`month-year-bubble`]}>
      <div className={style[`month-year-bubble__year-container`]}>
        <p className={style[`month-year-bubble__year-value`]}>{year}</p>
        <p className={style[`month-year-bubble__year-label`]}>{yearLabel}</p>
      </div>
      {month > 0 && (
        <>
          <Divider className={style[`month-year-bubble__divider`]} />
          <div className={style[`month-year-bubble__month-container`]}>
            <p className={style[`month-year-bubble__month-value`]}>{month}</p>
            <p className={style[`month-year-bubble__month-label`]}>
              {monthLabel}
            </p>
          </div>
        </>
      )}
    </article>
  )
}

export default MonthAndYearBubble
