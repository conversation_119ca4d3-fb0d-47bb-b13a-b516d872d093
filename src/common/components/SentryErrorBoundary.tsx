import { ErrorBoundary } from '@sentry/react'
import { SentryErrorBoundaryProps } from '../types/SentryErrorBoundary.types'
import GeneralError from './GeneralError'

/**
 * Catches an error if a component fails to render and displays a fallback UI.
 * In short words `try/catch` block but for UI elements using Sentry for error tracking
 */
const SentryErrorBoundary = ({
  children,
  fallbackErrorComponent,
  hideNavButton,
}: SentryErrorBoundaryProps) => {
  const fallbackComponent = fallbackErrorComponent ?? (
    <GeneralError hideNavButton={hideNavButton} />
  )

  return <ErrorBoundary fallback={fallbackComponent}>{children}</ErrorBoundary>
}

export default SentryErrorBoundary
