import countriesLocale from '../constants/countries-locales.json'
import { getCountrySvgIcon } from '../utils/UtilFunctions'
import Dropdown from './Dropdown'

/**
 * Uses `countries-locales.json` to render countries with flag icons. The value is
 * the alpha3 code of the country and label is the name of the country
 */
const CountryDropdown = ({
  value,
  onChange,
  readOnly,
  label,
  className,
  optional,
}: {
  value: (typeof countriesLocale)[number] | string
  onChange?: (value: string) => void
  readOnly?: boolean
  label?: string
  className?: string
  optional?: boolean
}) => {
  return (
    <Dropdown
      options={countriesLocale.map((country) => ({
        ...country,
        icon: getCountrySvgIcon(country.alpha2),
      }))}
      value={value as unknown}
      onChange={(value) => onChange?.(value as string)}
      itemKey={{ displayKey: 'name', valueOnChange: 'alpha3' }}
      searchBy={['name', 'alpha3', 'dial_code']}
      readOnly={readOnly}
      label={label}
      className={className}
      optional={optional}
      inputHeight="unset-h-w"
    />
  )
}

export default CountryDropdown
