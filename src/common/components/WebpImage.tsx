import { WebpImageProps } from '../types/WebpImage.types'
import Icon from './Icon'

/**
 * Used for displaying images in `webp` format, with a fallback
 * image in case the browser does not support `webp` format
 */

const WebpImage = ({
  webpName,
  alternativeImageName,
  alt,
  className,
}: WebpImageProps) => {
  return (
    <Icon
      fileName={webpName ?? alternativeImageName}
      fromWeb={webpName ?? alternativeImageName}
      className={className}
      alt={alt}
    />
  )
}

export default WebpImage
