import style from '../style/DividerHeader.module.scss'
import { DividerHeaderProps } from '../types/DividerHeader.types'
import Divider from './Divider'
import Header from './Header'

/**
 * Renders a header with a divider below  that stretches full width,
 * this component is only rendered on **Desktop**
 */
const DividerHeader = ({
  headerText,
  className,
  additionalText,
}: DividerHeaderProps) => {
  return (
    <div className={`${style[`dividerHeader`]} ${className ?? ''}`}>
      <section className={style['dividerHeader__inner']}>
        <Header title={headerText} className={className} />
        {additionalText && (
          <p className={style['dividerHeader__additional-txt']}>
            {additionalText}
          </p>
        )}
      </section>
      <Divider />
    </div>
  )
}

export default DividerHeader
