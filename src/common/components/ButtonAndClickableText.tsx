import { track } from '../analytics/Analytics'
import { ButtonEvent } from '../analytics/EventData'
import style from '../style/ButtonAndClickableText.module.scss'
import { ButtonAndClickableTextProps } from '../types/ButtonAndClickableTextProps.types'
import Button from './Button'

/**
 * Renders a button and a clickable text stacked on top oc
 * each other
 */
const ButtonAndClickableText = ({
  buttonOnClick,
  textOnClick,
  buttonLabel,
  textLabel,
  textDataTestID,
  buttonDataTestID,
  buttonTrackActivity,
  textTrackActivity,
  buttonVariant,
}: ButtonAndClickableTextProps) => {
  const onClickText = () => {
    textOnClick?.()

    if (textTrackActivity) {
      void track({
        event: ButtonEvent.clicked,
        properties: {
          object_id: textTrackActivity?.trackId,
          description: textTrackActivity?.eventDescription,
          label: textLabel,
        },
      })
    }
  }

  return (
    <section className={style['button-and-clickable-text']}>
      <Button
        variant={buttonVariant}
        onClick={buttonOnClick}
        className={style['button-and-clickable-text__button']}
        dataTestID={buttonDataTestID}
        trackActivity={buttonTrackActivity}
      >
        {buttonLabel}
      </Button>
      {textLabel && (
        <p
          onClick={onClickText}
          className={style['button-and-clickable-text__text']}
          data-testid={textDataTestID}
        >
          {textLabel}
        </p>
      )}
    </section>
  )
}

export default ButtonAndClickableText
