import { useTranslate } from '../hooks/useTranslate'

/**
 * Component that renders dynamic meta tags for SEO and social media sharing.
 * Uses the useTranslate hook to provide localized meta content.
 */
const DynamicMetaTags = () => {
  const t = useTranslate()

  // Get translated content
  const metaTitle = t('SEO.META_TITLE')
  const metaDescription = t('SEO.META_DESCRIPTION')
  const ogImage = t('SEO.META_IMAGE')
  const twitterImage = t('SEO.META_IMAGE_TWITTER')
  const siteUrl = window?.location?.origin ?? ''
  // Use ogImage as fallback for twitterImage if not provided
  const finalTwitterImage = twitterImage ?? ogImage

  return (
    <>
      {/* Basic meta tags */}
      <title>{metaTitle}</title>
      <meta name="title" content={metaTitle} />
      <meta name="description" content={metaDescription} />

      {/* Open Graph meta tags */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={siteUrl} />
      <meta property="og:title" content={metaTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:image" content={ogImage} />

      {/* Twitter Card meta tags */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={siteUrl} />
      <meta property="twitter:title" content={metaTitle} />
      <meta property="twitter:description" content={metaDescription} />
      <meta property="twitter:image" content={finalTwitterImage} />
    </>
  )
}

export default DynamicMetaTags
