import { ASSET } from '../constants/Assets'
import { useTranslate } from '../hooks/useTranslate'
import { SelectSexProps } from '../types/Select.types'
import SelectValue from './SelectValue'

/** * Renders buttons with Male and Female sex */
const SelectSex = ({ sex, setSex, trackActivities, label }: SelectSexProps) => {
  const t = useTranslate()
  return (
    <SelectValue
      label={label}
      value={sex}
      optional
      setValue={setSex}
      optionsToSelect={['Male', 'Female']}
      buttonLabels={[t('SEX_BUTTON.MALE_LABEL'), t('SEX_BUTTON.FEMALE_LABEL')]}
      buttonIcons={[ASSET.icononboardinmale, ASSET.icononboardinfemale]}
      activeButtonIcons={[
        ASSET.icononboardinmalewhite,
        ASSET.icononboardinfemalewhite,
      ]}
      trackActivities={trackActivities}
    />
  )
}

export default SelectSex
