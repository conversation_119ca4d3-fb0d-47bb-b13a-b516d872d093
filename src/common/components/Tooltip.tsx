import { useState } from 'react'
import style from '../style/Tooltip.module.scss'
import { TooltipProps } from '../types/Tooltip.types'

/** Tooltip wrapper that shows text when the user hover over the
 * wrapped component
 */
const Tooltip = ({ children, content, direction, className }: TooltipProps) => {
  const [active, setActive] = useState(false)

  const showTip = () => {
    setActive(true)
  }

  const hideTip = () => setActive(false)

  return (
    <div
      className={`${style.tooltip} ${className}`}
      onMouseEnter={showTip}
      onMouseLeave={hideTip}
    >
      {children}
      {active && (
        <div className={`${style[`tooltip__box`]} ${direction ?? style.top}`}>
          {content}
        </div>
      )}
    </div>
  )
}

export default Tooltip
