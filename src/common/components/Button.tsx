import { track } from '../analytics/Analytics'
import { ButtonEvent } from '../analytics/EventData'
import { ANIMATION } from '../constants/Animations'
import style from '../style/Button.module.scss'
import { ButtonProps } from '../types/Button.types'
import Icon from './Icon'
import LottieAnimation from './LottieAnimation'

/**
 * Generic button component that offers extended functionalities
 * like different button types, tracking and disabling the button when a promise
 * is pending and enabling the button when the promise has been resolved via
 * `loading` property
 */
const Button = ({
  onClick,
  disabled,
  children,
  className = '',
  icon,
  textOnLoading,
  loading,
  dataTestID,
  loadingAnimation,
  variant = 'primary',
  trackActivity,
  ...rest
}: ButtonProps) => {
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onClick?.(event)

    const buttonLabel = typeof children === 'string' ? children : ''
    if (trackActivity) {
      void track({
        event: ButtonEvent.clicked,
        properties: {
          object_id: trackActivity?.trackId,
          description: trackActivity?.eventDescription,
          label: buttonLabel,
          // See what to do with value, it comes from other elements
          object_value: trackActivity?.value,
        },
      })
    }
  }

  return (
    <button
      className={`${style[`button${loading ? '--loading' : `--${variant}`}`]} ${className}`}
      disabled={disabled}
      onClick={handleClick}
      data-testid={dataTestID}
      {...rest}
    >
      {loading ? (
        <LottieAnimation
          animationName={loadingAnimation ?? ANIMATION.loadingWhiteDots}
          className={style[`button__loading-animation`]}
          autoplay
          loop
        />
      ) : (
        <div className={style[`button__inner`]}>
          {!loading && icon && (
            <Icon fileName={icon} className={style['button__icon']} />
          )}
          <p className={style['button__text']}>
            {loading ? (textOnLoading ? textOnLoading : '') : children}
          </p>
        </div>
      )}
    </button>
  )
}

export default Button
