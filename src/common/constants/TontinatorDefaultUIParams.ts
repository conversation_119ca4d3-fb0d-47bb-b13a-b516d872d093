import { TontinatorUIParams } from '../types/SupportedCountries.types'

export const TontinatorDefaultUIParams: Record<string, TontinatorUIParams> = {
  JPN: {
    defaultRetirementAgeSlider: { age: 65, month: 1 },
    defaultCurrentAgeSlider: { age: 65, month: 0 },
    defaultOneTimeSliderValue: 1000000,
    defaultMonthlySliderValue: 100000,
    defaultSex: 'Male',
    oneTimeContribution: [
      0, 1000000, 2000000, 3000000, 4000000, 5000000, 6000000, 7000000, 8000000,
      9000000, 10000000, 15000000, 20000000, 30000000, 40000000, 50000000,
      60000000, 70000000, 80000000, 90000000, 100000000, 150000000, 200000000,
      250000000,
    ],
    monthlyContribution: [
      0, 1000, 5000, 10000, 25000, 50000, 75000, 100000, 150000, 300000, 500000,
      750000, 1000000,
    ],
    oneTimeContributionIfRetired: [
      1000000, 2000000, 3000000, 4000000, 5000000, 6000000, 7000000, 8000000,
      9000000, 10000000, 15000000, 20000000, 30000000, 40000000, 50000000,
      60000000, 70000000, 80000000, 90000000, 100000000, 150000000, 200000000,
      250000000,
    ],
    monthlyContributionMinIfOnly: 100000,
    oneTimeContributionMinIfOnly: 10000000,
  },
  USA: {
    defaultRetirementAgeSlider: { age: 65, month: 1 },
    defaultCurrentAgeSlider: { age: 65, month: 0 },
    defaultOneTimeSliderValue: 100000,
    defaultMonthlySliderValue: 0,
    defaultSex: 'Male',
    oneTimeContribution: [
      0, 10000, 20000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000,
      150000, 200000, 250000, 300000, 400000, 500000, 600000, 700000, 800000,
      900000, 1000000, 1500000, 2000000,
    ],
    monthlyContribution: [
      0, 5, 25, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500,
      2000, 2500, 3000, 4000, 5000, 7500, 10000,
    ],
    oneTimeContributionIfRetired: [
      10000, 20000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000,
      150000, 200000, 250000, 300000, 400000, 500000, 600000, 700000, 800000,
      900000, 1000000, 1500000, 2000000,
    ],
    monthlyContributionMinIfOnly: 1000,
    oneTimeContributionMinIfOnly: 100000,
  },
  ROW: {
    defaultRetirementAgeSlider: { age: 65, month: 1 },
    defaultCurrentAgeSlider: { age: 65, month: 0 },
    defaultOneTimeSliderValue: 100000,
    defaultMonthlySliderValue: 0,
    defaultSex: 'Male',
    oneTimeContribution: [
      0, 10000, 20000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000,
      150000, 200000, 250000, 300000, 400000, 500000, 600000, 700000, 800000,
      900000, 1000000, 1500000, 2000000,
    ],
    monthlyContribution: [
      0, 5, 25, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500,
      2000, 2500, 3000, 4000, 5000, 7500, 10000,
    ],
    oneTimeContributionIfRetired: [
      10000, 20000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000,
      150000, 200000, 250000, 300000, 400000, 500000, 600000, 700000, 800000,
      900000, 1000000, 1500000, 2000000,
    ],
    monthlyContributionMinIfOnly: 1000,
    oneTimeContributionMinIfOnly: 100000,
  },
}
