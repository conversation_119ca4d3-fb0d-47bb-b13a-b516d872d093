// Do NOT add trailing slashes here to any of the domains!
const origins = [
  'http://localhost:3000',
  'http://localhost:8080',
  'https://staging-tontine-com.netlify.app',
  'https://tontine.com',
  'https://tontineira.com',
  'https://ira.tontine.com',
] as const

/**
 * List of allowed origins from tontine website with trailing and without trailing slashes
 */
const allowedOrigins = [
  ...origins,
  ...origins.map((origin) => `${origin}/`),
] as unknown as typeof origins

export { allowedOrigins }
