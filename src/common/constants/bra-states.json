[{"id": 1, "name": "Acre", "iso_code": "BR-AC", "zip_ranges": [{"min": 69900000, "max": 69999999}]}, {"id": 2, "name": "Alagoas", "iso_code": "BR-AL", "zip_ranges": [{"min": 57000000, "max": 57999999}]}, {"id": 3, "name": "Amapá", "iso_code": "BR-AP", "zip_ranges": [{"min": 68900000, "max": 68999999}]}, {"id": 4, "name": "Amazonas", "iso_code": "BR-AM", "zip_ranges": [{"min": 69000000, "max": 69299999}, {"min": 69400000, "max": 69899999}]}, {"id": 5, "name": "Bahia", "iso_code": "BR-BA", "zip_ranges": [{"min": 40000000, "max": 48999999}]}, {"id": 6, "name": "Ceará", "iso_code": "BR-CE", "zip_ranges": [{"min": 60000000, "max": 63999999}]}, {"id": 7, "name": "Distrito Federal", "iso_code": "BR-DF", "zip_ranges": [{"min": 70000000, "max": 73699999}]}, {"id": 8, "name": "Espírito Santo", "iso_code": "BR-ES", "zip_ranges": [{"min": 29000000, "max": 29999999}]}, {"id": 9, "name": "Goiás", "iso_code": "BR-GO", "zip_ranges": [{"min": 72800000, "max": 76799999}]}, {"id": 10, "name": "Maranhão", "iso_code": "BR-MA", "zip_ranges": [{"min": 65000000, "max": 65999999}]}, {"id": 11, "name": "<PERSON><PERSON>", "iso_code": "BR-MT", "zip_ranges": [{"min": 78000000, "max": 78899999}]}, {"id": 12, "name": "Mato Grosso do Sul", "iso_code": "BR-MS", "zip_ranges": [{"min": 79000000, "max": 79999999}]}, {"id": 13, "name": "Minas Gerais", "iso_code": "BR-MG", "zip_ranges": [{"min": 30000000, "max": 39999999}]}, {"id": 14, "name": "Pará", "iso_code": "BR-PA", "zip_ranges": [{"min": 66000000, "max": 68899999}]}, {"id": 15, "name": "Paraíba", "iso_code": "BR-PB", "zip_ranges": [{"min": 58000000, "max": 58999999}]}, {"id": 16, "name": "Paraná", "iso_code": "BR-PR", "zip_ranges": [{"min": 80000000, "max": 87999999}]}, {"id": 17, "name": "Pernambuco", "iso_code": "BR-PE", "zip_ranges": [{"min": 50000000, "max": 56999999}]}, {"id": 18, "name": "Piauí", "iso_code": "BR-PI", "zip_ranges": [{"min": 64000000, "max": 64999999}]}, {"id": 19, "name": "Rio de Janeiro", "iso_code": "BR-RJ", "zip_ranges": [{"min": 20000000, "max": 28999999}]}, {"id": 20, "name": "Rio Grande do Norte", "iso_code": "BR-RN", "zip_ranges": [{"min": 59000000, "max": 59999999}]}, {"id": 21, "name": "Rio Grande do Sul", "iso_code": "BR-RS", "zip_ranges": [{"min": 90000000, "max": 99999999}]}, {"id": 22, "name": "Rondônia", "iso_code": "BR-RO", "zip_ranges": [{"min": 76800000, "max": 76999999}]}, {"id": 23, "name": "Roraima", "iso_code": "BR-RR", "zip_ranges": [{"min": 69300000, "max": 69399999}]}, {"id": 24, "name": "Santa Catarina", "iso_code": "BR-SC", "zip_ranges": [{"min": 88000000, "max": 89999999}]}, {"id": 25, "name": "São Paulo", "iso_code": "BR-SP", "zip_ranges": [{"min": 1000000, "max": 19999999}]}, {"id": 26, "name": "<PERSON><PERSON><PERSON>", "iso_code": "BR-SE", "zip_ranges": [{"min": 49000000, "max": 49999999}]}, {"id": 27, "name": "Tocantins", "iso_code": "BR-TO", "zip_ranges": [{"min": 77000000, "max": 77999999}]}]