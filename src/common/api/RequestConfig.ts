import { Milliseconds } from '../types/CommonTypes.types'
import { RequestConfig, RequestConfigParams } from './API.types'

/**
 * Default request settings
 */
const defaultErrorParams = {
  timeout: 900_000 as Milliseconds,
  timeoutError: 'Request timed out, server could not be contacted',
} as const

/**
 * Returns a custom header that can be read from the backend services.
 * Client side API requests are authorized with this header.
 * Request settings can be found here like timing out requests.
 *
 * The header includes:
 *
 * - Authorization via `X-Auth-Token`, which is added automatically if the user
 *   is authenticated. Also can be passed in as well with certain configurations
 * - Client side pin authorization if one provided, the `User-Pin` header is a
 *   added to the request header as well to authorize the request
 * - Request cancellation with `AbortSignal` from `AbortController`
 */
const axiosConfig = (
  params: RequestConfigParams = defaultErrorParams
): RequestConfig => {
  const {
    rawBodyRequest,
    signal,
    userPin,
    timeout,
    timeoutErrorMessage,
    withCredentials,
    emailToken,
    authToken,
    contentType,
  } = params

  const headers: Record<string, string> = {}

  if (emailToken) headers['X-Email-Token'] = emailToken
  if (contentType) headers['Content-Type'] = contentType
  if (rawBodyRequest && !contentType)
    headers['Content-Type'] = 'application/json'
  if (userPin) headers['User-Pin'] = userPin
  if (authToken) headers['X-Auth-Token'] = authToken

  return {
    signal,
    timeout,
    timeoutErrorMessage,
    withCredentials,
    ...(Object.keys(headers).length > 0 && { headers }),
  }
}

export { axiosConfig }
