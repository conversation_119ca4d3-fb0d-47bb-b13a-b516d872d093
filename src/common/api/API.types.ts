type RequestConfigParams = {
  signal?: AbortSignal
  userPin?: string
  rawBodyRequest?: boolean
  timeout?: number
  timeoutErrorMessage?: string
  withCredentials?: boolean
  emailToken?: string
  authToken?: string
  contentType?: string
}

type RequestConfig = {
  headers?: {
    'X-Auth-Token'?: string
    'User-Pin'?: string
    'Content-Type'?: string
    'X-Email-Token'?: string
  }
  signal?: AbortSignal
  timeout?: number
  timeoutErrorMessage?: string
  withCredentials?: boolean
}

export type { RequestConfig, RequestConfigParams }
