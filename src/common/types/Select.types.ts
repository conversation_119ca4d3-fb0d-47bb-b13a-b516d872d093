import { ButtonVariant } from './Button.types'
import { SexType, TrackActivity, ValidationData } from './CommonTypes.types'

type SelectValueProps<T> = {
  value: T
  setValue: (value: T) => void
  buttonLabels: Array<string>
  optionsToSelect: Array<T>
  activeButtonIcons?: Array<string>
  buttonIcons?: Array<string>
  label?: string
  className?: string
  tooltipText?: string
  showTooltip?: boolean
  labelInfoIcon?: boolean
  errorMessage?: ValidationData
  validatorFunction?: (value: T) => void
  debounceTime?: number
  optional?: boolean
  readonly?: boolean
  trackActivities?: Array<TrackActivity>
  variant?: ButtonVariant
}

type SelectTargetProps = {
  target: 'deposit' | 'target'
  setTarget: (target: 'deposit' | 'target') => void
  /**
   * Index 0 for the array is deposit
   */
  trackActivities?: Array<TrackActivity>
}

type SelectSexProps = {
  sex: SexType
  setSex: (sex: SexType) => void
  /**
   * Index 0 for the array is MALE
   */
  trackActivities?: Array<TrackActivity>
  label?: string
}

export type { SelectValueProps, SelectTargetProps, SelectSexProps }
