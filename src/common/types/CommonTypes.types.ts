import { AriaAttributes, HTMLAttributes } from 'react'
import { UseTranslationResponse } from 'react-i18next'
import { AgeMonth, ErrorStorage } from '../../features/CommonState.type'
import { PensionPlan } from '../analytics/EventData'
import { ObjectIdProperty } from '../analytics/ObjectId'
import { API_ERROR } from '../constants/ApiErrors'

type InvestmentDetail = {
  dayOfValidity: string
  rate: number
  source: 'TT-UAS'
  riskLevel: 'LVL1' | 'LVL2' | 'LVL3' | 'LVL4' | 'LVL5'
  name: string
}

type InvestmentStrategyId = 'BOL' | 'FII' | 'VBI' | 'BTC' | 'XAU'

/**
 * Required params in order to make an income forecast
 */
interface IncomeForecastParams {
  contributionAge: AgeMonth
  retirementAge?: AgeMonth
  countryOfResidence: string
  sex: SexType
  monthlyContribution: number
  oneTimeContribution: number
  writeDraftPlan?: boolean
  strategy: InvestmentStrategyId
  /**
   * This is not needed for the backend request yet!!
   */
  paramsMode?: TontinatorParamsMode
}

interface UnparsedForecastParams extends IncomeForecastParams {
  payoutAge?: AgeMonth
  isAuthenticated?: boolean
}

/**
 * Contribution forecast params
 */
type ContributionParams = {
  monthly_amount: number
  onetime_amount: number
  payout_age: AgeMonth
}

interface DemographicDataAnonUser {
  demographic_data_country_of_residence: string
  demographic_data_sex: SexType
  demographic_data_current_age: AgeMonth
}

/**
 * Forecast params sent to the Tontiantor API
 */
interface IncomeForecastRequestBody extends Partial<DemographicDataAnonUser> {
  contribution_allocations: InvestmentStrategyId
  contributions: ContributionParams
}

type ForecastAges = {
  retirementAge: AgeMonth
  contributionAge: AgeMonth
}

type ValidationData = {
  valid: boolean
  message?: string
  i18nKey?: string
  values?: object
}

type StateCodesAlpha2 =
  | 'US-AL'
  | 'US-AK'
  | 'US-AZ'
  | 'US-AR'
  | 'US-CA'
  | 'US-CO'
  | 'US-CT'
  | 'US-DE'
  | 'US-DC'
  | 'US-FL'
  | 'US-GA'
  | 'US-HI'
  | 'US-ID'
  | 'US-IL'
  | 'US-IN'
  | 'US-IA'
  | 'US-KS'
  | 'US-KY'
  | 'US-LA'
  | 'US-ME'
  | 'US-MD'
  | 'US-MA'
  | 'US-MI'
  | 'US-MN'
  | 'US-MS'
  | 'US-MO'
  | 'US-MT'
  | 'US-NE'
  | 'US-NV'
  | 'US-NH'
  | 'US-NJ'
  | 'US-NM'
  | 'US-NY'
  | 'US-NC'
  | 'US-ND'
  | 'US-OH'
  | 'US-OK'
  | 'US-OR'
  | 'US-PA'
  | 'US-RI'
  | 'US-SC'
  | 'US-SD'
  | 'US-TN'
  | 'US-TX'
  | 'US-UT'
  | 'US-VT'
  | 'US-VA'
  | 'US-WA'
  | 'US-WV'
  | 'US-WI'
  | 'US-WY'

type ZipRange = {
  min: number
  max: number
}

type StateData = {
  id: number
  name: string
  iso_code: string
  zip_ranges: Array<ZipRange>
}

type Milliseconds = number
type SexType = 'Male' | 'Female'
type RaErrorId = keyof typeof API_ERROR

/**
 * Error response object from the robo-actuary backend
 */
type RaApiError = {
  id: RaErrorId
  message: string
  /**
   * Error can be nested with the same properties, because of different services
   * This makes no sense... hopefully backend will sort this out
   */
  nested: RaApiError
  // Generic object can be anything from the backend
  data: object
}

type TranslatedError = {
  id: RaErrorId
  translatedError: string
  apiErrorMessage: string
  data: object
}

type InvestmentStrategy = {
  id: InvestmentStrategyId
  icon: string
  secondaryIcon: string
} & InvestmentDetail

type TontinatorParamsMode = 'IRA' | 'TTF'

/**
 * Used for tracking button click events for user behavior analysis
 */
type TrackActivity = {
  trackId?: ObjectIdProperty
  value?: unknown
  eventDescription?: string
}

type AccessibilitySettings = {
  fontIncreaseBy: number
}

type SmsVerificationProps = {
  onPhoneNumberVerified: (unverifiedPhoneNumber: string) => void
  onFailedPhoneVerification: (error?: ErrorStorage) => void
  unverifiedPhoneNumber?: string
}

type ReducedParams = Omit<
  IncomeForecastParams,
  'writeDraftPlan' | 'planID' | 'pastContributions'
>

type TFunction = UseTranslationResponse<'en', 'default'>['t']

type BoxProps = Omit<HTMLAttributes<HTMLElement>, 'onClick'> &
  AriaAttributes & { onClick?: () => void }
type TitleProps = HTMLAttributes<HTMLHeadingElement> & AriaAttributes

export type {
  AccessibilitySettings,
  BoxProps,
  ForecastAges,
  IncomeForecastParams,
  IncomeForecastRequestBody,
  InvestmentDetail,
  InvestmentStrategy,
  InvestmentStrategyId,
  Milliseconds,
  PensionPlan,
  RaApiError,
  ReducedParams,
  SexType,
  SmsVerificationProps,
  StateCodesAlpha2,
  StateData,
  TFunction,
  TitleProps,
  TontinatorParamsMode,
  TrackActivity,
  TranslatedError,
  UnparsedForecastParams,
  ValidationData,
}
