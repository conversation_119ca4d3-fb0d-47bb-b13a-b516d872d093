import { RefObject } from 'react'

type UseIntersectObserverParams = {
  onIntersect: (isIntersecting: boolean) => void
  root?: Element | Document | null
  rootMargin?: string
  threshold?: number | number[]
  elementToAttachObserverTo?: Element | RefObject<Element> | null
}

type UseIntersectObserverReturn = {
  setElement: (element: Element | RefObject<Element> | null) => void
}

export type { UseIntersectObserverParams, UseIntersectObserverReturn }
