import { ReactNode } from 'react'
import { TrackSliderButtonWithId } from '../analytics/Analytics.types'
import { SliderColorVariants } from './Range.types'

type SliderBoxProps = {
  formatter?: (value: number) => string
  prefix?: string
  onChange: (value: number) => void
  steps: Array<number>
  title?: string
  value: number
  disabled?: boolean
  children?: ReactNode
  disabledIncrement?: boolean
  disabledDecrement?: boolean
  boxValueDataTestID?: string
  incrementButtonDataTestID?: string
  decrementButtonDataTestID?: string
  sliderTestID?: string
  variant?: SliderColorVariants
  disabledSliderTooltipText?: string
  trackSlider?: TrackSliderButtonWithId
}

export type { SliderBoxProps }
