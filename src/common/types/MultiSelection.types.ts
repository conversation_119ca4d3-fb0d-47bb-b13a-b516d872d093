type SelectionItem = {
  id?: string | number
  title?: string
  desc?: string
  [key: string]: unknown
}

type SelectionProps = {
  title?: string
  desc?: string
  activeClassName?: string
  onSelectionClick?: (data: React.MouseEvent<HTMLDivElement>) => void
}

type MultiSelectionProps = {
  multiSelectionCardLabel?: string
  onSelection: (item: SelectionItem) => void
  setMultiSelectionData: (data: { activeSelection: SelectionItem }) => void
  multiSelectionData: {
    data?: Array<SelectionItem>
    activeSelection?: SelectionItem
  }
  allowMultipleItems?: boolean
}

export type { MultiSelectionProps, SelectionItem, SelectionProps }
