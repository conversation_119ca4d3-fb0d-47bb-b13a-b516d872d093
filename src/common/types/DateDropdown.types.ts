import { InputHTMLAttributes } from 'react'
import { MonthFormatting } from './UtilFunctions.types'

interface DateDropdownProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  /**
   * Date in YYYY-MM-DD format
   */
  value: string
  onChange: (value: string) => void
  mode?: 'monthYear'
  yearFrom?: number
  yearTo?: number
  monthFrom?: number
  monthTo?: number
  dayFrom?: number
  dayTo?: number
  format?: string
  locale?: string
  monthFormatting?: MonthFormatting
  label?: string
  yearLabel?: string
  readonly?: boolean
}

export type { DateDropdownProps }
