import { ReactNode } from 'react'
import { ModalProps } from './Modal.types'

type ConfirmationModalBaseProps = Partial<ModalProps> & {
  contentValues?: Record<string, unknown>
  children?: ReactNode
  dataTestID?: string
}

type ConfirmationModalHeaderProps = {
  icon?: string
  animatedIcon?: object
  title?: string
}

type ConfirmationModalContentProps = {
  content?: string
}

type ConfirmationModalProps = ConfirmationModalBaseProps &
  ConfirmationModalHeaderProps &
  ConfirmationModalContentProps

export type {
  ConfirmationModalProps,
  ConfirmationModalBaseProps,
  ConfirmationModalHeaderProps,
  ConfirmationModalContentProps,
}
