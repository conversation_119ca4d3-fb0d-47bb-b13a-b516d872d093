import { ReactNode } from 'react'
import { ButtonVariant } from './Button.types'
import { TrackActivity } from './CommonTypes.types'

type NavigationButtonsProps = {
  disabledFirst?: boolean
  disabledSecond?: boolean
  onClickFirst?: () => void
  onClickSecond?: () => void
  secondButtonType?: ButtonVariant
  className?: string
  secondButtonLoading?: boolean
  textOnLoading?: string
  secondButtonLabel?: string
  firstButtonLabel?: string
  hideBackButton?: boolean
  hideActionButton?: boolean
  customButton?: ReactNode
  infrontCustomButton?: ReactNode
  dataTestIDFirstBtn?: string
  dataTestIDSecondBtn?: string
  backButtonWhite?: boolean
  disabledFixedOnMobile?: boolean
  showOnMobile?: boolean
  trackActivityBackButton?: TrackActivity
  trackActivityActionButton?: TrackActivity
}

export type { NavigationButtonsProps }
