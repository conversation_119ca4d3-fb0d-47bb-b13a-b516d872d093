import { BoxProps, CardVariants, CardVariantType } from './Card.types'

type CardAlertType =
  | number
  | 'completed'
  | 'warn'
  | 'error'
  | 'pending'
  | 'not_reviewed'
  | 'rejected'
  | 'approved'
  | 'cancelled'

type CardAlertProps = {
  alert?: CardAlertType
  className?: string
}

type CardArrowAndAlertProps = {
  alert?: CardAlertType
  showArrow?: boolean
  alertAndArrowPosition?: 'start' | 'end'
  rotateArrow?: 'down' | 'up'
  arrowInvisible?: boolean
}

type CardArrowProps = {
  variant?: CardVariantType
  rotateArrow?: CardArrowAndAlertProps['rotateArrow']
  arrowInvisible?: CardArrowAndAlertProps['arrowInvisible']
  secondaryIcon?: string
  className?: string
}

type CardInfoProps = {
  showArrow?: boolean
  cardArrowProps?: CardArrowProps
  cardAlertProps?: CardAlertProps
} & BoxProps &
  CardVariants

export type {
  CardAlertProps,
  CardAlertType,
  CardArrowAndAlertProps,
  CardArrowProps,
  CardInfoProps,
}
