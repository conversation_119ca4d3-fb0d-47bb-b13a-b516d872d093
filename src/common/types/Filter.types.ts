import { ReactNode } from 'react'
import { TFunction } from './CommonTypes.types'

type FilterKey = {
  range: string
}

type RangeOption = {
  label: string
  type: RangeType
}

type FilterRangeTypes = Array<RangeOption>

interface FiltersProps<T> {
  onFiltersApplied: (filteredArray: T) => void
  onResetFilters?: () => void
  array: T
  filterKey: FilterKey
  children?: ReactNode
  defaultFromDate?: string
  defaultToDate?: string
  defaultRangeType?: RangeType
  t: TFunction
}

type RangeType =
  | 'today'
  | 'yesterday'
  | 'last7Days'
  | 'last30Days'
  | 'customRange'

interface DefaultParamsUseFilters<T> extends Omit<FiltersProps<T>, 't'> {
  onResetFilters?: () => void
  defaultRangeOption?: RangeOption
}

export type {
  DefaultParamsUseFilters,
  FilterRangeTypes,
  FiltersProps,
  RangeOption,
  RangeType,
}
