type DateSliderBoxProps = {
  onClickIncrementMonths?: () => void
  onClickDecrementMonths?: () => void
  onClickIncrementYears?: () => void
  onClickDecrementYears?: () => void
  children?: React.ReactNode
  month: number
  year: number
  monthLabel?: string
  yearLabel?: string
  locale?: string
  disabledIncrementMonth?: boolean
  disabledDecrementMonth?: boolean
  disabledDecrementYear?: boolean
  disabledIncrementYear?: boolean
}

export type { DateSliderBoxProps }
