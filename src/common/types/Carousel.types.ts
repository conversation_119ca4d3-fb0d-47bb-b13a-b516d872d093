import { ReactNode } from 'react'

type CarouselSlide = {
  imageWebp?: string
  imagePng?: string
  title?: string | ReactNode
  content?: string | ReactNode
  animation?: object | null
}

type CarouselProps = {
  slides: Array<CarouselSlide>
  startSlide?: number
  autoSlide?: boolean
  slideChangeIntervalMilliseconds?: number
  hideControls?: boolean
  hideIndicators?: boolean
}

export type { CarouselProps, CarouselSlide }
