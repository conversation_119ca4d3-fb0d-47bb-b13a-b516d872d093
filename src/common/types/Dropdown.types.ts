import { ValidationData } from './CommonTypes.types'

type OptionValue = {
  [key: string]: string
}

type ItemKey = {
  displayKey: string
  valueOnChange?: string
}

type DropdownProps<T> = {
  options: Array<T>
  itemKey: ItemKey
  // FIXME: Value cannot be T because item can can return
  // a value of the item instead of the whole item itself
  // this causes type issues in wrapped components
  value: T
  onChange: (value: T) => void
  searchBy: Array<string>
  label?: string
  readOnly?: boolean
  className?: string
  inputHeight?: string
  errorMessage?: ValidationData
  validatorFunction?: () => void
  placeholder?: string
  optional?: boolean
  alternateLabel?: boolean
  dataTestID?: string
  onSearchQueryTyped?: (value: string) => void
  restrictionRegex?: RegExp
  defaultOpen?: boolean
}

export type { DropdownProps, ItemKey, OptionValue }
