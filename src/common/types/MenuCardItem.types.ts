import { To } from 'react-router'
import { TestID } from '../../../cypress/support/ui-component-ids'
import { CardVariantType } from './Card.types'

type MenuCardItemProps = {
  mainText?: string
  alertCount?: number
  to?: To
  icon?: string
  writeProtected?: boolean
  warningModalTitle?: string
  warningModalContent?: string
  warningModalCancelButtonLabel?: string
  showArrow?: boolean
  disabled?: boolean
  dataTestID?: TestID
  cardVariant?: CardVariantType
}

export type { MenuCardItemProps }
