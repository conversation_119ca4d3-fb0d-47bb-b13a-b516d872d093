import { InvestmentStrategyId } from './CommonTypes.types'

type IncomeStatsProps = {
  contributionLabel?: string
  contributionAmount: number
  incomeStartAge?: number
  incomeLabel?: string
  incomeAmount: number
  incomePercentage: number
  currency: string
  variant?: 'grey' | 'yellow-faint' | 'blue-faint'
  isLoading?: boolean
}

type IncomeStatsExtendedProps = {
  investmentStrategy: InvestmentStrategyId
} & IncomeStatsProps

export type { IncomeStatsExtendedProps, IncomeStatsProps }
