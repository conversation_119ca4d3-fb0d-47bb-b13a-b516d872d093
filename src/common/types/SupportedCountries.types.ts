import { Age<PERSON><PERSON>h, YearMonth } from '../../features/CommonState.type'
import { SexType } from './CommonTypes.types'

interface BackendParamRules {
  minRetirementAge: AgeMonth
  minRetirementAgeYearMonth: YearMonth
  maxRetirementAge: AgeMonth
  maxRetirementAgeYearMonth: YearMonth
  minCurrentAge: AgeMonth
  minCurrentAgeYearMonth: YearMonth
  maxCurrentAge: AgeMonth
  maxCurrentAgeYearMonth: YearMonth
}

interface TontinatorUIParams extends Partial<BackendParamRules> {
  defaultRetirementAgeSlider: AgeMonth
  defaultCurrentAgeSlider: AgeMonth
  defaultOneTimeSliderValue: number
  defaultMonthlySliderValue: number
  defaultSex: SexType
  oneTimeContribution: Array<number>
  monthlyContribution: Array<number>
  oneTimeContributionIfRetired: Array<number>
  monthlyContributionMinIfOnly: number
  oneTimeContributionMinIfOnly: number
}

export type { BackendParamRules, TontinatorUIParams }
