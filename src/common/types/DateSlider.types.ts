import { UserDetails } from '../../features/authentication/types/AuthMachineTypes.type'

type DateSliderProps = {
  sliderSteps: Array<number>
  yearHeadLabel?: string
  monthHeadLabel?: string
  value?: {
    year?: number
    month?: number
    yearsOld?: number
    monthsOld?: number
    retirementAge?: {
      age?: number
      month?: number
    }
  }
  setValue: (value: DateSliderProps['value']) => void
  locale?: string
  yearsOldOnRetirementDateLabel?: string
  yearsOldOnRetirementDate?: number
  monthsOldOnRetirementDateLabel?: string
  monthsOldOnRetirementDate?: number
  label?: string
  caption?: string
  userDetails: Partial<UserDetails>
  ageThresholds: {
    maxRetirementAge: {
      age: number
      month: number
    }
    maxRetirementAgeYearMonth: {
      year: number
      month: number
    }
    minRetirementAge: {
      age: number
      month: number
    }
    minRetirementAgeYearMonth: {
      year: number
      month: number
    }
  }
}

export type { DateSliderProps }
