import { To } from 'react-router'
import { TestID } from '../../../cypress/support/ui-component-ids'
import { BoxProps, TitleProps } from './CommonTypes.types'
import { IconProps } from './Icon.types'

export const cardVariants = {
  grayDirty: 'gray-dirty',
  blueFaint: 'blue-faint',
  stat: 'stat',
} as const

type CardVariantType = (typeof cardVariants)[keyof typeof cardVariants]

type CardVariants = { variant?: CardVariantType }
type IconSizes = 'small' | 'large' | 'x-large'
type RoundnessSizes = 'off' | 'rounded-sm' | 'rounded' | 'rounded-l'

type CardProps = {
  dataTestID?: TestID
  href?: string | To
  disabled?: boolean
  active?: boolean
  rounded?: RoundnessSizes
} & BoxProps &
  CardVariants

type SharedCardProps = {
  className?: string
  title?: string
  subtitle?: string
  icon?: string
  iconSize?: IconSizes
} & CardProps

type CardHeaderProps = {
  iconProps?: Omit<IconProps, 'fileName'> & {
    iconSize?: IconSizes
  }
  icon?: string
} & BoxProps &
  CardVariants

type CardContentProps = {
  title?: string
  subtitle?: string
  titleProps?: TitleProps
  subtitleProps?: TitleProps
} & Omit<BoxProps, 'title'> &
  CardVariants

type ExtendedContentCardProps = CardProps & {
  autoExpand?: boolean
  expandClickDisabled?: boolean
  extendedCardVariant?: 'payout'
}

export type {
  BoxProps,
  CardContentProps,
  CardHeaderProps,
  CardProps,
  CardVariants,
  CardVariantType,
  ExtendedContentCardProps,
  IconSizes,
  SharedCardProps,
  TitleProps,
}
