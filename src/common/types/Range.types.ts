import { HTMLAttributes } from 'react'
import { ObjectIdProperty } from '../analytics/ObjectId'

type SliderColorVariants = 'yellow' | undefined

interface RangeProps
  extends Omit<HTMLAttributes<HTMLInputElement>, 'onChange'> {
  onChange?: (value: number) => void
  steps: Array<number>
  value: number
  disabled?: boolean
  thumbBubble?: React.ReactNode
  className?: string
  variant?: 'yellow'
  trackRangeId?: ObjectIdProperty
  disabledIncrement?: boolean
  disabledDecrement?: boolean
  enabledSteps?: Array<number>
}

export type { RangeProps, SliderColorVariants }
