import { ReactNode } from 'react'
import { TestID } from '../../../cypress/support/ui-component-ids'

type MenuCardConfig = {
  title: string
  variant?: 'alternative'
  comingSoon?: boolean
  items: Array<{
    mainText: string
    to?: string
    icon: string
    writeProtected?: boolean
    dataTestID?: TestID
    disabled?: boolean
    cardVariant?: 'gray-dirty'
  }>
}

type SecondaryMenuProps = {
  navigateTo: string
  pageTitle: string
  menuCards: Array<MenuCardConfig>
  children?: ReactNode
}

export type { MenuCardConfig, SecondaryMenuProps }
