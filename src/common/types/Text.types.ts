import { CSSProperties } from 'react'
import { ValidationData } from './CommonTypes.types'
import { UserInputProps } from './InputTypes.type'

type TextInputProps<T> = {
  debounceTime?: number
  validatorFunction?: (value: T, isOptional?: boolean) => void
  formatterFunction?: (value: T) => T
  onChange?: (value: T) => void
} & UserInputProps<T>

type TextErrorProps = {
  validationObject?: ValidationData
  className?: string
  dataTestID?: string
  errorText?: string
  position?: CSSProperties['position']
}

export type { TextInputProps, TextErrorProps }
