import { ButtonVariant } from './Button.types'
import { TrackActivity } from './CommonTypes.types'

type ButtonAndClickableTextProps = {
  buttonOnClick: () => void
  textOnClick: () => void
  buttonLabel: string
  textLabel?: string
  textDataTestID?: string
  buttonDataTestID?: string
  buttonTrackActivity?: TrackActivity
  textTrackActivity?: TrackActivity
  buttonVariant?: ButtonVariant
}

export type { ButtonAndClickableTextProps }
