import { useEffect } from 'react'
import { useSearchParams } from 'react-router'
import { DASHBOARD_CONSTANTS } from '../../features/dashboard/utils/consts'
import { COMMON_CONSTANTS } from '../utils/consts'
import {
  changeLanguageForCountry,
  getCountryInformation,
  getDetectedIpCountry,
  numberFormatter,
} from '../utils/UtilFunctions'
import { useSupportedCountries } from './useSupportedCountries'

const PERCENT_DIGITS_FORMATTING = {
  maximumFractionDigits: 1,
  minimumFractionDigits: 1,
}

const CURRENCY_DIGITS_FORMATTING = {
  maximumSignificantDigits: 4,
  minimumSignificantDigits: 1,
  maximumFractionDigits: 0,
  minimumFractionDigits: 0,
}

/**
 * @param overrideResidency Optional parameter to overrides the detected
 * residency or the one passed from query params. Useful when there is a country
 * dropdown and the UI needs to react to the country that is selected.
 *
 * Provides functions for formatting number values and other localization
 * options based on user residency for now. This is a soft "settings" feature
 * which will scale into a settings page where the user would be able to save
 * settings on how they want values in the app to be formatted.
 */
export const useLocalization = (overrideResidency?: string) => {
  const [urlSearchParams] = useSearchParams()

  const res = urlSearchParams.get('country')?.toUpperCase()
  const language = urlSearchParams.get('lang')

  const detectedCountry = getDetectedIpCountry()
  // Passed in country has priority, then query params country if none provided
  // then the detected IP country will be used
  const country = overrideResidency ?? res ?? detectedCountry

  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: country,
  })

  const isVerySmallScreen =
    window?.innerWidth >= DASHBOARD_CONSTANTS.IPHONE5_WIDTH &&
    window?.innerWidth < DASHBOARD_CONSTANTS.IPHONEX_WIDTH

  useEffect(() => {
    if (supportedCountry.alpha3) {
      localStorage.setItem(
        COMMON_CONSTANTS.IP_COUNTRY,
        supportedCountry.alpha3 === 'USA' ? 'USA' : supportedCountry.alpha3
      )
    }
  }, [supportedCountry?.alpha3])

  useEffect(() => {
    const selectedLocale = getCountryInformation(
      'default_locale',
      language ?? undefined
    )?.default_locale
    // If no lang param , then use detected country alpha 3's default locale

    selectedLocale && changeLanguageForCountry(selectedLocale)
  }, [language])

  /**
   * Formats a number based on residency from `user_details` and passed in `style`
   * and returns a formatted string with a currency symbol. Passed in params have
   * priority over `user_details`.
   */
  const formatAmount = ({
    amount = 0,
    currency,
    style,
    notation,
    digits,
  }: {
    amount: number | bigint
    currency?: string
    style?: 'percent' | 'currency'
    notation?: 'standard' | 'engineering' | 'compact' | 'scientific'
    digits?: {
      minimumFractionDigits?: number
      maximumFractionDigits?: number
      maximumSignificantDigits?: number
      minimumSignificantDigits?: number
    }
  }):
    | {
        formattedAmountWithSymbol: string
        symbol: string
        formattedAmount: string
      }
    | undefined => {
    try {
      const isLargeDigit =
        amount?.toString()?.length > DASHBOARD_CONSTANTS.LARGE_DIGIT_LIMIT ||
        isVerySmallScreen

      const userCountryInformation = supportedCountry

      let amountToFormat = amount
      const modifiedNotation = isLargeDigit ? 'compact' : notation

      if (typeof amount === 'bigint') {
        //FIXME: Converting bigint to Number can lose precision
        //But we do not have a use case where we use big int
        //This will be fine for a while
        amountToFormat = Number(amount)
        if (style === 'percent') {
          amountToFormat /= 100
        }
      } else if (typeof amount === 'number') {
        amountToFormat = style === 'percent' ? amount / 100 : amount
      } else {
        throw new TypeError(
          `Amount is not a number or bigint got >>${amount as string}<<`
        )
      }

      const formattedAmountWithSymbol = numberFormatter(
        amountToFormat,
        userCountryInformation?.default_locale,
        {
          currency: currency ?? userCountryInformation?.currency,
          style,
          notation: modifiedNotation,
          ...digits,
        }
      )
      //USA want to see the decimals
      const symbol = formattedAmountWithSymbol.replace(/[\d.,]/g, '').trim()

      const formattedAmount = numberFormatter(
        amountToFormat,
        userCountryInformation?.default_locale,
        {
          style: 'decimal',
          notation: modifiedNotation,
        }
      )

      return {
        formattedAmountWithSymbol,
        symbol,
        formattedAmount,
      }
    } catch (error) {
      console.error(error)
    }

    return undefined
  }

  /**
   * Formats tontinator returns with abstracted settings
   */
  const formatTontinatorAmount = ({
    amount,
    style,
    currency,
  }: {
    amount: number
    style: 'percent' | 'currency'
    currency: string
  }) => {
    if (amount) {
      const isPercentage = style === 'percent'
      return (
        formatAmount({
          amount: isPercentage ? amount : Math.trunc(amount),
          style,
          currency,
          digits: isPercentage
            ? PERCENT_DIGITS_FORMATTING
            : CURRENCY_DIGITS_FORMATTING,
        })?.formattedAmountWithSymbol ?? amount.toString()
      )
    }

    return ''
  }

  return {
    formatAmount,
    formatTontinatorAmount,
    isUSA: country === 'USA',
    detectedCountry: supportedCountry,
  }
}
