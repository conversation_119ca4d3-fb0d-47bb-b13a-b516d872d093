import { RefObject, useEffect, useState } from 'react'
import {
  UseIntersectObserverParams,
  UseIntersectObserverReturn,
} from '../types/UseIntersectObserver.types'

/**
 * Uses `IntersectionObserver` to observe a node element if it
 * shows on the screen. Use `setElement` to attach an observer to an element
 * reference
 */
export const useIntersectObserver = ({
  onIntersect,
  root = null,
  rootMargin = '20px',
  threshold = 0,
  elementToAttachObserverTo,
}: UseIntersectObserverParams): UseIntersectObserverReturn => {
  const [element, setElement] = useState<Element | RefObject<Element> | null>(
    elementToAttachObserverTo ?? null
  )

  useEffect(() => {
    /**
     * Handler for the observer, where we check if the target is
     * into view
     */
    const handleObserver = (entries: IntersectionObserverEntry[]) => {
      const target = entries[0]
      if (target.isIntersecting) {
        onIntersect(target.isIntersecting)
      }
    }

    const observer = new IntersectionObserver(handleObserver, {
      root: root as Element | Document | null,
      rootMargin,
      threshold: Array.isArray(threshold) ? threshold : [threshold],
    })

    // There is a reference to the element we want to observe, pass the reference
    // to the observer
    if (element) {
      if (element instanceof Element) {
        observer.observe(element)
      } else if (
        element &&
        'current' in element &&
        element.current instanceof Element
      ) {
        observer.observe(element.current)
      }
    }

    // Disconnect the observer when the component unmounts
    return () => observer.disconnect()
  }, [element, root, rootMargin, threshold])

  return { setElement }
}
