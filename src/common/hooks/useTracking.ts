import mixpanel from 'mixpanel-browser'
import { useEffect } from 'react'
import { useSearchParams } from 'react-router'
import { envs } from '../../config/envs'

const mixpanelProxy = envs.mixPanelProxy

/**
 * Handles mixpanel tracking initialization and handles tracking consent
 */
export const useTracking = () => {
  const [searchParams] = useSearchParams()
  const isConsentGiven = searchParams.get('consent') === 'true'

  useEffect(() => {
    if (envs.environment === 'production' && isConsentGiven) {
      mixpanel.init(import.meta.env.VITE_MIX_PANEL_API_KEY ?? '', {
        persistence: 'cookie',
        api_host: mixpanelProxy,
        record_heatmap_data: true,
        record_sessions_percent: 0.6,
        record_mask_text_selector: '',
      })
    }
  }, [isConsentGiven])
}
