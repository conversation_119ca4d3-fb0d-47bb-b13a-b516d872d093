import { useEffect } from 'react'
import {
  ClickedOutsideCallback,
  DetectOutsideClickRef,
} from '../types/DetectOutsideClick.types'

/**
 * Issues a callback when a click is detected that is outside the
 * attached element's reference
 */
const useDetectOutsideClick = (
  ref: DetectOutsideClickRef,
  clickedOutsideCallback: ClickedOutsideCallback = undefined
) => {
  useEffect(() => {
    /**
     * Event handler when a click outside the passed in element's ref is
     * detected
     */
    const handleClickOutside = (event: MouseEvent) => {
      if (ref?.current && !ref?.current.contains(event?.target as Node)) {
        clickedOutsideCallback?.()
      }
    }
    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [ref, clickedOutsideCallback])
}

export { useDetectOutsideClick }
