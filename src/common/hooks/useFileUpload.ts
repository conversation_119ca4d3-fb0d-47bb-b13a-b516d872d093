import {
  type ChangeEvent,
  type DragEvent,
  useCallback,
  useRef,
  useState,
} from 'react'

/**
 * Handles file upload and drag and drop functionality, allows for multiple
 * files to be uploaded
 */
export const useFileUpload = (
  onFilesSelected: (files: Array<File>) => void,
  onDragStateChange?: (isDragging: boolean) => void
) => {
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFiles = useCallback(
    (files: FileList) => {
      const fileArray = Array.from(files)
      onFilesSelected(fileArray)
    },
    [onFilesSelected]
  )

  const handleDrag = useCallback((e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDragIn = useCallback(
    (e: DragEvent) => {
      handleDrag(e)
      setIsDragging(true)
      onDragStateChange?.(true)
    },
    [handleDrag, onDragStateChange]
  )

  const handleDragOut = useCallback(
    (e: DragEvent) => {
      handleDrag(e)
      setIsDragging(false)
      onDragStateChange?.(false)
    },
    [handleDrag, onDragStateChange]
  )

  const handleDrop = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      handleDrag(e)
      setIsDragging(false)
      onDragStateChange?.(false)

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        handleFiles(e.dataTransfer.files)
        e.dataTransfer.clearData()
      }
    },
    [handleDrag, handleFiles, onDragStateChange]
  )

  const handleFileInput = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        handleFiles(e.target.files)
      }
    },
    [handleFiles]
  )

  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  return {
    isDragging,
    fileInputRef,
    handleFiles,
    handleDrag,
    handleDragIn,
    handleDragOut,
    handleDrop,
    handleFileInput,
    triggerFileInput,
  }
}
