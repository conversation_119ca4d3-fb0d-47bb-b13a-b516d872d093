import { useCallback, useState } from 'react'
import { browserStorageEnabled, safelyParseJSON } from '../utils/UtilFunctions'

type useBrowserStorageParams = {
  key: string
  initialValue?: string | object | null | number | boolean
  storageType?: 'local' | 'session'
}
/**
 * Default storage is `local`
 * Used for storing and retrieving data from `localStorage` or
 * `sessionStorage` depending on the passed in `storageType`, the stored data is
 * safely stringified and stored into `localStorage` or `sessionStorage`. The
 * retrieved data is safely parsed. Additional check is made if the browser
 * storage is enabled so it does not crash the app
 */
const useBrowserStorage = ({
  key,
  initialValue,
  storageType = 'local',
}: useBrowserStorageParams) => {
  const storage = storageType === 'local' ? localStorage : sessionStorage

  const [storedValue, setStoredValue] = useState<unknown>(() => {
    //Browser storage enabled, try to retrieve the stored data, otherwise return
    //the initial value
    if (browserStorageEnabled()) {
      //Retrieve an item from local storage
      const localStorageString = storage?.getItem(key) as string | undefined

      //Parse the returned item into JSON
      const parsedJSON = safelyParseJSON(localStorageString) as
        | string
        | undefined

      return parsedJSON ?? initialValue
    }
    //storage disabled return the initial value
    return initialValue
  })

  /**
   * Stringifies and adds a value to the `localStorage` and sets
   * the value into hook's state
   */
  const addValueToStorage = useCallback(
    (value: unknown) => {
      try {
        storage.setItem(key, JSON.stringify(value))
        setStoredValue(value)
      } catch (error) {
        console.error(error)
      }
    },
    [key, storage]
  )

  /**
   * Removes a value from the `localStorage` with given key
   */
  const removeValueFromStorage = (key: string) => storage.removeItem(key)

  return { storedValue, addValueToStorage, removeValueFromStorage }
}

export default useBrowserStorage
