import { useEffect, useMemo, useState } from 'react'
import { OptionValue } from '../types/Dropdown.types'
import { COMMON_CONSTANTS } from '../utils/consts'

/**
 * Searches an array of objects with the provided `searchBy` array of object keys
 */
export const useSearch = <T>({
  options,
  searchBy,
}: {
  options: Array<T>
  searchBy: Array<string>
}) => {
  const [foundOptions, setFoundOptions] = useState(options)
  const [searchQuery, setSearchQuery] = useState('')
  const [timerId, setTimerId] = useState<NodeJS.Timeout | null>(null)

  // Debounce function
  const debounce = (func: () => void, delay: number) => {
    if (timerId) clearTimeout(timerId)
    setTimerId(setTimeout(func, delay))
  }

  // makes sure that the
  // options are updated if they change
  useMemo(() => {
    setFoundOptions(options)
  }, [options])

  useEffect(() => {
    // 500ms debounce
    debounce(() => {
      const filteredOptions = searchForOption({
        options,
        searchQuery,
        searchBy,
      })

      setFoundOptions(filteredOptions)
    }, COMMON_CONSTANTS.SEARCH_DEBOUNCE_MS)
  }, [searchQuery])

  return {
    foundOptions,
    searchQuery,
    setSearchQuery,
  }
}

/**
 * Filters passed in options based on the passed in search query and search by keys
 */
const searchForOption = <T>({
  options,
  searchQuery,
  searchBy,
}: {
  options: Array<T>
  searchQuery: string
  searchBy?: Array<string>
}) => {
  if (searchBy && searchBy?.length > 0) {
    return options.filter((option) => {
      const isMatch = searchBy.some((key) => {
        if (!option) return false
        const value = (option as OptionValue)[key]?.toLowerCase()
        return value?.indexOf(searchQuery?.toLowerCase()) > -1
      })

      return isMatch
    })
  }

  return options
}
