@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define divider */
.divider {
  position: relative;
  width: 100%;

  &__number {
    @include mixins.font-style(
      $font-weight: variables.$font-bold,
      $font-size: variables.$font-size-s
    );
    @include mixins.flex-layout;
    width: 25px;
    height: 25px;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    top: -13px;
    background-color: colors.$yellow;
    border-radius: variables.$rounded-full;
  }

  &__line {
    border: 0.5px solid colors.$white-faint;
    background-color: colors.$white-faint;
    &--active {
      background-color: colors.$blue-light !important;
      border: 1px solid colors.$blue-light !important;
    }
  }
}
