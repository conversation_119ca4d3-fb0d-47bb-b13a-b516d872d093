@use './abstracts/mixins';
@use './abstracts/variables';
@use './abstracts/colors';

/* @define title-and-clickable-text */
.title-and-clickable-text {
  @include mixins.flex-layout(column);
  @include mixins.no-user-select;
  width: 100%;

  &__title {
    @include mixins.font-style(
      $font-weight: variables.$font-bold,
      $font-size: variables.$font-size-xxlarge,
      $color: colors.$blue
    );
    text-align: center;
  }

  &__sub-title {
    @include mixins.font-style(
      $font-size: variables.$font-size-l,
      $color: colors.$blue,
      $font-weight: variables.$font-bold,
      $line-height: 30px
    );
    text-align: center;
  }

  &__clickable-text {
    @include mixins.font-style(
      $font-size: variables.$font-size-l,
      $color: colors.$blue,
      $line-height: 1.875rem
    );
    text-align: center;
    cursor: pointer;
    text-decoration: underline;
  }
}
