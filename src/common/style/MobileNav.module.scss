@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

@mixin text-wrapper($background-color: colors.$blue,
  $flex-direction: row,
  $width: 8.75rem,
  $gap: 0.5rem) {
  @include mixins.flex-layout($flex-direction: $flex-direction);
  position: relative;
  background-color: $background-color;
  height: 50%;
  width: $width;
  cursor: pointer;
  padding: 0.3125rem;
  border-radius: variables.$rounded;
  white-space: nowrap;
  gap: $gap;
}

/** @define mobileNav */
.mobileNav {
  display: none;
  width: 100%;
  background-color: colors.$blue !important;
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 0.5rem variables.$mobile-spacing;
  z-index: variables.$mobile-nav-z-index;
  height: 5rem;
  text-transform: uppercase;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);

  &__item-wrapper {
    @include mixins.flex-layout(row, space-between, center);
  }

  &__text-wrapper {
    @include text-wrapper($background-color: colors.$blue);

    &--active {
      @include text-wrapper($background-color: colors.$white);
    }
  }

  &__text {
    font-size: variables.$font-size-s;
    color: colors.$white;
    font-weight: variables.$font-semibold;

    &--active {
      color: colors.$blue;
      font-weight: variables.$font-semibold;
    }
  }

  &__notification {
    background-color: red;
    padding: 6px;
    position: absolute;
    border-radius: 50%;
    bottom: 5px;
    left: 30px;
    z-index: variables.$nav-notification-z-index;
  }

  &__icon {
    height: 30px;
  }

  //Iphone 5 and smaller screen devices
  @media screen and (max-width: 320px) {
    &__text-wrapper {
      width: 87px;
    }

    &__text {
      font-size: variables.$font-size-xs;
    }
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    display: block;
    height: 60px;

    &__item-wrapper {
      justify-content: start;
    }

    &__notification {
      padding: 5px;
      bottom: 20px;
      left: 43px;
    }

    &__text {
      font-size: 12px;

      &--active {
        font-size: 12px;
      }
    }

    &__icon {
      height: 18px;
    }

    &__text-wrapper {
      @include text-wrapper($background-color: colors.$blue,
        $flex-direction: column,
        $width: 85px,
        $gap: 5px);

      &--active {
        @include text-wrapper($background-color: colors.$white,
          $flex-direction: column,
          $width: 85px,
          $gap: 5px);
      }
    }
  }
}