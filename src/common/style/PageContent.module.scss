@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/* @define pageContent **/
.pageContent {
  @include mixins.flex-layout(column);
  @include mixins.font-style($font-size: variables.$font-size-m);

  &__text-content {
    margin-bottom: 0.625rem;
  }

  &__title {
    font-weight: variables.$font-bold;
  }

  &__icon {
    height: 50px;
    margin-bottom: 1.25rem;
  }

  &--small {
    .pageContent__icon {
      height: 20px;
      width: 20px;
      margin-bottom: 15px;
    }
  }

  &__main-content {
    width: 100%;
  }

  &__container {
    @include mixins.flex-layout(column);
    width: 100%;
    padding: 0.9375rem;
    border-radius: variables.$rounded;
    background-color: colors.$blue-faint;
    &--white {
      background-color: colors.$white;
      padding: 0;
    }
  }
}
