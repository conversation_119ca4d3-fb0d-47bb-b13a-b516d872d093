@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define navBar */
.navBar {
  @include mixins.flex-layout;
  @include mixins.no-user-select;
  position: relative;
  width: 100%;
  z-index: variables.$nav-bar-z-index;
  background-color: colors.$blue;
  padding: 10px 100px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);

  &__container {
    @include mixins.flex-layout(row, space-between);
    width: 93.75rem;
  }

  &__acc-container {
    @include mixins.flex-layout;
    gap: 1.25rem;
  }

  &__language-dropdown {
    width: 7rem;
    height: 3rem;
    margin-left: auto;
    margin-right: 1.25rem;
  }

  &__auth-section {
    @include mixins.flex-layout;
    gap: 1.25rem;
    min-width: 15.625rem;
  }

  &__signup {
    &--hidden {
      display: none;
    }
  }

  &__login {
    &--hidden {
      display: none;
    }
  }

  &__icon {
    cursor: pointer;
    width: 250px;
    height: 60px;

    &:hover {
      opacity: 0.6;
    }
  }

  &__auth-btn {
    width: 100px !important;
  }

  &__nav-items {
    @include mixins.flex-layout(row, space-between);
    gap: 1.875rem;
    margin: 0 0.625rem;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    display: none;
  }
}