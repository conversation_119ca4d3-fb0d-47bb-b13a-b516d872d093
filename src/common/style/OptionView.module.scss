@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define option-view */
.option-view {
  @include mixins.flex-layout($justify-content: space-between);
  @include mixins.no-user-select;
  cursor: pointer;
  padding-left: 20px;
  padding-right: 40px;
  margin: 5px 0;

  &__container {
    @include mixins.flex-layout($gap: 10px);
  }

  &:hover {
    background-color: colors.$blue-lighter;
  }

  &--selected {
    background-color: colors.$blue-lighter;
  }

  &--back-option {
    opacity: 0.6;
  }

  &--no-data {
    @include mixins.no-user-select;
    pointer-events: none;
  }

  &__icon {
    width: 25px;
    height: 25px;
  }

  &__text {
    @include mixins.font-style(
      $font-size: variables.$input-field-font-size,
      $font-weight: variables.$font-bold
    );
  }
}
