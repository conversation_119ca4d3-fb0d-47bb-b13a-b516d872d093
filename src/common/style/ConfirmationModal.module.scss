@use './abstracts/mixins';
@use './abstracts/variables';

/** @define confirmationModal */
.confirmationModal {
  @include mixins.flex-layout(column);

  &__icon {
    height: variables.$modal-icon-size;
    width: variables.$modal-icon-size;
    margin-bottom: 0.9375rem;
  }

  &__animation {
    height: variables.$modal-animated-icon-size;
    width: variables.$modal-animated-icon-size;
  }

  &__title {
    @include mixins.font-style($font-size: variables.$font-size-l,
      $font-weight: variables.$font-semibold );
    text-align: center;
    margin-bottom: 0.9375rem;
  }

  &__explainer {
    @include mixins.font-style;
    text-align: center;
    margin-bottom: 0.9375rem;
  }

  &__children_wrapper {
    @include mixins.flex-layout($flex-direction: column, $justify-content: center, $gap: 0.5rem);

    width: 100%;
  }
}