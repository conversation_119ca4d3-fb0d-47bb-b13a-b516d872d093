@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

$checkmark-green: url('../../assets/icon-account_checked-green-circle_small.svg');

/** @define multi-selection */
.multi-selection {
  @include mixins.no-user-select;
  width: 100%;
  min-width: 300px;

  &__label {
    font-size: variables.$font-size-s;
    color: colors.$gray-dark;
    margin-bottom: 0;
    font-weight: variables.$font-normal;
  }

  &__container {
    background-color: colors.$white;
    display: flex;
    flex-wrap: wrap;
    padding: 0 15px;
    border-radius: variables.$rounded;
  }

  &__list-item {
    @include mixins.flex-layout(row, space-between);
    width: 100%;
    padding: 15px 0;
    cursor: pointer;
    border-bottom: 1px solid colors.$white-faint;
    &:last-child {
      border-bottom: 0;
    }
  }
  &__title {
    font-size: variables.$font-size-m;
    color: colors.$gray-dark;
    font-weight: variables.$font-semibold;
    margin-bottom: 0;
    max-width: 18.75rem;
  }
  &__sub {
    font-size: variables.$font-size-s;
    color: colors.$gray-dark;
    font-weight: variables.$font-normal;
    margin-bottom: 0;
  }
  &__action {
    @include mixins.multi-select-action;
    &--active {
      @include mixins.multi-select-action;
      border: none;
      background: $checkmark-green no-repeat;
    }

    &--inactive {
      @include mixins.multi-select-action;
    }
  }
}
