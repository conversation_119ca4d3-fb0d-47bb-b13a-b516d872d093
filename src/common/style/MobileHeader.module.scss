@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define mobileHeader */
.mobileHeader {
  display: none;

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.flex-layout(row, space-between, center);
    width: 100%;
    position: sticky;
    top: 0;
    left: 0;
    height: 50px;
    padding: 0 15px;
    background-color: colors.$blue;
    z-index: variables.$mobile-header-z-index;

    &__action {
      cursor: pointer;
    }

    &__root-title {
      @include mixins.no-user-select;
      @include mixins.font-style(
        $font-size: variables.$font-size-large,
        $font-weight: variables.$font-bold,
        $color: colors.$white
      );
    }

    &__root-logo {
      width: 30px;
      height: 35px;
      cursor: pointer;
    }

    &__page-title {
      @include mixins.font-style(
        $font-size: variables.$font-size-m,
        $font-weight: variables.$font-semibold,
        $color: colors.$white
      );

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
      width: 100%;
    }

    &__back-icon,
    &__secondary-icon {
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    &__options {
      font-size: variables.$font-size-s;
    }
  }
}
