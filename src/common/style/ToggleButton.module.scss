@use './abstracts/colors';
@use './abstracts/variables';
@use './abstracts/mixins';

$shadow-values: 0px 8px 10px -3px;
$border-width: 2px;

@mixin toggle-button($bg-color: colors.$white,
  $border-color: colors.$gray-faint) {
  padding: 5px;
  cursor: pointer;
  background-color: $bg-color;
  border: $border-width solid $border-color;
  border-radius: variables.$rounded;
  @include mixins.flex-layout(row, center, center, $gap: 7px);
  @include mixins.no-user-select;

  @media only screen and (max-width: variables.$mobile-devices) {
    height: 42px;
  }
}

@mixin active($border-color: colors.$blue, $bg-color: colors.$blue-faint) {
  box-shadow: variables.$chart-box-shadow;
  @include toggle-button($bg-color: $bg-color, $border-color: $border-color);
}

/** @define toggle-button */
.toggle-button {
  width: 100%;
  // Desired height is 38px + 2px border when active/hover
  height: 38px;

  &__container {
    box-shadow: variables.$chart-box-shadow;
    @include toggle-button;

    &--active {
      @include active;
    }

    &:hover {
      @include active($border-color: colors.$blue-light);

      // Prevents mobile from imitating the hover effect
      @media only screen and (max-width: variables.$mobile-devices) {
        @include active($border-color: transparent, $bg-color: transparent);
        @include mixins.no-user-select;
      }
    }
  }

  &__label {
    text-align: center;
    white-space: nowrap;
    @include mixins.font-style($color: colors.$gray-dark,
      $font-weight: variables.$font-bold );
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    box-shadow: $shadow-values rgba(0, 0, 0, 0.2);

    &__label {
      line-height: 14px;
      white-space: wrap;
      @include mixins.font-style($color: colors.$gray-dark,
        $font-weight: variables.$font-bold,
        $font-size: variables.$font-size-xs );
    }
  }
}