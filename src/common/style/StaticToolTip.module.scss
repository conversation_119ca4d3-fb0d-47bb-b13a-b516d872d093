@use './abstracts/mixins';
@use './abstracts/colors';
@use './abstracts/variables';

//Used to add a tip to the tooltip, currently the values are set for the tooltip
//to be rendered under the tooltip box
@mixin tip($color: colors.$blue, $left: 41%, $rotate: 0, $bottom: -10px) {
  &::after {
    content: '';
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid $color;
    bottom: $bottom;
    position: absolute;
    left: $left;
    transform: rotate($rotate);
  }
}

/** @define static-tooltip */
.static-tooltip {
  width: 18.75rem;
  position: relative;
  max-width: 5.625rem;
  text-align: center;
  text-transform: uppercase;
  border-radius: variables.$rounded;
  padding: 0.875rem;
  @include mixins.no-user-select;

  &--inactive {
    cursor: pointer;
    background-color: colors.$white;
    border: 1px solid colors.$gray-mist;
    @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-s,
      $line-height: none
    );
  }

  &__tip--center {
    &--gold {
      @include mixins.font-style(
        $font-weight: variables.$font-semibold,
        $font-size: variables.$font-size-s,
        $line-height: none
      );
      @include tip($color: colors.$yellow, $left: 39%, $bottom: -10px);
    }
    &--blue {
      @include mixins.font-style(
        $font-weight: variables.$font-semibold,
        $color: colors.$white,
        $font-size: variables.$font-size-s,
        $line-height: none
      );
      @include tip($color: colors.$blue, $left: 39%, $bottom: -10px);
    }
  }

  &--blue {
    background-color: colors.$blue;
  }

  &--gold {
    background-color: colors.$yellow;
  }

  &__tip--right {
    &--gold {
      @include mixins.font-style(
        $font-weight: variables.$font-semibold,
        $font-size: variables.$font-size-s,
        $line-height: none
      );
      @include tip(
        $color: colors.$yellow,
        $left: 93%,
        $rotate: 270deg,
        $bottom: 18px
      );
    }
    &--blue {
      @include mixins.font-style(
        $font-weight: variables.$font-semibold,
        $color: colors.$white,
        $font-size: variables.$font-size-s,
        $line-height: none
      );
      @include tip(
        $color: colors.$blue,
        $left: 93%,
        $rotate: 270deg,
        $bottom: 18px
      );
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    max-width: 10rem;
  }
}
