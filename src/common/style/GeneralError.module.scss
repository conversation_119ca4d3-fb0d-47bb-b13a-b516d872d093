@use './abstracts/mixins';
@use './abstracts/variables';

/** @define general-error */
.general-error {
  @include mixins.flex-layout($flex-direction: column);
  margin-top: 20px;

  &__container {
    @include mixins.flex-layout($flex-direction: column);
    max-width: 400px;
    gap: 20px;
  }

  &__icon {
    width: 50px;
    height: 50px;
  }

  &__user-text {
    @include mixins.font-style($font-size: variables.$font-size-l);
    text-align: center;
  }

  &__error-msg {
    @include mixins.font-style($font-size: variables.$font-size-l, $color: red);
    text-align: center;
  }

  &__btn-container {
    @include mixins.flex-layout($flex-direction: column);
    gap: 15px;
    margin-top: 20px;
  }
}
