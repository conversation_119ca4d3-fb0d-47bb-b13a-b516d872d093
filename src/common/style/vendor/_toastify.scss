@use '../abstracts/variables';

//Overwrites the default toastify styles
//This is meant for the toast messages in the app
:root {
  --toastify-color-success: #22b573 !important;
  --toastify-color-warning: #ffcc50 !important;
  --toastify-z-index: 99999999999 !important;
}

.Toastify__close-button {
  width: 20px !important;
  height: 20px !important;
}

.Toastify__toast {
  font-family: inherit !important;

  &-container--top-right {
    width: 25rem !important;
    margin-top: 7rem;
    margin-right: 10rem;
  }

  &-icon {
    width: 30px !important;
    height: 30px !important;
  }

  //for mobile devices
  @media only screen and (max-width: variables.$mobile-devices) {
    width: initial;
    margin-top: 4rem;
    margin-left: 20px;
    margin-right: 20px;
  }
}
