@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define button-and-clickable-text */
.button-and-clickable-text {
  @include mixins.flex-layout(column, $gap: 5px);

  &__button {
    width: 350px;
  }

  &__text {
    @include mixins.no-user-select;
    @include mixins.font-style(
      $font-size: variables.$font-size-m,
      $font-weight: variables.$font-bold
    );
    text-decoration: underline;
    text-align: center;
    cursor: pointer;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
    &__button {
      width: 100%;
    }
  }
}
