@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define carousel */
.carousel {
  overflow: hidden;
  position: relative;
  margin: 0 12.5rem;
  margin-top: 0.5rem;

  &__inner {
    white-space: nowrap;
    transition: all 1s;
  }

  &__animation {
    height: 25rem;
    width: 100%;

    @media screen and (min-width: 901px) and (max-width: 1280px) {
      /* Styles for Desktops */
      height: 15.625rem;
    }
  }

  &__image {
    height: 300px;
    width: 400px;
  }

  &__title {
    @include mixins.font-style($font-size: variables.$font-size-large,
      $font-weight: variables.$font-semibold );

    text-align: center;
    margin-top: 3.125rem;
    margin-bottom: 1.875rem;
  }

  &__content {
    @include mixins.font-style;
    text-align: center;
    white-space: break-spaces;
    max-width: 43.75rem;
  }

  &__indicator {
    @include mixins.flex-layout(row, null, null);
    background-color: colors.$white;
    border-radius: variables.$rounded-pill;
    position: absolute;
    transform: translateX(-50%);
    left: 50%;
    bottom: 200px;
    z-index: variables.$carousel-indicator-z-index;
    padding: 2px;

    &-item {
      @include mixins.carousel-indicator-item;

      &--active {
        @include mixins.carousel-indicator-item;
        background: colors.$blue-light;
        opacity: 1;
        width: 20px;
        border-radius: 30px;
      }
    }
  }

  &__item {
    display: inline-block;
    width: 100%;
    position: relative;
    margin-top: 4.375rem;

    &-container {
      @include mixins.flex-layout(column);
    }
  }

  &__control {
    border: none;
    display: inline-block;
    position: absolute;
    height: 35px;
    width: 35px;
    top: calc(50% - 39px);
    cursor: pointer;

    &-left {
      left: 15%;
      transform: rotate(180deg);
    }

    &-right {
      right: 15%;
    }
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    margin-left: 0;
    margin-right: 0;
    height: 30rem;
    margin-top: 0;

    &__item {
      margin-top: 2.5rem;
    }

    &__image,
    &__animation {
      height: 200px;
      width: 250px;
    }

    &__indicator {
      bottom: 210px;
    }

    &__title {
      margin-top: 1.875rem;
      margin-bottom: 1.5625rem;
    }

    &__content {
      text-align: center;
      font-size: variables.$font-size-m;
      white-space: break-spaces;
      max-width: 21.875rem;
    }

    &__control {
      top: calc(50% - 80px);

      &-left {
        left: 10px;
      }

      &-right {
        right: 10px;
      }
    }
  }
}