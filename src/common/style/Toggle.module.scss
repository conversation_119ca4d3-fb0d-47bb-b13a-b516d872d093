@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define toggle */
@mixin toggle-border($border-color) {
  padding-bottom: 0.75rem;
  border-bottom: 2px solid $border-color;
  @include mixins.flex-layout;
}

.toggle {
  height: auto;
  cursor: pointer;
  @include mixins.flex-layout;

  &__container {
    max-width: 300px;

    &--on {
      cursor: pointer;
      @include toggle-border(colors.$blue);
    }
    &--off {
      cursor: pointer;
      @include toggle-border(colors.$gray-faint);
    }
  }

  &__label {
    cursor: pointer;
    @include mixins.no-user-select;
    @include mixins.font-style(
      $font-size: variables.$font-size-s,
      $font-weight: variables.$font-semibold,
      $line-height: 20px
    );

    @media only screen and (max-width: variables.$mobile-devices) {
      width: 100%;
      @include mixins.font-style(
        $font-size: variables.$font-size-xxs,
        $font-weight: variables.$font-semibold,
        $line-height: 15px
      );
    }
  }

  &__icon {
    width: 35px;
    height: 30px;
    margin-left: 5px;
    @media only screen and (max-width: variables.$mobile-devices) {
      margin-left: 2px;
    }
  }
}
