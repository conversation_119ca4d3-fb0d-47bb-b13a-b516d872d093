@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define selectValue */
.selectValue {
  height: variables.$user-input-height;
  max-width: variables.$user-input-width;
  position: relative;

  &__button--hidden {
    visibility: hidden;
  }

  &--without-label {
    @include mixins.flex-layout;
  }

  &__label {
    @include mixins.input-label;
  }

  &__container {
    @include mixins.flex-layout(row);
    width: 100%;
    gap: 10px;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    max-width: 100%;
  }
}
