@use './abstracts/colors';
@use './abstracts/variables';
@use './abstracts/mixins';

/** @define file-upload */
.file-upload {
  position: relative;
  border-radius: variables.$rounded;
  background-color: colors.$gray-faint;
  padding: 1.25rem;
  text-align: center;
  cursor: pointer;
  height: 12.5rem;
  transition: border-color 0.3s ease;
  @include mixins.flex-layout($flex-direction: column, $gap: 0.625rem);
  @include mixins.font-style;

  &--dragging,
  &:hover {
    outline: 1px solid colors.$blue-lighter;
    background-color: colors.$blue-faint;
    opacity: 0.7;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    height: 9.375rem;
    @include mixins.font-style($font-size: variables.$font-size-s);
  }
}
