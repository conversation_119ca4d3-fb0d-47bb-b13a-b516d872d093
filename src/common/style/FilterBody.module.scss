@use './abstracts/mixins';
@use './abstracts/variables';

/** @define filter-body */
.filter-body {
  width: 100%;
  padding: 0.9375rem;
  box-shadow: variables.$generic-shadow;
  border-radius: variables.$rounded;

  @include mixins.no-user-select;
  @include mixins.flex-layout($justify-content: space-between);

  &__main-text {
    @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-ml,
      $line-height: small
    );
  }

  &__sec-container {
    @include mixins.flex-layout($justify-content: center, $gap: 2.5rem);
    @media only screen and (max-width: variables.$mobile-devices) {
      gap: 0.625rem;
    }
  }
  &__reset-container {
    cursor: pointer;
    @include mixins.flex-layout(
      $justify-content: center,
      $gap: 0.625rem,
      $align-items: none
    );
    @media only screen and (max-width: variables.$mobile-devices) {
      gap: 5px;
    }
  }

  &__sec-text {
    @include mixins.font-style(
      $font-size: variables.$font-size-s,
      $line-height: small
    );
  }

  &__filter-icon {
    cursor: pointer;
    width: 25px;
    height: 25px;
  }

  &__reset-icon {
    width: 20px;
    height: 20px;
  }
}
