@use './abstracts/mixins';
@use './abstracts/variables';

/** @define store-icons */
.store-icons {
  &__icons-container {
    @include mixins.flex-layout($justify-content: center, $align-items: center);
    gap: 1.5rem;
  }

  &__icon {
    height: 3rem;
    width: 10rem;
    object-fit: cover;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__icons-container {
      @include mixins.flex-layout(column);
      gap: 0.5rem;
    }
  }
}
