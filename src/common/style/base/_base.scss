@use '../abstracts/colors';
@use '../abstracts/mixins';
@use '../abstracts/variables';

:root {
  // Needs to be with 0px not only 0
  --font-modifier: 0px;
}

* {
  -webkit-margin-before: 0;
  -webkit-margin-after: 0;
  -webkit-margin-start: 0;
  -webkit-margin-end: 0;
  -webkit-padding-start: 0;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  list-style: none;
  //Used for debugging CSS, uncomment
  //outline to enter CSS debug mode
  // outline: 1px solid black;
  @include mixins.scroll-bar-style;

  @media only screen and (max-width: variables.$mobile-devices) {

    /* For WebKit browsers only */
    // Disable scrollbar on all mobile webkit browsers
    ::-webkit-scrollbar {
      display: none !important;
      width: 0 !important;
      max-width: 0 !important;
    }
  }
}

body {
  overflow: hidden;
  font-family: 'Titillium Web', sans-serif !important;
  width: 100%;
  background: none;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;

  #root {
    background-color: colors.$white;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    background-color: transparent;
    // Needs to be scroll for mobile, because otherwise it breaks the pull to
    // refresh on mobile on webkit browsers like (Chrome, Safari and other )
    overflow-y: scroll;
  }
}