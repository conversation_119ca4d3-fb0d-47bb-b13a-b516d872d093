@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';


/** @define boxCard */
.boxCard {
    min-width: unset;
    padding: 1rem;
    @include mixins.flex-layout(column, space-between, center, 0.5rem);

    &__header {
        width: 100%;
        @include mixins.flex-layout(row, space-between, center, 0.5rem);
    }

    &__body {
        @include mixins.flex-layout(column, flex-start, flex-start, .35rem);
    }

    .boxCard__title,
    .boxCard__subtitle {
        font-size: variables.$font-size-s;

        &--alt {
            font-size: variables.$font-size-l;
            font-weight: variables.$font-bold;
        }
    }

}