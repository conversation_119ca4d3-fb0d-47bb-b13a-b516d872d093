@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/* Custom properties */
$tooltip-text-color: white;
$tooltip-background-color: colors.$gray-medium;
$tooltip-margin: 43px;
$tooltip-arrow-size: 6px;

.tooltip {
  @include mixins.flex-layout(
    row,
    $justify-content: center,
    $align-items: center
  );
  position: relative;
  z-index: 99;

  /* Absolute positioning */
  &__box {
    @include mixins.flex-layout;
    text-align: left;
    user-select: none;
    position: absolute;
    border-radius: variables.$rounded-l;
    left: 50%;
    //Controls the whole's tooltip position
    transform: translate(-43%, -42%);
    padding: 6px 10px;
    color: $tooltip-text-color;
    background: $tooltip-background-color;
    font-size: 14px;
    line-height: 1;
    z-index: 99;
    white-space: wrap;
    width: 250px;
    height: 65px;

    /* CSS border triangles */
    &::before {
      content: ' ';
      //Controls the tip's position
      left: 43%;
      border: solid transparent;
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
      border-width: $tooltip-arrow-size;
      margin-left: calc($tooltip-arrow-size) * -1;
    }

    /* Absolute positioning */
    &.top {
      top: calc($tooltip-margin) * -1.1;

      /* CSS border triangles */
      &::before {
        top: 100%;
        border-top-color: $tooltip-background-color;
      }
    }

    /* Absolute positioning */
    &.right {
      left: calc(100% + $tooltip-margin);
      top: 50%;
      transform: translateX(0) translateY(-50%);

      /* CSS border triangles */
      &::before {
        left: calc($tooltip-arrow-size) * -1;
        top: 50%;
        transform: translateX(0) translateY(-50%);
        border-right-color: $tooltip-background-color;
      }
    }

    /* Absolute positioning */
    &.bottom {
      bottom: calc($tooltip-margin) * -1;

      /* CSS border triangles */
      &::before {
        bottom: 100%;
        border-bottom-color: $tooltip-background-color;
      }
    }

    /* Absolute positioning */
    &.left {
      left: auto;
      right: calc(100% + $tooltip-margin);
      top: 50%;
      transform: translateX(0) translateY(-50%);

      /* CSS border triangles */
      &::before {
        left: auto;
        right: calc($tooltip-arrow-size) * -2;
        top: 50%;
        transform: translateX(0) translateY(-50%);
        border-left-color: $tooltip-background-color;
      }
    }
  }
}
