@use './abstracts/mixins';
@use './abstracts/variables';
@use './abstracts/colors';

/** @define open-in-app */
.open-in-app {
    @include mixins.flex-layout(row, start, center, 0.5rem);
    background-color: colors.$blue;
    padding: 0.5rem;
    z-index: 999999999999999;

    &__wrapper {
        @include mixins.flex-layout(row, space-between, center, 0.5rem);
        width: 100%;
    }

    &__logo {
        width: variables.$card-x-large-icon-size;
        height: variables.$card-x-large-icon-size;
    }

    &__content {
        @include mixins.flex-layout(column, start, start);
    }

    &__title {
        @include mixins.font-style($font-size: variables.$font-size-s, $line-height: small,
            $color: colors.$white );
    }

    &__open-button {
        border-radius: variables.$rounded-xxxl;
        padding: 5px 15px;
        flex-basis: unset;
        height: unset;
        width: unset;
    }

    &__buttonLabel {
        @include mixins.font-style($font-size: variables.$font-size-xs, $color: colors.$blue, $font-weight: variables.$font-bold, $text-transform: uppercase);
    }

    &__close-icon {
        width: 10px;
        height: 10px;
        margin: 5px;
    }
}