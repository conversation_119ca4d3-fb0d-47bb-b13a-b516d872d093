@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

// Mixin for card content style changes
@mixin card-content-styles($title-weight: variables.$font-semibold,
    $subtitle-weight: variables.$font-normal,
    $title-size: variables.$font-size-s,
    $subtitle-size: variables.$font-size-s,
    $article-gap: 0.5rem) {
    .card__title {
        @include mixins.font-style($font-weight: $title-weight,
            $font-size: $title-size,
            $line-height: small);
    }

    .card__subtitle {
        @include mixins.font-style($font-weight: $subtitle-weight,
            $font-size: $subtitle-size,
            $line-height: small);
    }

    .card__body {
        gap: $article-gap;
    }
}

/** @define card */
.card {
    background-color: colors.$white;
    min-width: 20rem;
    padding: 1.25rem;
    transition: 0.25s ease-out;
    border-radius: variables.$rounded;
    @include mixins.flex-layout(row, space-between, center, 0.75rem);
    @include mixins.no-user-select;

    &__header {
        @include mixins.flex-layout(column, center, center, 0.5rem);
    }

    &__info {
        margin-left: auto;
        @include mixins.flex-layout($gap: 0.5rem);
    }

    &__body {
        margin-right: auto;
        @include mixins.flex-layout(column, flex-start, center);
    }

    &__footer {
        margin-left: auto;
        @include mixins.flex-layout($gap: 0.5rem);
    }

    &__icon {
        width: variables.$card-icon-size;
        height: variables.$card-icon-size;

        &--small {
            width: variables.$card-small-icon-size;
            height: variables.$card-small-icon-size;
        }

        &--large {
            width: variables.$card-large-icon-size;
            height: variables.$card-large-icon-size;
        }

        &--x-large {
            width: variables.$card-x-large-icon-size;
            height: variables.$card-x-large-icon-size;
        }

        @media only screen and (max-width: variables.$mobile-devices) {
            width: variables.$card-small-icon-size;
            height: variables.$card-small-icon-size;
        }
    }

    &__title {
        width: 100%;
        @include mixins.font-style($font-weight: variables.$font-semibold,
            $font-size: variables.$font-size-ml,
            $line-height: small);

        @media only screen and (max-width: variables.$mobile-devices) {
            @include mixins.font-style($font-weight: variables.$font-semibold,
                $font-size: variables.$font-size-s,
                $line-height: small);
        }
    }

    &__subtitle {
        width: 100%;
        margin-top: 0.3125rem;
        @include mixins.font-style($font-size: variables.$font-size-s,
            $line-height: small);

        @media only screen and (max-width: variables.$mobile-devices) {
            @include mixins.font-style($font-size: variables.$font-size-xs,
                $line-height: small);
        }
    }

    &__arrow {
        height: variables.$button-icon-size;
        width: variables.$button-icon-size;

        &--up {
            transform: rotate(-90deg);
        }

        &--down {
            transform: rotate(90deg);
        }

        &--hidden {
            visibility: hidden;
        }
    }

    &__alert {
        background-color: colors.$white;
        border-radius: variables.$rounded-full;
        width: variables.$card-icon-size;
        height: variables.$card-icon-size;
        @include mixins.flex-layout;
        @include mixins.font-style($color: colors.$white,
            $font-size: variables.$font-size-xs,
            $line-height: none,
            $font-weight: variables.$font-bold );

        &--number {
            background-color: colors.$alert-amber;
        }

        &--cancelled {
            background-color: unset;
        }

        @media only screen and (max-width: variables.$mobile-devices) {
            width: variables.$card-small-icon-size;
            height: variables.$card-small-icon-size;
        }
    }

    &--off {
        border-radius: 0;
    }

    &--rounded-sm {
        border-radius: variables.$rounded-sm;
    }

    &--rounded {
        border-radius: variables.$rounded;
    }

    &--rounded-l {
        border-radius: variables.$rounded-l;
    }

    &--stat {
        background-color: colors.$blue-faint;
        @include card-content-styles($title-size: variables.$font-size-m,
            $subtitle-size: variables.$font-size-l,
            $subtitle-weight: variables.$font-semibold);
    }

    @media only screen and (min-width: variables.$mobile-devices) {
        &--enable-interact {
            &:hover {
                @include mixins.card-interact(pointer, colors.$blue-light, colors.$white);
            }

            &:active {
                @include mixins.card-interact(pointer, $text-color: colors.$white);
            }
        }
    }

    &--gray-dirty {
        background-color: colors.$gray-faint;

        @media only screen and (min-width: variables.$mobile-devices) {
            &--enable-interact {
                &:hover {
                    opacity: 0.7;
                    @include mixins.card-interact(pointer,
                        colors.$gray-faint,
                        colors.$gray-dark,
                        none);
                }

                &:active {
                    opacity: 1;
                    @include mixins.card-interact(pointer,
                        colors.$blue-faint,
                        colors.$gray-dark,
                        none);
                }
            }
        }
    }

    &--blue-faint {
        background-color: colors.$blue-faint;

        @media only screen and (min-width: variables.$mobile-devices) {
            &--enable-interact {
                &:hover {
                    opacity: 0.7;
                    @include mixins.card-interact(pointer,
                        colors.$blue-faint,
                        colors.$gray-dark,
                        none);
                }

                &:active {
                    opacity: 1;
                    @include mixins.card-interact(pointer,
                        colors.$gray-faint,
                        colors.$gray-dark,
                        none);
                }
            }
        }
    }

    &--pointer {
        cursor: pointer;
    }

    &--active {
        @include mixins.card-interact($text-color: colors.$white);

        &:hover,
        &:active {
            pointer-events: none;
        }
    }

    &--disabled {
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
        @include mixins.disabled;
    }

    @media only screen and (max-width: variables.$mobile-devices) {
        min-width: 100%;
    }

    @media only screen and (max-width: variables.$mobile-devices-s) {
        min-width: 100%;
    }
}