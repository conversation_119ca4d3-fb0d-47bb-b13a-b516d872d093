@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

$checkbox-checked: url('../../assets/icon-account_marked-square.svg');

/** @define checkbox */
.checkbox {
  @include mixins.flex-layout(row);
  gap: 5px;
  white-space: nowrap;

  &__body {
    appearance: none;
    -webkit-appearance: none;
    height: 20px;
    width: 20px;
    background-color: colors.$white;
    border: 1px solid colors.$gray-dark;
    border-radius: 5px;
    cursor: pointer;

    &:after {
      display: none;
      color: colors.$white;
    }

    &:checked {
      @include mixins.flex-layout(row, center, null);
      background: $checkbox-checked no-repeat center;
      border: none;
      &:after {
        display: block;
      }
    }
  }

  &__label {
    @include mixins.font-style($font-size: variables.$font-size-s);
    @include mixins.no-user-select;
    white-space: break-spaces;
    max-width: 100%;
    flex-basis: 100%;
    @media only screen and (max-width: variables.$mobile-devices) {
      max-width: 20rem;
    }
  }
}
