@use './abstracts/mixins';
@use './abstracts/variables';

/** @define navigation-btns */
.navigation-btns {
  @include mixins.flex-layout(column);
  margin: variables.$bottom-layout-btns-margin 0;

  &__icon {
    transform: rotate(180deg);
  }

  &__container {
    @include mixins.flex-layout;
    gap: 10px;
    width: variables.$button-max-width;
  }

  &__back {
    flex-basis: 30%;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__fixed {
      //For phones the button/s will always be on the bottom of the page
      position: fixed;
      bottom: 0;
      left: 16px;
      right: 16px;
      z-index: 9999;
    }

    &__container {
      width: 100%;
    }

    &__back {
      display: none;
      flex-basis: 30%;
      &--show-mobile {
        display: block;
      }
    }
  }
}
