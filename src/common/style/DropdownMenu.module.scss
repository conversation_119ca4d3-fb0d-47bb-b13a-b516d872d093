@use './abstracts/colors';
@use './abstracts/variables';

/** @define dropdown-menu */
.dropdown-menu {
  position: absolute;
  width: 100%;
  top: 60px;
  z-index: 9999999999999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  max-height: 15.625rem;
  overflow-y: scroll;
  border-radius: variables.$rounded;
  border: variables.$blue-light-border;
  box-shadow: 0px 2px 5px -2px rgba(0, 0, 0, 0.5);
  background-color: colors.$white;

  &--open {
    opacity: 1;
    visibility: visible;
  }
}