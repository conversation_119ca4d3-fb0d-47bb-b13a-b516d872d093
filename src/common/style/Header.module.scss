@use './abstracts/mixins';
@use './abstracts/colors';
@use './abstracts/variables';

@mixin header-text(
  $color: colors.$gray-dark,
  $fs: variables.$font-size-xxlarge
) {
  margin: 0;
  @include mixins.no-user-select;
  @include mixins.font-style(
    $font-size: $fs,
    $color: $color,
    $font-weight: variables.$font-semibold
  );
  @include mixins.first-letter-uppercase;
  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.font-style(
      $font-size: variables.$font-size-large,
      $color: $color,
      $font-weight: variables.$font-semibold
    );
  }
}

@mixin header($mt: 0, $mb: 0, $text-align: left) {
  text-align: $text-align;
  margin-top: $mt;
  margin-bottom: $mb;
}

/** @define header */
.header {
  @include mixins.no-user-select;
  @include header;

  &--spaced {
    @include header($mt: 3.125rem, $mb: 2.5rem, $text-align: center);
  }

  &--no-margin {
    @include header(0, 0);
  }

  &__text--default {
    @include header-text;
  }

  &__text--blue {
    @include header-text($color: colors.$blue);
  }
}
