@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define month-year-bubble */
.month-year-bubble {
  @include mixins.flex-layout;
  background-color: colors.$blue;
  padding: 0.0625rem 0.625rem;
  border-radius: variables.$rounded;
  gap: 5px;

  &__year-container,
  &__month-container {
    @include mixins.flex-layout;
    gap: 3px;
  }

  &__month-value,
  &__year-value {
    @include mixins.font-style(
      $font-size: variables.$font-size-l,
      $color: colors.$white,
      $font-weight: variables.$font-semibold
    );
  }

  &__month-label,
  &__year-label {
    @include mixins.font-style(
      $color: colors.$white,
      $font-size: variables.$font-size-xxs
    );
  }

  &__divider {
    transform: rotate(90deg);
    width: 25px !important;
  }
}
