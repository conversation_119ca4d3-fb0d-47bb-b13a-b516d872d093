@use './abstracts/mixins';
@use './abstracts/variables';
@use './abstracts/colors';

@mixin bordered($border-color: colors.$blue-faint) {
  border: 1px $border-color solid;
  padding: 1.25rem;
  border-radius: variables.$rounded;
  position: relative;
  z-index: 1;
  margin-top: 1.5625rem;
  padding-top: 2.75rem;
  width: 25rem;

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
  }
}

/** @define input-group */
.input-group {
  position: relative;
  z-index: 1;
  margin-top: 3.125rem;

  &--bordered-yellow {
    @include bordered(colors.$yellow);
  }

  &--bordered-light-blue {
    @include bordered(colors.$blue-light);
  }

  &__container {
    @include mixins.grid-layout($justify-content: none,
      $grid-template-columns: repeat(2, 1fr));
    column-gap: 3.125rem;

    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.grid-layout($grid-template-columns: 100%);
    }
  }

  &__label {
    @include mixins.font-style($font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-ml );

    &--bordered {
      background-color: white;
      z-index: 99;
      position: absolute;
      top: -17px;
      left: 20px;
      padding: 0 5px;
      @include mixins.font-style($font-weight: variables.$font-semibold,
        $font-size: variables.$font-size-ml );
    }
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.grid-layout($grid-template-columns: 100%);
  }
}