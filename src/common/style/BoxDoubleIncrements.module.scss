@use './abstracts/colors';
@use './abstracts/mixins';

/** @define incrementBox */
.incrementBox {
  @include mixins.flex-layout;
  background-color: colors.$blue-faint;
  cursor: pointer;

  &__icon {
    @include mixins.no-user-select;
    padding: 0.625rem;
    filter: invert(68%) sepia(6%) saturate(4807%) hue-rotate(183deg) brightness(105%) contrast(94%);

    &-up {
      @include mixins.arrow-up;

      &right--disabled {
        @include mixins.disabled;
        opacity: 0.2 !important;
      }

      &left--disabled {
        @include mixins.disabled;
        opacity: 0.2 !important;
      }
    }

    &-down {
      @include mixins.arrow-down;

      &right--disabled {
        @include mixins.disabled;
        opacity: 0.2 !important;
      }

      &left--disabled {
        @include mixins.disabled;
        opacity: 0.2 !important;
      }
    }
  }

  &__container {
    @include mixins.flex-layout(column);
  }

  //TODO: Active style on click
  &__divider {
    width: 70% !important;
  }
}