@use './abstracts/mixins';
@use './abstracts/variables';

/** @define browsers-icons */
.browsers-icons {
  &__all-browsers {
    @include mixins.flex-layout($justify-content: center, $align-items: center);
    gap: 3rem;
    padding-bottom: 2rem;
  }

  &__individual-browser {
    width: 3rem;
  }

  &__caption {
    @include mixins.font-style($font-size: variables.$font-size-ml);
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__all-browsers {
      gap: 1.5rem;
    }

    &__individual-browser {
      width: 2.5rem;
    }

    &__caption {
      @include mixins.font-style($font-size: variables.$font-size-xs);
    }
  }
}
