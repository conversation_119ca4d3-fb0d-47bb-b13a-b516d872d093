@use './abstracts/mixins';
@use './abstracts/variables';

/** @define extendedCard */
.extendedCard {
    overflow: hidden;
    max-width: 100%;

    &--payout {
        @include mixins.flex-layout($align-items: unset, $gap: 0.75rem);

        @media only screen and (max-width: variables.$mobile-devices) {
            @include mixins.flex-layout(column, $align-items: unset);
        }
    }

    &--off {
        border-radius: 0;
    }

    &--rounded-sm {
        border-radius: variables.$rounded-sm;
    }

    &--rounded {
        border-radius: variables.$rounded;
    }

    &--rounded-l {
        border-radius: variables.$rounded-l;
    }
}