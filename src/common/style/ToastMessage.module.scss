@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define toast-message */
.toast-message {
  &__title {
    margin: 0;
    @include mixins.font-style(
      $font-size: variables.$font-size-m,
      $color: colors.$white,
      $font-weight: variables.$font-semibold
    );
  }

  &__content {
    margin: 0;
    @include mixins.font-style(
      $font-size: variables.$font-size-s,
      $color: colors.$white
    );
  }

  &--black {
    .toast-message__title {
      @include mixins.font-style(
        $font-size: variables.$font-size-m,
        $color: colors.$gray-dark,
        $font-weight: variables.$font-semibold
      );
    }
    .toast-message__content {
      @include mixins.font-style(
        $font-size: variables.$font-size-s,
        $color: colors.$gray-dark !important
      );
    }
  }
}
