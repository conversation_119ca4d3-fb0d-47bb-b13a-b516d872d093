@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

@mixin message-container($bg-color: colors.$blue-faint,
  $border: none,
  $font-size: variables.$font-size-ml,
  $cursor: default) {
  background-color: $bg-color;
  gap: 30px;
  width: 100%;
  text-align: left;
  padding: 0.625rem;
  border: $border;
  border-radius: variables.$rounded;
  cursor: $cursor;
  @include mixins.flex-layout;
  @include mixins.font-style($font-size: $font-size, $line-height: 1.625rem);

  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.font-style($font-size: $font-size,
      $line-height: 20px);
  }
}

/** @define banner-message */
.banner-message {
  border-radius: variables.$rounded;
  @include mixins.flex-layout;
  @include mixins.no-user-select;


  &__text {

    // Font size variants
    &--xxlarge {
      @include message-container($font-size: variables.$font-size-xxlarge);
    }

    &--xlarge {
      @include message-container($font-size: variables.$font-size-xlarge);
    }

    &--large {
      @include message-container($font-size: variables.$font-size-large);
    }

    &--l {
      @include message-container($font-size: variables.$font-size-l);
    }

    &--ml {
      @include message-container($font-size: variables.$font-size-ml);
    }

    &--m {
      @include message-container($font-size: variables.$font-size-m);
    }

    &--s {
      @include message-container($font-size: variables.$font-size-s);
    }
  }

  &__container {
    @include message-container;

    &--info {
      @include message-container($bg-color: colors.$white,
        $border: 1px solid colors.$blue,
        $cursor: default);
    }
  }



  &__icon {
    width: 28px;
    height: 28px;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    background-color: colors.$white;

    &__container {
      gap: 1.5625rem;
    }
  }
}