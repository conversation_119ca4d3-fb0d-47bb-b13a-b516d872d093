@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define percent-tag */
.percent-tag {
  @include mixins.flex-layout;

  &__divider {
    margin-bottom: 19px;
  }

  &__container {
    @include mixins.flex-layout;
    @include mixins.no-user-select;
    padding: 2px 5px;
    background-color: colors.$green;
    border-radius: variables.$rounded;
    width: 140px;
    height: 35px;
  }

  &__icon {
    width: 22px;
    height: 22px;
    filter: variables.$full-white;
  }

  &__amount {
    @include mixins.font-style(
      $color: colors.$white,
      $font-weight: variables.$font-bold,
      $font-size: variables.$font-size-m
    );
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    height: auto;

    &__divider {
      margin-bottom: 20px;
      padding: 0 10px;
    }

    &__icon {
      width: 15px;
      height: 15px;
    }

    &__container {
      width: 75px;
      height: 30px;
      margin-bottom: 5px;
      @media screen and (max-width: 320px) {
        width: 60px;
      }
    }

    &__amount {
      @include mixins.font-style(
        $color: colors.$white,
        $font-weight: variables.$font-bold,
        $font-size: variables.$font-size-xxs
      );

      @media screen and (max-width: variables.$mobile-iphone-5) {
        @include mixins.font-style(
          $color: colors.$white,
          $font-weight: variables.$font-bold,
          $font-size: variables.$font-size-xxs
        );
      }
    }
  }
}
