@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define switch */
.switch {
  &__toggle {
    @include mixins.flex-layout(row, space-around, null);
    font-size: variables.$font-size-xs;
    border-radius: variables.$rounded-sm;
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: colors.$white-lighter;
    -webkit-transition: 0.4s;
    transition: 0.4s;

    &:before {
      border-radius: variables.$rounded-sm;
      color: colors.$gray-dark;
      font-weight: variables.$font-semibold;
      position: absolute;
      content: 'OFF';
      height: 25px;
      width: 35px;
      left: 2px;
      bottom: 2.5px;
      background-color: colors.$white;
      -webkit-transition: 0.4s;
      transition: 0.4s;
    }

    &--on,
    &--off {
      margin-top: 3px;
      @include mixins.no-user-select;
    }
  }

  &__body {
    position: relative;
    display: inline-block;
    width: 70px;
    height: 30px;
    content: 'OFF';
    & .switch__input {
      opacity: 0;
      width: 0;
      height: 0;
      &:checked + .switch__toggle {
        outline: 1px solid colors.$white-faint;
      }
      &:checked + .switch__toggle:before {
        color: colors.$white;
        font-weight: variables.$font-semibold;
        -webkit-transform: translateX(35px);
        -ms-transform: translateX(35px);
        transform: translateX(35px);
        background-color: colors.$green-neutral;
        content: 'ON';
        left: -2px;
      }
    }
  }

  &__label {
    font-size: variables.$font-size-xxs;
    color: colors.$gray-dark;
  }
}
