@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define userInput */
.userInput {
  position: relative;
  flex-basis: 100%;
  max-width: variables.$user-input-width;
  height: variables.$user-input-height;

  &--auto-h {
    height: auto !important;
  }

  &--unset-h-w {
    height: unset !important;
    max-width: unset !important;
  }

  &--text-area {
    width: 100% !important;
    max-width: none !important;
    height: 10rem !important;
    resize: none;
  }

  &__label {
    @include mixins.input-label;
  }

  &__wrapper {
    position: relative;
    text-align: center;

    .userInput__prefix {
      position: absolute;
      top: 8.7px;
      left: 10px;
      @include mixins.font-style($color: colors.$blue,
        $font-size: variables.$font-size-ml );
    }

    .userInput__suffix {
      position: absolute;
      height: variables.$button-icon-size;
      width: variables.$button-icon-size;
      top: 14px;
      right: 12px;
    }

    .userInput__suffix-text {
      position: absolute;
      top: 14px;
      right: 12px;
      @include mixins.no-user-select;
      @include mixins.font-style($line-height: none,
        $color: colors.$blue,
        $font-size: variables.$font-size-s,
        $font-weight: variables.$font-bold );

      //media query
      @media screen and (min-width: 901px) and (max-width: 1082px) {
        display: none;
      }

      @media screen and (max-width: 300px) {
        display: none;
      }
    }
  }

  &__jsx {
    position: absolute;
    top: 5px;
    left: 0;
    z-index: 0;
    pointer-events: none;
    width: 100%;
    @include mixins.font-style($font-size: variables.$font-size-m,
      $font-weight: variables.$font-semibold );
  }

  &__input-element {
    &--error {
      border: 1px red solid;
      @include mixins.font-style($font-weight: variables.$font-semibold);
      @include mixins.input-general;

      &::placeholder {
        color: colors.$gray-medium-lighter;
      }
    }

    &--prefix {
      padding-left: 40px !important;
    }

    &--default {
      border: variables.$white-faint-border;
      @include mixins.font-style($font-weight: variables.$font-semibold);
      @include mixins.input-general;

      &::placeholder {
        color: colors.$gray-medium-lighter;
      }

      &:hover {
        border: variables.$blue-light-border;
      }

      &:focus {
        border: variables.$blue-light-border;
      }

      &:read-only {
        pointer-events: none !important;
        border: none !important;
        outline: none !important;
        text-overflow: ellipsis !important;
      }
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
    max-width: none;
  }
}