import i18n, { languages } from '../../config/i18n'
import { ACCOUNT_MENU } from '../../routes/Route'
import CommonCard from '../components/card/CommonCard'
import Layout from '../components/Layout'
import NavigationButtons from '../components/NavigationButtons'
import { useCustomNavigation } from '../hooks/useCustomNavigation'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/LanguageSettings.module.scss'

const LanguageSettings = () => {
  const t = useTranslate()
  const navigate = useCustomNavigation()

  return (
    <Layout
      containerWidth="small"
      pageTitle={t('LANGUAGE_SETTINGS.PAGE_TITLE')}
      navigateTo={ACCOUNT_MENU.SETTINGS}
      bottomSection={
        <NavigationButtons
          hideActionButton
          onClickFirst={() => navigate(ACCOUNT_MENU.SETTINGS)}
        />
      }
    >
      <section className={style['language-settings__container']}>
        {languages.map((language) => {
          return (
            <CommonCard
              key={language.value}
              title={language.fullName}
              subtitle={language.value}
              variant="gray-dirty"
              iconSize="large"
              icon={language.icon}
              onClick={
                language.value !== i18n.language
                  ? () => i18n.changeLanguage(language.value)
                  : undefined
              }
              cardInfoProps={{
                cardAlertProps: {
                  alert:
                    language.value === i18n.language ? 'completed' : undefined,
                },
              }}
            />
          )
        })}
      </section>
    </Layout>
  )
}

export default LanguageSettings
