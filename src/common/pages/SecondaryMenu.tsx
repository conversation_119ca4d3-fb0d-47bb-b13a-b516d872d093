import Layout from '../components/Layout'
import MenuCard from '../components/MenuCard'
import MenuCardItem from '../components/MenuCardItem'
import SecondaryMenuContainer from '../components/SecondaryMenuContainer'
import { useTranslate } from '../hooks/useTranslate'
import { SecondaryMenuProps } from '../types/SecondaryMenu.types'

/**
 * A reusable secondary menu component that displays a list of menu cards with items
 * following the pattern used in FundPensionMenu and other similar components
 */
const SecondaryMenu = ({
  navigateTo,
  pageTitle,
  menuCards,
  children,
}: SecondaryMenuProps) => {
  const t = useTranslate()

  return (
    <Layout navigateTo={navigateTo} pageTitle={t(pageTitle)}>
      <SecondaryMenuContainer>
        {menuCards.map((card, cardIndex) => (
          <MenuCard
            key={`card-${cardIndex}`}
            title={t(card.title)}
            variant={card.variant}
            comingSoon={card.comingSoon}
          >
            {card.items.map((item, itemIndex) => (
              <MenuCardItem
                key={`item-${cardIndex}-${itemIndex}`}
                mainText={t(item.mainText)}
                to={item.to}
                icon={item.icon}
                writeProtected={item.writeProtected}
                dataTestID={item.dataTestID}
                disabled={item.disabled}
                cardVariant={item.cardVariant}
              />
            ))}
          </MenuCard>
        ))}
        {children}
      </SecondaryMenuContainer>
    </Layout>
  )
}

export default SecondaryMenu
