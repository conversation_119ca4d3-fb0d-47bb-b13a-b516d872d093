import { captureException } from '@sentry/browser'
import axios, { isCancel } from 'axios'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import {
  type CountryCode,
  getExampleNumber,
  parsePhoneNumber,
  validatePhoneNumberLength,
} from 'libphonenumber-js'
import examples from 'libphonenumber-js/mobile/examples'
import { toast } from 'react-toastify'
import i18n from '../../config/i18n'
import {
  AccountPermissions,
  LitePensionPlan,
  SubmissionStatus,
  UserDetails,
} from '../../features/authentication/types/AuthMachineTypes.type'
import { BankMachineContext } from '../../features/banking/types/BankMachineTypes.type'
import { AgeMonth, ErrorStorage } from '../../features/CommonState.type'
import { DASHBOARD_CONSTANTS } from '../../features/dashboard/utils/consts'
import { parseIncomeForecastParams } from '../../features/dashboard/utils/UtilFunctions'
import {
  ReferralDetails,
  UserReferralStats,
} from '../../features/referral/types/ReferralTypes.type'
import { API } from '../api/API'
import { API_ERROR } from '../constants/ApiErrors'
import countriesLocales from '../constants/countries-locales.json'
import { regex } from '../constants/Regex'
import { TontinatorDefaultUIParams } from '../constants/TontinatorDefaultUIParams'
import {
  ForecastAges,
  IncomeForecastParams,
  IncomeForecastRequestBody,
  SexType,
  StateCodesAlpha2,
  StateData,
  ValidationData,
} from '../types/CommonTypes.types'
import {
  ApiError,
  DateDifferenceValues,
  FilterArrayByDateRangeParams,
  MonthFormatting,
  RangeTypeToRange,
} from '../types/UtilFunctions.types'
import { APP_LINKS, COMMON_CONSTANTS } from '../utils/consts'

/** Returns the locize translation for the passed in key */
const i18Translation = (locizeKey: string, options?: string) =>
  i18n.t(locizeKey, options ?? '')

/** Checks if browser storage is enabled on user's browser */
const browserStorageEnabled = () => {
  const test = 'test'
  try {
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch (e) {
    console.log(e)
    return false
  }
}

/** Generates an array of numbers from `start` to `end`, inclusive.
 *  Reverse range is supported
 */
const generateRange = (start: number, end: number) =>
  Array(Math.max(end, start) - Math.min(start, end) + 1)
    .fill(0)
    .map((_, idx) => start + idx)

/** Writes the provided content to user's clipboard */
const copyToClipboard = (content: string, toastMessage: string) => {
  navigator.clipboard
    .writeText(content)
    .then(() => toast.success(toastMessage))
    .catch(() => toast.error('Failed to copy to clipboard'))
}

/** Converts seconds to minutes, rounding down to the nearest whole number.
 * Returns `null` if the input is `undefined`, `null`, or not a positive number.
 * */
const secondsToMinutes = (seconds?: number) =>
  seconds && seconds > 0 ? Math.floor(seconds / 60) : null

/** For a given birth date returns the exact current age */
const getCurrentAge = (dateOfBirth: string) => {
  const dob = new Date(dateOfBirth)
  return Math.abs(new Date(Date.now() - dob.getTime()).getUTCFullYear() - 1970)
}

/** Asks for browser media permission like video or audio, takes in `permission`
 * string with a permission name, returns a callback `onSuccess` if permission
 * granted and `onFailure` if permission is denied */
const askForBrowserPermission = (
  permission: string,
  {
    onSuccess,
    onFailure,
  }: {
    onSuccess: (result: boolean) => void
    onFailure: (error: Error) => void
  },
  permissionOptions: object
) => {
  const userMedia = Object.freeze({
    video: {
      video: permissionOptions ? permissionOptions : true,
    },
  } as const)

  if (permission === 'video') {
    navigator.mediaDevices
      .getUserMedia(userMedia.video)
      .then((stream) => {
        //Stops the stream right away so it does not interfere with biometrics,
        //there is no other solution for this
        stream.getTracks().forEach((track) => {
          track.stop()
        })
        onSuccess(true)
      })
      .catch(onFailure)

    return
  }
  throw Error(`Invalid argument: ${permission} does not exist `)
}

/** Generates an error message based from the API's error response ID. The error
 * response ID is then checked with the API_ERROR object that contains locize
 * keys. If an API error exists a locize translation is returned */
const generateApiError = (error?: ApiError) => {
  // error contains a response
  if (error?.response) {
    // destruct the response
    const {
      response: {
        // nested means that the API has responded with a main error and a
        // nested error which can be from another service for example banking
        // service has returned a banking service error and a user account
        // nested error
        data: { id, message, nested, data },
      },
    } = error

    // Check if the response contains an error id
    if (API_ERROR[id]) {
      // Prioritize retuning nested error id and message
      return {
        id,
        translatedError: i18Translation(API_ERROR[id]),
        apiErrorMessage: nested?.message || message,
        data,
      }
    }
  }

  return {
    id: error?.code,
    translatedError: i18Translation(
      error?.code
        ? (API_ERROR?.[error?.code] ?? 'ERROR_GENERIC')
        : 'ERROR_GENERIC'
    ),
    apiErrorMessage: error?.message,
    data: error,
  }
}

/** Returns a unique generated ID, if `sessionStorageKey` is passed in then the
 * generated key is stored in `sessionStorage` */
const generateUniqueId = (sessionStorageKey?: string) => {
  const uniqueID =
    Math.random()
      .toString(COMMON_CONSTANTS?.UNIQUE_ID_GENERATOR_RADIX)
      .substring(
        COMMON_CONSTANTS?.DECIMAL_CUT_START,
        COMMON_CONSTANTS?.DECIMAL_CUT_END
      ) +
    Math.random()
      .toString(COMMON_CONSTANTS?.UNIQUE_ID_GENERATOR_RADIX)
      .substring(
        COMMON_CONSTANTS?.DECIMAL_CUT_START,
        COMMON_CONSTANTS?.DECIMAL_CUT_END
      )

  if (browserStorageEnabled()) {
    if (sessionStorageKey) {
      sessionStorage.setItem(sessionStorageKey, uniqueID)
    }
  }

  return uniqueID
}

/** Converts a date to locale date string and locale time string, formatting
 * options can be passed in for formatting the date string */
const convertDateToClientLocale = (
  date: string | number | Date,
  formattingOptions: object,
  locale = navigator.language
) => {
  const convertedDate = new Date(date)

  return {
    localeDate: convertedDate.toLocaleDateString(),
    localeTime: convertedDate.toLocaleTimeString(),
    formattedLocaleDate: convertedDate.toLocaleDateString(
      locale || COMMON_CONSTANTS.FALLBACK_LOCALE,
      formattingOptions
    ),
  }
}

/** Checks if the user has strong authentication type */
const hasStrongAuth = (authType?: AccountPermissions) => {
  try {
    if (authType) {
      return authType === 'write'
    }
    throw new Error(
      `Function argument not provided for authType, got >>>${authType}<<<`
    )
  } catch (error) {
    console.error(error)
  }
  return undefined
}

/** Returns the DOM element that matches the passed in query selector */
const selectDomElement = (querySelector?: string): HTMLElement | null => {
  try {
    if (querySelector) {
      return document?.querySelector(querySelector)
    }
    throw new Error(
      `Function argument not provided for querySelector, got >>>${querySelector}<<<`
    )
  } catch (error) {
    console.error(error)
  }
  return null
}

/** Does a check if there is a JSON for parsing if not null is returned, if JSON
 * string is invalid then an error is thrown
 */
const safelyParseJSON = (jsonString?: string) => {
  if (jsonString) {
    try {
      return JSON.parse(jsonString) as unknown
    } catch (error) {
      console.error(error)
    }
  }
  return null
}

/** Returns the number of days in a given month and year. */
const daysInMonth = (year: number, month: number): number =>
  new Date(year, month, 0).getDate()

/** Checks if two objects are deeply equal, meaning if their keys values are
 * equal */
const deepEqual = (object1: unknown, object2: unknown): boolean => {
  if (object1 === object2) return true

  if (object1 instanceof Date && object2 instanceof Date) {
    return object1.getTime() === object2.getTime()
  }

  if (
    !object1 ||
    !object2 ||
    typeof object1 !== 'object' ||
    typeof object2 !== 'object'
  ) {
    return object1 === object2
  }

  if (Object.getPrototypeOf(object1) !== Object.getPrototypeOf(object2)) {
    return false
  }

  const keys = Object.keys(object1 as Record<string, unknown>)
  if (keys.length !== Object.keys(object2 as Record<string, unknown>).length) {
    return false
  }

  return keys.every((key) =>
    deepEqual(
      (object1 as Record<string, unknown>)[key],
      (object2 as Record<string, unknown>)[key]
    )
  )
}

/** Converts a a month from number to string with localization option */
const monthNumberToString = (
  monthNumber: number,
  locale = 'en-US',
  formatting = 'short'
) => {
  const dateObject = new Date()
  dateObject.setDate(monthNumber)
  dateObject.setMonth(monthNumber - 1)

  return dateObject.toLocaleString(locale, {
    month: formatting as MonthFormatting,
  })
}

/** Queries the `countriesLocale` for passed in search by value, and returns an
 * object containing country information
 */
const getCountryInformation = (
  key: keyof (typeof countriesLocales)[0],
  value?: string
) => {
  return countriesLocales?.find((country) => country[key] === value)
}

/**
 * Modifies `retirementAge` and `contributionAge` if they are equal, by adding a
 *  month to the payout age when converting to `AgeMonthString type. If
 *  `AgeMonthString` type is passed in then no modification is performed, the
 *  same values are returned
 */
const adjustAndConvertToAgeMonthString = (
  retirementAge?: AgeMonth,
  contributionAge?: AgeMonth
): ForecastAges => {
  if (retirementAge && contributionAge) {
    const { age: retirementYearsOld, month: retirementMonthsOld } =
      retirementAge

    const { age: contributionYearsOld, month: contributionMonthsOld } =
      contributionAge

    // biome-ignore lint/style/noParameterAssign: <TODO: Solve>
    retirementAge = modifyContributionAgeAndRetirementAge(
      contributionYearsOld,
      contributionMonthsOld,
      retirementYearsOld,
      retirementMonthsOld
    )

    return {
      contributionAge,
      retirementAge,
    } as ForecastAges
  }

  return {
    contributionAge,
    retirementAge,
  } as ForecastAges
}

/**
 * Modifies the `retirementAge` if it is equal to the `contributionAge`, by
 * incrementing the retirement month by 1. Month overflow is also handled in
 * case the `retirementMonthsOld` is above 11, valid ranges are [0,11].
 *
 * The returned value is in `AgeMonth` format
 */
const modifyContributionAgeAndRetirementAge = (
  contributionYearsOld: number,
  contributionMonthsOld: number,
  retirementYearsOld: number,
  retirementMonthsOld: number
): AgeMonth => {
  let modifiedRetirementMonth = retirementMonthsOld
  let modifiedRetirementAge = retirementYearsOld

  //Contribution age and retirement age are equal increment month by 1, so if we
  //have a scenario 65-7 65-7 we send to the tontinator 65-7 65-8
  if (
    contributionYearsOld === retirementYearsOld &&
    contributionMonthsOld === retirementMonthsOld
  ) {
    modifiedRetirementMonth = retirementMonthsOld + 1
  }

  //Month overflow check, if overflow, set month to 0 and increase retirement
  //years old by 1
  if (
    contributionYearsOld === retirementYearsOld &&
    modifiedRetirementMonth > DASHBOARD_CONSTANTS.MONTHS_OLD_MAX
  ) {
    modifiedRetirementMonth = 0
    modifiedRetirementAge = modifiedRetirementAge + 1
  }

  return { age: modifiedRetirementAge, month: modifiedRetirementMonth }
}

/**
 * For passed in string DoB `YYYY-MM-DD` format returns an object with
 * `yearsOld`, `monthBornOn` and `monthsOld` properties
 */
const dobToYearsAndMonthsOld = (
  dateOfBirth: string
): {
  yearsOld: number
  monthBornOn: number
  monthsOld: number
  birthYear: number
} => {
  const today = new Date()
  const birthDate = new Date(dateOfBirth)

  let yearsOld = today.getFullYear() - birthDate.getFullYear()
  const monthBornOn = birthDate.getMonth() + 1

  // Check if the person's birthday has already occurred this year
  if (
    today.getMonth() < birthDate.getMonth() ||
    (today.getMonth() === birthDate.getMonth() &&
      today.getDate() < birthDate.getDate())
  ) {
    yearsOld--
  }

  // Calculate the total number of months between the birth date and current
  // date
  const totalMonthsOld =
    (today.getFullYear() - birthDate.getFullYear()) * 12 +
    (today.getMonth() - birthDate.getMonth()) -
    (today.getDate() < birthDate.getDate() ? 1 : 0)

  // Convert the person's age in years to months
  const yearsInMonths = yearsOld * 12

  // Calculate the person's age in months by subtracting the complete years in
  // months from the total months
  const monthsOld = totalMonthsOld - yearsInMonths

  return {
    yearsOld,
    monthBornOn,
    monthsOld,
    birthYear: birthDate.getFullYear(),
  }
}

/**
 * Formats a number based on passed in options and returns a string
 */
const numberFormatter = (
  number: number | bigint,
  locale = 'en-US',
  options?: Intl.NumberFormatOptions
): string => new Intl.NumberFormat(locale, options).format(number)

/**
 * Calculates the difference in years,months and days between two dates using
 * Dayjs library also returns the date objects parsed with dayjs
 */
const dateDifference = (
  startDateISO: string,
  targetDateISO: string
): string | DateDifferenceValues => {
  //Necessary to have strict parsing In order to avoid parsing 2024-00-37 to
  //Tue, 04 Jan 2022
  dayjs.extend(customParseFormat)

  const startDateParsed = dayjs(startDateISO, 'YYYY-MM-DD', true)
  const targetDateParsed = dayjs(targetDateISO, 'YYYY-MM-DD', true)

  const yearsDiff = targetDateParsed.diff(startDateParsed, 'years')
  const monthsDiff = targetDateParsed.diff(startDateParsed, 'months')
  const daysDiff = targetDateParsed.diff(startDateParsed, 'day')

  //Shallow valid because, only checks format and not if the date is actually
  //The library does not really have a good definition what is valid or
  //invalid... in the docs https://day.js.org/docs/en/parse/string-format
  const shallowValid = startDateParsed.isValid() && targetDateParsed.isValid()

  //If the parsed date is invalid the difference will be a NaN
  const validDifference =
    !isNaN(yearsDiff) && !isNaN(monthsDiff) && !isNaN(daysDiff)

  if (shallowValid && validDifference) {
    return {
      years: yearsDiff,
      months: monthsDiff,
      days: daysDiff,
      targetDateParsed,
      startDateParsed,
    }
  }

  //JS natively does this, I am not sure if this is okay or not
  return COMMON_CONSTANTS.INVALID_DATE_ERROR_MSG
}

/**
 * Formats date using `datjs` library
 */
const formatDate = (
  date: string | number | dayjs.Dayjs | Date | null | undefined,
  format?: string
): string => {
  return dayjs(date).format(format)
}

/**
 * Checks if the passed in year is a leap year
 */
const isLeapYear = (year: number): boolean =>
  (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0

/**
 * Check if the user has verified their identity and returns the verified user
 * information, otherwise returns the unverified user information.
 */
const verifiedPersonalDetails = (user_details: UserDetails) => {
  //Unverified user information
  const {
    unverified_first_name,
    unverified_last_name,
    unverified_date_of_birth,
    unverified_sex,
    unverified_age,
    unverified_phone_number,
    unverified_residency,
  } = user_details

  //Verified user information from ID Verification
  const {
    verified_first_name,
    verified_last_name,
    verified_date_of_birth,
    verified_sex,
    verified_age,
    verified_phone_number,
  } = user_details

  return {
    ...user_details,
    // There is no verified residency at the moment
    residency: unverified_residency,
    first_name: verified_first_name ?? unverified_first_name,
    last_name: verified_last_name ?? unverified_last_name,
    date_of_birth: verified_date_of_birth ?? unverified_date_of_birth,
    sex: verified_sex ?? unverified_sex,
    age: verified_age ?? unverified_age,
    phone_number: verified_phone_number ?? unverified_phone_number,
  }
}

/**
 * Displays a greeting to the user depending on what period of the day it is.
 * Example: Good Morning!, Good Evening
 */
const getGreeting = (): string => {
  const currentHour = dayjs().hour()

  if (currentHour >= 5 && currentHour < 12) {
    return i18n.t('MYTT_MORNING_GREETING')
  }
  if (currentHour >= 12 && currentHour < 18) {
    return i18n.t('MYTT_AFTERNOON_GREETING')
  }
  return i18n.t('MYTT_EVENING_GREETING')
}

/**
 * Returns supported countries info, default return params is ROW (rest of
 * world)
 */
const getSupportedTontinatorParams = (alpha3CountryCode: string) => {
  return (
    TontinatorDefaultUIParams?.[
      alpha3CountryCode as keyof typeof TontinatorDefaultUIParams
    ] ?? TontinatorDefaultUIParams['ROW']
  )
}

/**
 * Returns the difference between two numbers in percentage
 */
const differenceInPercentage = (num1: number, num2: number): number =>
  ((num1 - num2) / num1) * 100

/**
 * Returns a object containing `valid` property and `message` property
 */
const generateValidationData = (
  valid: boolean,
  i18nKey?: string,
  values?: object
): ValidationData => ({
  valid,
  message: i18Translation(i18nKey ?? ''),
  i18nKey,
  values,
})

/**
 * Generates a validation object that contains `valid` property that signals if
 * the input is valid and `message` property which is used to render an error
 * message to the user by using i18n translation
 */
const issueValidationData = ({
  valid,
  i18nKey,
  setStateAction,
  values,
}: {
  valid: boolean
  setStateAction: (state: ValidationData | undefined) => void
  i18nKey?: string
  values?: object
}) => {
  try {
    setStateAction(generateValidationData(valid, i18nKey, values))
  } catch (err) {
    console.error(err)
  }
}

/**
 * Takes in validator functions and setState function to set the validation
 * data. Validator functions only set the error state if the given input, does
 * not pass validation
 * - Validation to check if input is empty is done by default
 */
const validateInputWithError = ({
  input,
  validateFormat,
  emptyInputErrorI18nKey,
  invalidInputErrorI18nKey,
  valuesForInvalidInput,
  extendedValidationErrorI18nKey,
  valuesForExtendedValidation,
  setStateAction,
  extendedValidator,
  optionalField = false,
}: {
  input?: string
  validateFormat?: (input: string) => boolean
  extendedValidator?: (input: string) => boolean
  emptyInputErrorI18nKey: string
  invalidInputErrorI18nKey?: string
  valuesForInvalidInput?: object
  extendedValidationErrorI18nKey?: string
  valuesForExtendedValidation?: object
  setStateAction: (data: ValidationData | undefined) => void
  optionalField?: boolean
}) => {
  //Input by default is invalid, but no error message in order for a submit
  //button to be disabled
  if (input === undefined) {
    issueValidationData({
      valid: false,
      setStateAction,
    })
    //Input invalid if only white space is entered or user wipes the data in the
    //input. Common case if input should not be empty
  } else if (input === null || input.length === 0 || !input.trim().length) {
    issueValidationData({
      //If optional field is true, this validation check is valid by default
      valid: optionalField,
      i18nKey: optionalField ? undefined : emptyInputErrorI18nKey,
      setStateAction,
    })
    //Validates input format depending what is passed in, common case
  } else if (validateFormat?.(input)) {
    issueValidationData({
      valid: false,
      i18nKey: invalidInputErrorI18nKey,
      values: valuesForInvalidInput,
      setStateAction,
    })
    //Extended validation if needed
  } else if (extendedValidator?.(input)) {
    issueValidationData({
      valid: false,
      i18nKey: extendedValidationErrorI18nKey,
      values: valuesForExtendedValidation,
      setStateAction,
    })
  } else {
    issueValidationData({
      valid: true,
      setStateAction,
    })
  }
}

/**
 * Returns state information from passed in state data
 */
const getStateInformation = (
  stateAlpha2ISOCode: StateCodesAlpha2,
  stateData: Array<StateData>
) => {
  if (!stateAlpha2ISOCode) {
    return undefined
  }
  try {
    if (!stateData) {
      return stateAlpha2ISOCode
    }

    return stateData.find((state) => state['iso_code'] === stateAlpha2ISOCode)
  } catch (error) {
    console.error(error)
  }
  return undefined
}

/**
 * Captures an exception and sends an alert to sentry
 */
const captureExceptionWithSentry = (error: unknown, options?: object) =>
  captureException(error, options)

/**
 * Returns how many years and months will the user have from the provided
 * FutureDate
 */
const birthAgeAndMonthsFromFutureDate = (
  birthDate: string,
  futureDate: string
): { age: number; months: number } | undefined => {
  try {
    if (!birthDate || !futureDate) {
      throw new TypeError(
        `Invalid date or could not parse arg1: >>${birthDate}<<  arg2:>>${futureDate}<<`
      )
    }

    const dateDifferencesValues: string | DateDifferenceValues = dateDifference(
      birthDate,
      futureDate
    )

    if (typeof dateDifferencesValues === 'string') {
      throw new TypeError(`Invalid date, from dateDifference`)
    }

    //Convert the total months difference into remainder months
    const remainderMonths: number = Math.round(
      dateDifferencesValues?.months % 12
    )

    return {
      age: dateDifferencesValues?.years,
      months: remainderMonths,
    }
  } catch (error) {
    console.error(error)
  }

  return undefined
}

/**
 * Calculates how old the user will be on their retirement date in months and
 * years
 */
const calculateRetirementValues = (
  user_details: Partial<UserDetails>,
  retirementData: {
    year: number
    month: number
    day?: number
  }
) => {
  try {
    const { date_of_birth } = user_details

    // Construct retirement date using dayjs for consistent YYYY-MM-DD format
    const retirementDateISO = dayjs()
      .year(retirementData.year)
      .month(retirementData.month - 1) // dayjs months are 0-indexed
      .date(retirementData.day ?? dayjs(date_of_birth).get('D'))
      .format('YYYY-MM-DD')

    const calculatedValues = birthAgeAndMonthsFromFutureDate(
      date_of_birth ?? '',
      retirementDateISO
    )

    if (!calculatedValues) {
      throw new TypeError(
        `Could not calculate retirement values got >>${JSON.stringify(
          calculatedValues
        )}<<`
      )
    }

    return calculatedValues
  } catch (error) {
    console.error(error)
  }

  return undefined
}

/**
 * - Removes trailing and leading whitespace with `.trim()`
 * - Replaces duplicate whitespace with one whitespace
 *
 * If nothing is passed in an empty string is returned
 */
const sanitizeInputValue = ({
  inputValue,
  onlySpaces,
}: {
  inputValue: string
  onlySpaces?: boolean
}): string => {
  if (inputValue) {
    inputValue = inputValue.replace(regex.duplicateSpaces, ' ')
    if (onlySpaces) {
      return inputValue.trimStart()
    }
    return inputValue.trim()
  }

  return ''
}

/**
 * Returns translated string from i18n. Is not content is found for the passed
 * in key, simply the key itself is returned.
 */
const i18nTrans = (key: string, options?: string) => {
  if (options) {
    return i18n.t(key, options)
  }
  return i18n.t(key)
}

/**
 * @important Firefox handles permissions differently, a the moment only
 * `camera` permission is supported for firefox.
 *
 * Checks if a browser permission has been granted. Firefox is handled
 * differently, and it is not reliable as the chromium based browsers API
 *
 * `PermissionStatus` is only returned if browser is chromium based
 */
const checkForBrowserPermissionStatus = async (
  permissionName: PermissionName
): Promise<
  | {
      state: 'granted' | 'prompt' | 'denied'
      name: PermissionName
      deviceId?: string
    }
  | PermissionStatus
> => {
  if (!navigator) {
    throw new Error('Navigator not defined')
  }

  // This is only defined on Firefox it is not conventional
  //@ts-expect-error
  const isFirefox = typeof InstallTrigger !== 'undefined'

  if (isFirefox) {
    return new Promise((resolve, reject) => {
      navigator?.mediaDevices
        ?.enumerateDevices()
        .then((devices) => {
          devices?.forEach((device) => {
            if (device.kind === 'videoinput') {
              resolve({
                state: device.label ? 'granted' : 'prompt',
                name: permissionName,
                deviceId: device.deviceId,
              })
            }
          })
        })
        .catch(() => {
          class PermissionDeniedError extends Error {
            state: 'denied'
            name: string
            constructor({ message, name }: { message?: string; name: string }) {
              super(message)
              this.state = 'denied'
              this.name = name
            }
          }

          return reject(
            new PermissionDeniedError({
              name: permissionName,
            })
          )
        })
    })
  }

  // Chromium handling
  return new Promise((resolve, reject) => {
    if (!navigator.permissions) {
      return reject(new Error('Permissions API not supported'))
    }
    navigator.permissions
      .query({ name: permissionName })
      .then((data: PermissionStatus) => resolve(data))
      .catch(reject)
  })
}

/**
 * Picks properties from an object and returns a new object
 */
const pickProperty = (
  obj: { [key: string]: object },
  ...keys: Array<string>
) => {
  return keys.reduce((accumulator, key) => {
    const { [key]: value } = obj
    // biome-ignore lint/performance/noAccumulatingSpread: <TODO: Check if need this rule>
    return { ...accumulator, [key]: value }
  }, {})
}

/**
 * Formats a file size in bytes to a human-readable string with appropriate units
 */
const formatFileSize = (bytes: number, decimals = 2) => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${Number.parseFloat((bytes / k ** i).toFixed(decimals))} ${sizes[i]}`
}

/**
 * Warning message to be shown to users as a security precaution
 */
const consoleWarningMessage = () => {
  console.log(
    `%c      WARNING! READ ME!

    If someone told you to copy-paste something here, DO NOT do it. It is a malicious intent!
    `,
    `color:red; font-size:25px; font-weight:700`
  )
}

/**
 * Parses a phone number only if there is a phone number and it is valid,
 * otherwise the `parsePhoneNumber` parser will throw an error
 */
const parsePhoneNum = (phoneNumber: string) => {
  try {
    // Check only the length of the phone number, this is loose validation in
    // order for the parser not to spam error messages if necessary very strict
    // check can be made with `isValidPhoneNumber` function
    if (phoneNumber && validatePhoneNumberLength(phoneNumber) !== 'TOO_SHORT') {
      const { countryCallingCode, number } = parsePhoneNumber(phoneNumber)

      const formattedPhoneNumber =
        parsePhoneNumber(phoneNumber).formatInternational()

      return {
        dialCode: `+${countryCallingCode}`,
        formattedPhoneNumber: formattedPhoneNumber.replaceAll(
          `+${countryCallingCode}`,
          ''
        ),
        phoneNumber: number,
        formattedFullPhoneNumber: formattedPhoneNumber,
      }
    }
  } catch (error) {
    console.error(error)
  }

  return undefined
}

/**
 * Returns a example phone number placeholder without dial code for the passed
 * in country dial code. If the dial code does not exist, then USA international
 * example number is returned
 */
const showExamplePhoneNumber = (dialCode: string) => {
  const alpha2CountryCode: CountryCode =
    (getCountryInformation('dial_code', dialCode)?.alpha2 as CountryCode) ??
    // We do not use Alpha2 in our codebase, won't be added in the coasts file
    'US'

  return getExampleNumber(alpha2CountryCode, examples)
    ?.formatInternational()
    ?.replace(dialCode, '')
}

/**
 * Renders nominal balance from the banking context
 */
const renderNominalBalance = (
  formatAmount: (param: {
    amount: number | bigint
    residency?: string
    currency?: string
    style?: 'percent' | 'currency'
    notation?: 'standard' | 'engineering' | 'compact' | 'scientific'
    digits?: {
      minimumFractionDigits?: number
      maximumFractionDigits?: number
      maximumSignificantDigits?: number
      minimumSignificantDigits?: number
    }
  }) =>
    | undefined
    | {
        formattedAmountWithSymbol: string
      },
  bankContext: {
    bankingInfo: {
      payinHistory?: Array<{
        nominalBalance: {
          amount: number
          currency: string
        }
      }>
    }
  } & BankMachineContext,
  notation?: 'compact' | 'standard'
):
  | undefined
  | {
      formattedAmountWithSymbol: string
    } => {
  if (bankContext?.bankingInfo?.payinHistory) {
    const contributions = bankContext?.bankingInfo?.payinHistory ?? []

    if (contributions?.length > 0) {
      const amount = contributions?.[0]?.nominalBalance?.amount
      const currency = contributions?.[0]?.nominalBalance?.currency

      return formatAmount({
        amount: Math.trunc(amount),
        currency,
        style: 'currency',
        notation: notation ?? 'compact',
        digits: {
          maximumFractionDigits: 2,
        },
      })
    }
  }

  return {
    formattedAmountWithSymbol: '-',
  }
}

/**
 *  Parses the magic login url and extracts the magic login token
 */
const parseMagicLink = (pathname: string, magicLoginParam: string) => {
  if (pathname) {
    // Split and filter out empty strings, undefined and null
    const magicLoginParams = pathname.split('/').filter((param) => param)

    //The order must be `/magic_login/:magic_login_token`
    if (
      magicLoginParams.includes(magicLoginParam) &&
      magicLoginParams?.length === 2
    ) {
      // Magic login param must be `magic_login`
      if (magicLoginParams?.[0] === magicLoginParam) {
        return {
          param: magicLoginParams?.[0],
          token: magicLoginParams?.[1],
        }
      }
    }
  }

  return undefined
}

/**
 * Parses params for email
 */
const parseParamsForEmail = (incomeForecastParams: IncomeForecastParams) => {
  if (incomeForecastParams) {
    const {
      monthlyContribution,
      oneTimeContribution,
      sex,
      countryOfResidence,
      retirementAge,
      contributionAge,
      strategy,
    } = incomeForecastParams

    const {
      contributionAge: adjustedContributionAge,
      retirementAge: adjustedPayoutAge,
    } = adjustAndConvertToAgeMonthString(retirementAge, contributionAge)

    return parseIncomeForecastParams({
      strategy,
      monthlyContribution,
      oneTimeContribution,
      countryOfResidence: countryOfResidence,
      sex,
      contributionAge: adjustedContributionAge,
      payoutAge: adjustedPayoutAge,
      isAuthenticated: false,
      writeDraftPlan: false,
    })
  }
  return undefined
}

/**
 * Fetches the user's geo location and only stores the `countryCode` for now
 */
const getIpGeoLocation = () => {
  const ipCountry = localStorage?.getItem(COMMON_CONSTANTS.IP_COUNTRY) ?? ''

  if (!ipCountry) {
    axios
      .get(API.ipGeoLocation)
      .then(
        (response: {
          data: {
            geolocation: {
              countryCode: string
              ans: string
              cityName: string
              ip: string
              isProxy: boolean
              latitude: number
              longitude: number
              regionName: string
              timeZone: string
              usingIdentifier: string
            }
            userAgent: string
          }
        }) => {
          localStorage?.setItem(
            COMMON_CONSTANTS.IP_COUNTRY,
            response.data?.geolocation?.countryCode
          )
        }
      )
      .catch((error) => {
        // No need to handle just warn that could not fetch geo location data
        console.warn('Could not fetch geo location', error)
      })
  }
}

/**
 * Returns user's ip detected country code if present in local storage, if not
 * it fallbacks to `USA`
 */
const getDetectedIpCountry = () => {
  const geoIp = localStorage?.getItem(COMMON_CONSTANTS.IP_COUNTRY)

  if (geoIp) {
    return geoIp
  }

  return COMMON_CONSTANTS.FALLBACK_COUNTRY_CODE
}

/**
 * FaceTec SDK browser compatible browsers
 */
const faceTecCompatibleBrowser = (isMobileOrTablet: boolean): boolean => {
  const userBrowser = navigator.userAgent

  // FaceTec compatible browsers
  const compatibleBrowsers: Array<string> = ['Chrome', 'Safari', 'Firefox']

  // This filters out Mozilla Firefox for mobile devices since FaceTec does not
  // support it
  const filteredBrowsers = isMobileOrTablet
    ? compatibleBrowsers.filter((browser) => browser !== 'Firefox')
    : compatibleBrowsers

  return filteredBrowsers.some((browser) => userBrowser.includes(browser))
}

/** Returns the appropriate CardAlert status for the passed in status */
const idVerificationAlertStatus = (status?: SubmissionStatus) => {
  const statusMap = {
    approved: 'completed',
    not_reviewed: 'pending',
    rejected: 'error',
  } as const
  return status ? statusMap[status] : 'warn'
}

/**
 *  Returns a throttled function of the passed in function
 */

type ThrottledFunction<T extends Array<unknown>> = (...args: T) => void
const throttle = <T extends Array<unknown>>(
  func: (...args: T) => void,
  timeout = 300
): ThrottledFunction<T> => {
  let timer: NodeJS.Timeout | null = null

  return (...args: T): void => {
    if (!timer) {
      func.apply(this, args)
      timer = setTimeout(() => {
        timer = null
      }, timeout)
    }
  }
}

/**
 * Detects a device type depending on the screen size.
 *
 * @note This is NOT an accurate way to detect user is using a certain device.
 */
const detectDeviceType = (): 'desktop' | 'tablet' | 'mobile' => {
  let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop'

  if (window.innerWidth >= 992) {
    // Desktops and laptops typically have screens wider than 992px
    deviceType = 'desktop'
  } else if (window.innerWidth >= 768 && window.innerWidth < 992) {
    // Tablets typically have screens between 768px and 991px wide
    deviceType = 'tablet'
  } else {
    // Mobile devices typically have screens narrower than 767px
    deviceType = 'mobile'
  }
  return deviceType
}

/**
 * Returns a debounced function of the passed in function
 */
const debounce = <T extends unknown[]>(
  func: (...args: T) => void,
  timeout = 300
) => {
  let timer: NodeJS.Timeout

  return (...args: T): void => {
    clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, timeout)
  }
}

/**
 * Parses forecast params from lite version of the webapp
 */
const parseLiteParams = (
  liteForecastParams: IncomeForecastRequestBody
): LitePensionPlan | undefined => {
  try {
    if (liteForecastParams) {
      const retirementAge = liteForecastParams?.contributions?.payout_age

      const {
        contribution_allocations,
        contributions,
        demographic_data_country_of_residence,
        demographic_data_sex,
        demographic_data_current_age,
      } = liteForecastParams

      return {
        contributionAge: demographic_data_current_age as AgeMonth,
        retirementAge: retirementAge,
        countryOfResidence: demographic_data_country_of_residence ?? '',
        oneTimeContribution: contributions?.onetime_amount ?? 0,
        sex: demographic_data_sex as SexType,
        monthlyContribution: contributions?.monthly_amount ?? 0,
        strategy: contribution_allocations,
        paramsMode: 'TTF',
      }
    }
  } catch (error) {
    console.error('Could not parse lite params response', error)
  }

  return undefined
}

/**
 * Parses the array returned from the referral API. Does not handle customized
 * code
 */
const parseLiteReferralData = (data?: UserReferralStats): ReferralDetails => {
  if (!data) {
    throw new Error(`No referral data returned from API`)
  }

  return {
    referralCode: data[0].referral_code,
    redeemCount: data[0].count_redeemed,
  }
}

/**
 * Fetches a country svg flag icon using the alpha2  country code
 */
const getCountrySvgIcon = (alpha2Code: string) =>
  `https://purecatamphetamine.github.io/country-flag-icons/3x2/${alpha2Code}.svg`

/**
 * Logs request body to console for debugging purposes, does only shallow check
 * if an argument has been passed
 */
const logForecastParams = (
  forecastParams?: Array<IncomeForecastRequestBody>
) => {
  if (forecastParams) {
    forecastParams.forEach((incomeForecastParam) => {
      if (incomeForecastParam) {
        console.log('↓ Params sent to the tontinator ↓')
        console.table(incomeForecastParam)
      }
    })
  } else {
    console.error('Did not get any params to log!')
  }
}

/**
 * @note The `error` is rethrow again in order to trigger an `onError` state
 * transition in a state machine Axios cancel error is ignored by default
 *
 * - Issues a failureCallback in case of an error
 * - Sends an alert with Sentry to devs
 * - Rethrows the error that occurred, unless explicitly specified not to do so
 */
const writeToConsoleAndIssueAlert = ({
  error,
  failureCallback,
  skipRethrow,
}: {
  error: unknown
  failureCallback?: (parsedError: ErrorStorage) => void
  errorMessage?: string
  skipRethrow?: boolean
}): void => {
  if (!isCancel(error)) {
    //Issues a callback all the way to the `sendEvent`

    failureCallback?.(
      generateApiError(error as ApiError) as unknown as ErrorStorage
    )
    console.error(
      'Error thrown unparsed:',
      //@ts-expect-error
      error?.response?.data ? error?.response?.data : error
    )

    captureExceptionWithSentry(
      //@ts-expect-error
      error?.response?.data ? error?.response?.data : error
    )

    //Triggers the onError state transition in a xstate machine for invoked
    //services, by re-throwing the same error
    if (!skipRethrow) {
      throw error
    }
  }
}

/**
 * Uses dayjs to extract day, month and year from a date string and
 * returns an object or null if no date if provided or date is invalid
 */
const destructToNumbers = (value: string) => {
  if (!value || !dayjs(value).isValid()) {
    return null
  }

  return {
    day: dayjs(value).get('date'),
    month: dayjs(value).get('month'),
    year: dayjs(value).get('year'),
  }
}

/**
 * Returns the days in the month and year, using dayjs
 */
const daysInMonthAndYear = ({ month, year }: { month: number; year: number }) =>
  dayjs().year(year).month(month).daysInMonth()

const adjustDayIfFebruary = ({
  day,
  month,
  year,
  februaryMonthIndex,
}: {
  day: number
  month: number
  year: number
  februaryMonthIndex: number
}) => {
  if (month === februaryMonthIndex) {
    const daysInMonth = daysInMonthAndYear({ month, year })
    return Math.min(day, daysInMonth)
  }

  return day
}

/**
 * Filters an array by date range including the boundaries and returns the
 * filtered array from passed in `objectKey`, the object key can be nested fro
 * example `l1.l2.l3`
 */
const filterArrayByDateRange = <T>({
  arrayOfObjects,
  fromDate,
  toDate,
  objectKey,
}: FilterArrayByDateRangeParams<T>) => {
  return arrayOfObjects?.filter((item) => {
    const keys = objectKey.split('.')

    const date = keys.reduce(
      (obj: unknown, key) =>
        typeof obj === 'object' && obj !== null
          ? (obj as Record<string, unknown>)[key]
          : undefined,
      item as unknown
    )
    if (typeof date !== 'string') return false
    return date >= fromDate && date <= toDate
  })
}

/**
 * Returns an object of `from` and `to` values as YYYY-MM-DD on passed in range
 * for day, year, month. The range type can be `today`, `yesterday` so on
 */
const rangeTypeToRange = ({
  rangeType,
  dateType,
  filterTypes,
}: RangeTypeToRange) => {
  const dateObject = dayjs()

  if (rangeType === 'yesterday') {
    return {
      from: dateObject.subtract(1, 'day').format('YYYY-MM-DD'),
      to: dateObject.subtract(1, 'day').format('YYYY-MM-DD'),
    }
  }

  return {
    from: dateObject
      .subtract(filterTypes[rangeType], dateType)
      .format('YYYY-MM-DD'),
    to: dateObject.format('YYYY-MM-DD'),
  }
}

/**
 * Changes the app's language based on the passed in locale, as quick workaround
 */
const changeLanguageForCountry = (countryLocale: string) => {
  const lng: { [key: string]: string } = {
    pt: 'pt-BR',
    es: 'es-SV',
  }
  const localePt1 = countryLocale.split('-')[0] ?? ''

  i18n
    .changeLanguage(lng[localePt1] ?? 'en-US')
    .then()
    .catch((error) => {
      // No need to handle just warn that could not fetch geo location data
      console.warn('Could not change language', error)
    })
}

/** Detects the user's operating system based on the user agent. */
const useOS = () => {
  const userAgent = navigator.userAgent
  if (/iPad|iPhone|iPod/.test(userAgent)) return 'ios'
  if (/android/i.test(userAgent)) return 'android'
  return 'unknown'
}

/**
 * Returns a function that will open the app in the app store if the user is
 * on a mobile or tablet device, given the device type.
 */
const useOpenApp = (isMobileOrTablet: boolean) => {
  const os = useOS()
  return () => {
    if (!isMobileOrTablet) return
    if (os === 'unknown') return
    window.location.href = APP_LINKS[os]
  }
}

export {
  adjustAndConvertToAgeMonthString,
  adjustDayIfFebruary,
  askForBrowserPermission,
  birthAgeAndMonthsFromFutureDate,
  browserStorageEnabled,
  calculateRetirementValues,
  captureExceptionWithSentry,
  changeLanguageForCountry,
  checkForBrowserPermissionStatus,
  consoleWarningMessage,
  convertDateToClientLocale,
  copyToClipboard,
  dateDifference,
  daysInMonth,
  daysInMonthAndYear,
  debounce,
  deepEqual,
  destructToNumbers,
  detectDeviceType,
  differenceInPercentage,
  dobToYearsAndMonthsOld,
  faceTecCompatibleBrowser,
  filterArrayByDateRange,
  formatDate,
  formatFileSize,
  generateApiError,
  generateRange,
  generateUniqueId,
  getCountryInformation,
  getCountrySvgIcon,
  getCurrentAge,
  getDetectedIpCountry,
  getGreeting,
  getIpGeoLocation,
  getStateInformation,
  getSupportedTontinatorParams,
  hasStrongAuth,
  i18nTrans,
  i18Translation,
  idVerificationAlertStatus,
  isLeapYear,
  logForecastParams,
  modifyContributionAgeAndRetirementAge,
  monthNumberToString,
  numberFormatter,
  parseLiteParams,
  parseLiteReferralData,
  parseMagicLink,
  parseParamsForEmail,
  parsePhoneNum,
  pickProperty,
  rangeTypeToRange,
  renderNominalBalance,
  safelyParseJSON,
  sanitizeInputValue,
  secondsToMinutes,
  selectDomElement,
  showExamplePhoneNumber,
  throttle,
  useOpenApp,
  useOS,
  validateInputWithError,
  verifiedPersonalDetails,
  writeToConsoleAndIssueAlert,
}
