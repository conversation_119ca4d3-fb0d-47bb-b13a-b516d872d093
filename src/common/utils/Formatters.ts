/**
 * Formats a postal code to specified format.
 */
export function formatPostalCode(
  postalCode: string,
  format: [number, number]
): string {
  let firstLen: number
  let secondLen: number

  if (Array.isArray(format)) {
    ;[firstLen, secondLen] = format
  } else {
    firstLen = format
    secondLen = 0
  }

  // Remove any existing hyphens
  const cleanCode = postalCode.replace(/-/g, '')

  if (secondLen === 0) {
    return cleanCode.slice(0, firstLen)
  }

  // Format with hyphen if needed
  if (cleanCode.length > firstLen) {
    return `${cleanCode.slice(0, firstLen)}-${cleanCode.slice(firstLen, firstLen + secondLen)}`
  }

  return cleanCode
}
