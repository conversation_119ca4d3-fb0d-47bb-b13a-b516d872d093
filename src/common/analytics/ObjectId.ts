// Object ids for the UI elements
type TontinatorScreen = 'tontinator'
type ComparePlansScreen = 'compare_plan'
type RegisterScreen = 'register'
type EmailLoginModal = 'email_login_modal'
type ComparePlan1 = 'plan1'
type ComparePlan2 = 'plan2'
type SuccessModal = 'success_modal'
type ReferralLite = 'referral_lite'
type ChartId = TontinatorScreen | ComparePlansScreen
type AuthLogin = 'email' | 'biometrics'
type AuthSignUp = 'referral_code' | 'no_referral_code'
type AuthLogout = 'mtl'
type ShareMedium =
  | 'facebook'
  | 'x'
  | 'whatsapp'
  | 'sms'
  | 'viber'
  | 'telegram'
  | 'email'
  | 'clipboard'

type TermAndConditionsModal = 'terms_and_conditions_modal'
type Accessibility = 'accessibility'

// Chunk Id for buttons, needs to be only scoped
type ButtonIdChunk =
  | 'open_compare_plan'
  | 'close_compare_plan'
  | 'open_register_screen'
  | 'login_text'
  | 'sex_male'
  | 'sex_female'
  | 'choose_plan1'
  | 'choose_plan2'
  | 'back'
  | 'annuity'
  | 'percentage'
  | 'inflation'
  | 'breakeven'
  | 'submit_signup'
  | 'send_login_email'
  | 'dismiss'
  | 'resend_email'
  | 'slider_page'
  | 'close'
  | 'tac_text'
  | 'close_via_backdrop'
  | 'navigate_to_referral'
  | 'update_lite_plan'
  | 'font_size_increase'
  | 'font_size_default'
  | 'open_menu'
  | 'close_menu'
  | 'enable_ira'
  | 'enable_ttf'

type SliderIdChunk =
  | 'slider_increment_age'
  | 'slider_increment_age_box'
  | 'slider_decrement_age'
  | 'slider_increment_income_start_age'
  | 'slider_decrement_income_start_age'
  | 'slider_increment_income_start_age_box'
  | 'slider_increment_onetime_contribution'
  | 'slider_decrement_onetime_contribution'
  | 'slider_increment_onetime_box'
  | 'slider_increment_monthly_contribution'
  | 'slider_decrement_monthly_contribution'
  | 'slider_increment_monthly_box'
  | 'slider_age'
  | 'slider_income_start_age'
  | 'slider_onetime_contribution'
  | 'slider_monthly_contribution'

type TextInputIdChunk =
  | 'first_name'
  | 'last_name'
  | 'email'
  | 'referral_code'
  | 'search'
type CheckboxIdChunk = 'tac' | 'email_news'
type LegendIdChunk = 'legend' | 'legend_item_annuity' | 'legend_item_bank'
type DropdownIdChunk = 'investment_strategy'

type ButtonUiId =
  | `${TontinatorScreen}_${ButtonIdChunk}`
  | `${ComparePlansScreen}_${ButtonIdChunk}`
  | `${RegisterScreen}_${ButtonIdChunk}`
  | `${SuccessModal}_${ButtonIdChunk}`
  | `${EmailLoginModal}_${ButtonIdChunk}`
  | `${ReferralLite}_${ButtonIdChunk}`
  | `${TermAndConditionsModal}_${ButtonIdChunk}`
  | `${Accessibility}_${ButtonIdChunk}`

type SliderUiId =
  | `${TontinatorScreen}_${SliderIdChunk}`
  | `${ComparePlan1}_${SliderIdChunk}`
  | `${ComparePlan2}_${SliderIdChunk}`

type TextInputUiId =
  | `${RegisterScreen}_${TextInputIdChunk}`
  | `${EmailLoginModal}_${TextInputIdChunk}`

type CheckboxUiId =
  | `${RegisterScreen}_${CheckboxIdChunk}`
  | `${TermAndConditionsModal}_${CheckboxIdChunk}`
type LegendUiId = `${TontinatorScreen}_${LegendIdChunk}`
type DropdownUiId =
  | `${TontinatorScreen}_${DropdownIdChunk}`
  | `${ComparePlan1}_${DropdownIdChunk}`
  | `${ComparePlan2}_${DropdownIdChunk}`

/**
 * Object ids for clarifying events or on what object was interacted.
 *
 *  Follows the scheme: `{scope}_{object}_{action}`
 *
 * `scope` - the screen, page or form
 * `object` - the object that was interacted with can be a button, slider or any ui
 * element
 * `action` - the action that was performed like clicked, viewed, moved
 */
type ObjectIdProperty =
  | ButtonUiId
  | SliderUiId
  | TextInputUiId
  | LegendUiId
  | ChartId
  | ShareMedium
  | AuthLogin
  | AuthSignUp
  | AuthLogout
  | CheckboxUiId
  | DropdownUiId

export type {
  ButtonUiId,
  ChartId,
  CheckboxUiId,
  ObjectIdProperty,
  SliderUiId,
  TextInputUiId,
}
