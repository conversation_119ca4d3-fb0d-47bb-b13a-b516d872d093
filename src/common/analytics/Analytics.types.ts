import {
  AuthEvent,
  ButtonEvent,
  CarouselEvent,
  CheckboxEvent,
  ForecastParamsEvent,
  GraphEvent,
  InputFieldEvent,
  LegendEvent,
  NavItemEvent,
  PageEvent,
  PensionPlan,
  ReferralLinkEvent,
  RegisterFormEvent,
  SliderEvent,
} from './EventData'
import { ObjectIdProperty } from './ObjectId'

type EventToTrack =
  | PageEvent
  | LegendEvent
  | ErrorEvent
  | CheckboxEvent
  | ButtonEvent
  | CarouselEvent
  | SliderEvent
  | ReferralLinkEvent
  | GraphEvent
  | RegisterFormEvent
  | NavItemEvent
  | ForecastParamsEvent
  | InputFieldEvent
  | AuthEvent
  | PensionPlan

type TrackProperties = {
  description?: string
  object_id?: ObjectIdProperty
  object_value?: unknown
  url_type?: 'internal' | 'external'
  source?: 'webapp' | 'website'
  device?: 'desktop' | 'mobile' | 'tablet'
  label?: string
  authToken?: string
}

type TrackRequest = {
  event: EventToTrack
  properties: TrackProperties
}

type TrackSliderButtonIds = Partial<{
  incBtnId: ObjectIdProperty
  incBtnDesc: string
  incBtnBoxId: ObjectIdProperty
  incBtnBoxDesc: string
  decBtnId: ObjectIdProperty
  decBtnDesc: string
  decBtnBoxId: ObjectIdProperty
}>

type TrackSliderButtonWithId = TrackSliderButtonIds & {
  sliderId?: ObjectIdProperty
}

export type {
  EventToTrack,
  TrackProperties,
  TrackRequest,
  TrackSliderButtonIds,
  TrackSliderButtonWithId,
}
