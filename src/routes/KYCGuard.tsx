import { ReactNode } from 'react'
import Layout from '../common/components/Layout'
import { useTranslate } from '../common/hooks/useTranslate'
import { useAccountService } from '../features/authentication/hooks/useAccountService'
import MissingInformation from '../features/authentication/pages/MissingInformation'
import { PUBLIC } from './Route'
import { KycProtected } from './Route.type'

/**
 * Checks if the user has completed L1 KYC, if not, renders a
 * `<MissingInformation />` page for the user to complete the kyc checklist
 */
const KYCGuard = ({
  children,
  requestKyc,
}: {
  children: ReactNode
  requestKyc?: KycProtected
}) => {
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  if (requestKyc) {
    if (
      requestKyc?.l1 &&
      !requestKyc?.l2 &&
      user_details?.kyc_status?.L1?.passed_level
    ) {
      return children
    }

    if (requestKyc.l2 && user_details?.kyc_status?.L2?.passed_level) {
      return children
    }

    return (
      <Layout
        pageTitle={t('MISSING_DETAILS.EXPLAINER_TITLE')}
        containerWidth="small"
        navigateTo={PUBLIC.GO_TO_PREV_PAGE}
      >
        <MissingInformation requestKyc={requestKyc} />
      </Layout>
    )
  }

  return children
}

export default KYCGuard
