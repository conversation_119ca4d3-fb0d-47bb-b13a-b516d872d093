type LanguageConfig = {
  readonly value: string
  readonly name: string
  readonly icon: string
  readonly fullName: string
}

type LocizeConfig = {
  readonly projectId: string
  readonly apiKey: string | undefined
  readonly version: 'latest'
  readonly referenceLng: string
}

type LocalStorageCacheConfig = {
  readonly expirationTime: number
}

export type { LanguageConfig, LocalStorageCacheConfig, LocizeConfig }
