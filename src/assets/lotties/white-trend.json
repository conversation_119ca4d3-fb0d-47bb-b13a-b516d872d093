{"v": "5.5.7", "meta": {"g": "LottieFiles AE 0.1.20", "a": "", "k": "", "d": "", "tc": ""}, "fr": 30, "ip": 0, "op": 30, "w": 200, "h": 200, "nm": "Comp 1", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Arrow shape", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [0.35]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0.35]}, "t": 22, "s": [50]}, {"t": 29, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": -0.078, "ix": 10}, "p": {"a": 0, "k": [150.226, 78.785, 0], "ix": 2}, "a": {"a": 0, "k": [50.125, -21, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[28.25, -42], [70, -0.125], [70, -41.75]], "o": [[28.25, -42], [70, -0.125], [70, -41.75]], "v": [[28.25, -42], [70, -0.125], [70, -41.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false, "_render": true}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false, "_render": true}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform", "_render": true}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "_render": true}], "ip": 0, "op": 321, "st": 0, "bm": 0, "completed": true}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "<PERSON> Shape Layer 1: Path 1 [1.1]", "cl": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar pathToTrace = thisComp.layer('arrow line')('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\nvar progress = $bm_div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathTan = pathToTrace.tangentOnPath(progress);\n$bm_rt = radiansToDegrees(Math.atan2(pathTan[1], pathTan[0]));"}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('arrow line');\nvar progress = $bm_div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.667], "y": [0.333]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0.333]}, "t": 20, "s": [50]}, {"t": 30, "s": [100]}], "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 321, "st": 0, "bm": 0, "completed": true}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "arrow line", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-64, 36], [-18.125, -10], [9.875, 18], [50.375, -22.625]], "o": [[-64, 36], [-18.125, -10], [9.875, 18], [50.375, -22.625]], "v": [[-64, 36], [-18.125, -10], [9.875, 18], [50.375, -22.625]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false, "_render": true}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 13, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "_render": true}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform", "_render": true}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "_render": true}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Trace Shape Layer 1: Path 1 [1.1]').effect('Trace Path')('Progress');"}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false, "_render": true}], "ip": 0, "op": 321, "st": 0, "bm": 0, "completed": true}], "markers": [], "__complete": true}