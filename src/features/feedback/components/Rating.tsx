import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/Rating.module.scss'
import RatingButtons from './RatingButtons'

/**
 * Renders a container with emojis for the net promoter score buttons
 */
const Rating = ({ setRating }: { setRating: (id: number) => void }) => {
  const t = useTranslate()

  return (
    <section className={style.rating}>
      <RatingButtons onClick={(index) => setRating(index)} />
      <article className={style['rating__emojis']}>
        <div className={style['rating__emojis-text-emoji']}>
          <Icon
            fileName={ASSET.iconnotlikelyemojiurey}
            className={style['rating__emoji-icon']}
          />
          <p>{t('FEEDBACK.RATING_LOW_TEXT')}</p>
        </div>
        <div className={style['rating__emojis-text-emoji']}>
          <Icon
            fileName={ASSET.iconerylikelyemojiurey}
            className={style['rating__emoji-icon']}
          />
          <p>{t('FEEDBACK.RATING_HIGH_TEXT')}</p>
        </div>
      </article>
    </section>
  )
}

export default Rating
