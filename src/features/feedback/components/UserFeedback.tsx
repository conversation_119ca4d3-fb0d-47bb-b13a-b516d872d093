import { t } from 'i18next'
import { useState } from 'react'
import { toast } from 'react-toastify'
import Button from '../../../common/components/Button'
import Icon from '../../../common/components/Icon'
import TextInput from '../../../common/components/TextInput'
import ToastMessage from '../../../common/components/ToastMessage'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import style from '../style/UserFeedback.module.scss'
import { UserFeedbackProps } from '../types/Feedback.types'
import Rating from './Rating'

/**
 * Renders feedback modal on desktop and a feedback card on mobile, collects user
 * feedback and sends it to the auth machine. The feedback is shown in the
 * following basecamp project: https://3.basecamp.com/5235135/buckets/********/message_boards/**********
 */
const UserFeedback = ({
  ratingOpenDefault = false,
  closeModal,
  onSuccessfulSubmit,
}: UserFeedbackProps) => {
  const {
    rating,
    setRating,
    textFeedback,
    setTextFeedback,
    submitFeedback,
    openRating,
    setOpenRating,
    submittedFeedback,
    currentState,
  } = useUserFeedback({
    ratingOpenDefault,
    onSuccessfulSubmit,
  })

  if (submittedFeedback) {
    return null
  }

  return (
    <main className={style.userFeedback}>
      <section
        className={style['userFeedback__container']}
        onClick={
          ratingOpenDefault
            ? undefined
            : () => setOpenRating((prevState) => !prevState)
        }
      >
        <h3 className={style['userFeedback__title']}>
          {t('USER_FEEDBACK.TITLE')}
        </h3>
        {!ratingOpenDefault && (
          <Icon
            fileName={ASSET.icononboardinarrowforward}
            className={style[`userFeedback--${openRating ? 'open' : 'closed'}`]}
          />
        )}
        {ratingOpenDefault && (
          //Only renders on desktop if the UserFeedback component is a modal
          <Icon
            className={style[`userFeedback__close-button`]}
            fileName={ASSET.iconaccountcloetoatmeae}
            onClick={closeModal}
          />
        )}
      </section>

      {openRating && <Rating setRating={setRating} />}

      {openRating && rating > 0 && (
        <>
          <TextInput
            value={textFeedback}
            onChange={setTextFeedback}
            isTextArea
            placeholder={t('FEEDBACK_MODAL.PLACEHOLDER_MESSAGE')}
          />
          <Button
            className={style[`userFeedback__submit-btn`]}
            onClick={submitFeedback}
            loading={currentState === 'SUBMITTING_USER_FEEDBACK'}
            textOnLoading={t('LOADING_TEXT')}
          >
            {t('FEEDBACK_MODAL.SUBMIT_BUTTON')}
          </Button>
        </>
      )}
    </main>
  )
}

/**
 * Handles the UI's state and submitting the feedback event to the auth machine
 */
const useUserFeedback = ({
  ratingOpenDefault = false,
  onSuccessfulSubmit,
}: UserFeedbackProps) => {
  const { send, currentState } = useAccountService()
  const t = useTranslate()
  const [openRating, setOpenRating] = useState(ratingOpenDefault)
  const [rating, setRating] = useState(0)
  const [textFeedback, setTextFeedback] = useState<undefined | string>(
    undefined
  )
  const [submittedFeedback, setSubmittedFeedback] = useState(false)

  const submitFeedback = () => {
    const handleSuccessfulSubmit = () => {
      setSubmittedFeedback(true)
      onSuccessfulSubmit?.()
      toast.success(
        <ToastMessage
          title={t('USER_FEEDBACK.SUBMITTED_TITLE')}
          content={t('USER_FEEDBACK.SUBMITTED_CONTENT')}
        />
      )
    }

    const handleFailedSubmit = () =>
      toast.error(<ToastMessage title={t('ERROR_GENERIC')} />)

    send({
      type: 'SUBMIT_USER_FEEDBACK',
      payload: {
        userFeedback: {
          rating,
          comment: textFeedback,
        },
        successCallback: handleSuccessfulSubmit,
        failureCallback: handleFailedSubmit,
      },
    })
  }

  return {
    send,
    currentState,
    openRating,
    setOpenRating,
    rating,
    setRating,
    textFeedback,
    setTextFeedback,
    submittedFeedback,
    setSubmittedFeedback,
    t,
    submitFeedback,
  }
}

export default UserFeedback
