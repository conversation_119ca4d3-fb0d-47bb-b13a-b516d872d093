import { useState } from 'react'
import style from '../style/Rating.module.scss'
import { RatingButtonsProps } from '../types/Feedback.types'

const NET_PROMOTER_SCORE = 10

/**
 * Renders net promoter score buttons with active styling state
 */
const RatingButtons = ({ onClick }: RatingButtonsProps) => {
  const [activeToggleButton, setActiveToggleButton] = useState(0)

  const onClickHandler = (index: number) => {
    onClick(index)
    setActiveToggleButton(index)
  }

  return (
    <ul className={style['rating__buttons']}>
      {Array.from({ length: NET_PROMOTER_SCORE }, (_, index) => {
        // Needed to render 1-10 and no 0-9 button
        const realIndex = index + 1
        return (
          // biome-ignore lint/a11y/useButtonType: <TODO: Why need button type rule?>
          <button
            onClick={() => onClickHandler(realIndex)}
            className={
              style[
                `rating__buttons--${activeToggleButton === realIndex ? 'active' : 'inactive'}`
              ]
            }
            key={`${index}net-score`}
          >
            {realIndex}
          </button>
        )
      })}
    </ul>
  )
}

export default RatingButtons
