@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define userFeedback */
.userFeedback {
  border: 1px solid colors.$blue-light;
  background-color: colors.$blue-faint !important;
  border-radius: variables.$rounded;
  padding: 0.625rem;

  &__container {
    @include mixins.flex-layout(row, space-between);
    padding: 0.625rem;
  }

  &__title {
    @include mixins.font-style;
    @include mixins.no-user-select;
    margin: 0;
  }

  &__close-button {
    cursor: pointer;
    margin-left: 2.5rem;
    margin-bottom: 2.5rem;
    filter: invert(73%) sepia(5%) saturate(4832%) hue-rotate(181deg) brightness(95%) contrast(103%);
  }

  &--closed {
    transform: rotate(270deg);
  }

  &--open {
    transform: rotate(90deg);
  }

  &__submit-btn {
    margin-top: 1.25rem;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    margin: variables.$mobile-spacing;
  }
}