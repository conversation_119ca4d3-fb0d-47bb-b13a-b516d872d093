@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define rating */
.rating {
  margin: 10px 0;
  padding: 5px;
  @include mixins.flex-layout(column);

  &__buttons {
    width: 100%;
    @include mixins.no-user-select;
    @include mixins.flex-layout(row, space-between);

    &--inactive {
      @include mixins.button(
        colors.$white,
        colors.$blue,
        1px solid colors.$blue-light,
        40px,
        29px,
        $font-size: variables.$font-size-s
      );
    }

    &--active {
      color: white;
      text-align: center;
      transform: scale(1.5);
      z-index: 99;
      @include mixins.button(
        colors.$blue,
        colors.$white !important,
        none,
        40px,
        29px,
        $font-size: variables.$font-size-m
      );
    }
  }

  &__emoji-icon {
    width: 20px;
    height: 20px;
  }

  &__emojis {
    width: 100%;
    margin-top: 15px !important;
    margin-left: 20px;
    margin-right: 20px;
    @include mixins.no-user-select;
    @include mixins.flex-layout(row, space-between);
    @include mixins.font-style($font-size: variables.$font-size-s);

    &-text-emoji {
      background-color: colors.$white;
      border-radius: variables.$rounded;
      padding: 0.125rem;
      @include mixins.flex-layout($gap: 5px);
    }
  }
}
