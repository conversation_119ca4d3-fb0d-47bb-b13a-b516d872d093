import dayjs from 'dayjs'
import {
  IncomeForecastParams,
  UnparsedForecastParams,
} from '../../../common/types/CommonTypes.types'
import { TontinatorUIParams } from '../../../common/types/SupportedCountries.types'
import {
  captureExceptionWithSentry,
  daysInMonth,
} from '../../../common/utils/UtilFunctions'
import { AgeMonth } from '../../CommonState.type'
import { DASHBOARD_CONSTANTS } from './consts'

/**
 * Calculates retirement data based on user's `contributionAge` and
 * `retirementAge`
 */
const calculateYearForRetirementAge = (
  contributionAge: AgeMonth,
  retirementAge: AgeMonth
) => {
  const { age: yearsOldOnRetirement, month: monthsOldOnRetirement } =
    retirementAge

  const { age: yearsOldNow, month: monthsOldNow } = contributionAge

  try {
    const currentYear = dayjs().year()
    const currentMonth = dayjs().month() + 1

    let retirementYear = yearsOldOnRetirement - yearsOldNow + currentYear
    let retirementMonth = monthsOldOnRetirement - monthsOldNow + currentMonth

    // if the retirement month is negative, add 12 and subtract 1 from the
    // retirement year
    if (retirementMonth < 0) {
      retirementMonth += DASHBOARD_CONSTANTS.DECEMBER
      retirementYear -= 1
    }

    // if the retirement month is zero, set it to 12 and subtract 1 from the
    // retirement year
    if (retirementMonth === 0) {
      retirementMonth = DASHBOARD_CONSTANTS.DECEMBER
      retirementYear -= 1
    }

    //Handles month overflow
    if (retirementMonth > 12) {
      retirementMonth -= 12
      retirementYear += 1
    }

    return {
      yearsOldOnRetirement,
      monthsOldOnRetirement,
      yearsOldNow,
      monthsOldNow,
      retirementYear,
      retirementMonth,
    }
  } catch (error) {
    captureExceptionWithSentry(error)
  }

  return undefined
}

/** Returns the number of days in the current month, using local time */
const daysCurrentMonth = () =>
  daysInMonth(new Date().getFullYear(), new Date().getMonth() + 1)

/** Returns the number of days in the current month, remaining days
 * until the next month and today's date, all this data is in `Number` format
 */
const daysUntilNextPayout = () => {
  const todayDate = new Date().getDate()
  return {
    currentDaysInMonth: daysCurrentMonth(),
    remaining: daysCurrentMonth() - todayDate,
    todayDate,
  }
}

const checkRetirementAgeIsBelowMin = (
  retirementAge: number,
  currentAge: number,
  minRetirementAge: number
): number => {
  const maxAge = Math.max(minRetirementAge, currentAge)
  return Math.max(retirementAge, maxAge)
}

/**
 * Checks if one time and monthly contributions are 0
 */
const contributionValuesAreZero = (
  oneTimeContribution: number,
  monthlyContribution: number
) => oneTimeContribution === 0 && monthlyContribution === 0

/**
 * Adjusts the retirement age and prevents it from going below minimum
 * retirement age
 */
const adjustRetirementAgeSlider = (
  currentAge: number,
  retirementAge: number,
  minRetirementAge: number,
  maxRetirementAge: number
) => {
  if (retirementAge < minRetirementAge) {
    return minRetirementAge
  }

  //Returns the max age, and does not allow the user to move the slider more
  //than the max
  if (retirementAge > maxRetirementAge && currentAge < maxRetirementAge) {
    return maxRetirementAge
  }

  return retirementAge
}

/**
 * Adjusts the current age inside the retirement age slider, and allows the
 * retirement slider to modify the current age slider increment or decrement
 */
const adjustCurrentAgeSliderInsideRetirementAgeSlider = (
  currentAge: number,
  retirementAge: number,
  minRetirementAge: number,
  maxRetirementAge: number
) => {
  //Moving the retirement slider also moves the contribution slider if the USA
  //MIN/MAX conditions are met
  if (currentAge >= maxRetirementAge && retirementAge > maxRetirementAge) {
    return retirementAge
  }

  //Allows the retirement slider to modify the currentAge slider also known as
  //both slider moving at the same time at the same value
  if (currentAge > retirementAge && currentAge > minRetirementAge) {
    return retirementAge
  }

  return currentAge
}

/**
 * Makes sure the retirement age is not less than the current age
 */
const adjustRetirementAgeViaCurrentAgeCall = (
  retirementAge: number,
  currentAge: number,
  minRetirementAge: number,
  maxRetirementAge: number
) => {
  if (retirementAge < minRetirementAge) {
    // biome-ignore lint/style/noParameterAssign: <TODO: Solve>
    retirementAge = minRetirementAge
  }

  if (currentAge > retirementAge) {
    return currentAge
  }

  //ONLY FOR USA WE NEED TO MODIFY THE BEHAVIOR FOR THE SLIDERS
  if (currentAge < maxRetirementAge && retirementAge > maxRetirementAge) {
    return maxRetirementAge
  }

  if (currentAge >= maxRetirementAge && retirementAge > maxRetirementAge) {
    return currentAge
  }

  return retirementAge
}

/**
 * Makes sure that contribution values are no 0 by adjusting the one time
 * contribution. Uses `TontinatorDefaultUIParams.json` to source the values
 */
const adjustOneTimeContribution = (
  monthlyContribution: number,
  oneTimeContribution: number,
  params: TontinatorUIParams
) => {
  if (contributionValuesAreZero(oneTimeContribution, monthlyContribution)) {
    return params.oneTimeContributionMinIfOnly
  }
  return oneTimeContribution
}

/**
 * Makes sure that contribution values are no 0 by adjusting the monthly
 * contribution
 */
const adjustMonthlyContribution = (
  monthlyContribution: number,
  oneTimeContribution: number,
  params: TontinatorUIParams
) => {
  if (contributionValuesAreZero(monthlyContribution, oneTimeContribution)) {
    return params.monthlyContributionMinIfOnly
  }
  return monthlyContribution
}
/**
 * Adjusts the contributions if the user is retired and makes sure the UI does
 * not send 0 monthly and 0 one time contribution which will be invalid
 */
const adjustContributions = ({
  isRetired,
  formData,
  params,
}: {
  isRetired: boolean
  formData: IncomeForecastParams
  params: TontinatorUIParams
}) => {
  const adjustedValues = {
    ...formData,
    monthlyContribution: isRetired ? 0 : formData.monthlyContribution,
  }

  if (
    isRetired &&
    contributionValuesAreZero(
      adjustedValues.monthlyContribution,
      adjustedValues.oneTimeContribution
    )
  ) {
    return {
      ...adjustedValues,
      oneTimeContribution: params.oneTimeContributionMinIfOnly,
    }
  }

  return adjustedValues
}

/**
 * Parses income params to tontinator request body.
 */
const parseIncomeForecastParams = ({
  contributionAge,
  monthlyContribution,
  oneTimeContribution,
  payoutAge,
  countryOfResidence,
  sex,
  isAuthenticated,
  strategy,
  writeDraftPlan,
}: UnparsedForecastParams) => {
  const hasContribution = Boolean(
    oneTimeContribution && oneTimeContribution > 0
  )

  const hasMonthlyContribution = Boolean(
    monthlyContribution && monthlyContribution > 0
  )

  if (payoutAge && (hasContribution || hasMonthlyContribution)) {
    const demographic_data = {
      demographic_data_country_of_residence: countryOfResidence,
      demographic_data_sex: sex,
      demographic_data_current_age: contributionAge,
      demographic_data_type: 'full',
    }

    const contributions = {
      monthly_amount: monthlyContribution,
      onetime_amount: oneTimeContribution,
      payout_age: payoutAge,
    }

    //If all params are in place init an contribution params object
    const incomeForecastRequestBody = {
      contribution_allocations: strategy,
      contributions,
    }

    if (isAuthenticated) {
      return {
        ...incomeForecastRequestBody,
        write_draft_plan: writeDraftPlan ?? false,
      }
    }

    return {
      ...incomeForecastRequestBody,
      ...demographic_data,
      write_draft_plan: writeDraftPlan ?? false,
    }
  }

  throw new Error(
    `Failed to parse income params, missing params, got:${JSON.stringify({
      contributionAge,
      monthlyContribution,
      oneTimeContribution,
      payoutAge,
      countryOfResidence,
      sex,
    })}`
  )
}

export {
  adjustContributions,
  adjustCurrentAgeSliderInsideRetirementAgeSlider,
  adjustMonthlyContribution,
  adjustOneTimeContribution,
  adjustRetirementAgeSlider,
  adjustRetirementAgeViaCurrentAgeCall,
  calculateYearForRetirementAge,
  checkRetirementAgeIsBelowMin,
  contributionValuesAreZero,
  daysUntilNextPayout,
  parseIncomeForecastParams,
}
