@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define formProgress */
.formProgress {
  @include mixins.flex-layout(column);

  &__divider {
    margin-bottom: variables.$margin-bottom-from-top-divider;
  }

  &__steps {
    @include mixins.font-style(
      $font-size: variables.$font-size-large,
      $font-weight: variables.$font-semibold
    );

    margin-bottom: 0.625rem;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    display: none;
  }
}
