@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

/** @define param-modes */
.param-modes {
  @include mixins.flex-layout($justify-content: left);
  width: 21.875rem;
  gap: 5px;
  margin-bottom: 0.3125rem;

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
  }

  &--active {
    filter: colors.$blue-image;
  }
  &--inactive {
    filter: colors.$dark-gray-image;
  }
}
