@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define tontinator-page */
.tontinator-page {
  position: relative;

  &__graph-title {
    display: block;
    font-size: variables.$font-size-xlarge !important;
    margin-top: 1.25rem !important;
  }
  &__disclaimer {
    @include mixins.font-style(
      $font-size: variables.$font-size-xxs,
      $color: colors.$gray-medium-lighter
    );
    text-align: center;
  }

  &__bottom-buttons {
    @include mixins.flex-layout;
    width: 100%;
    gap: 10px;
    margin: 0.625rem 0;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__bottom-buttons {
      flex-wrap: wrap;
      margin-bottom: 35px;
    }

    &__graph-title {
      display: block;
      font-size: variables.$font-size-l !important;
    }
  }
}
