@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';



/** @define tontinatorDashboard */
.tontinatorDashboard {
  padding: 25px 25px 2px 2px;

  &--full {
    overflow-y: scroll;
    height: variables.$full-v-dashboard-height;
    padding: 20px 5px 0 1px;
    width: 100%;
    flex-basis: 60%;

    @media only screen and (max-width: variables.$mobile-devices) {
      height: fit-content;
      padding-top: 0;
      flex-basis: 100%;
    }
  }

  &__divider {
    margin-top: 0;
    margin-bottom: 1.25rem;

    &--hidden {
      margin-top: 20px;
      visibility: hidden;
    }
  }

  &__banner-container {
    margin: 15px 0 10px 0;
  }

  &__section {
    border-radius: 5px;
    background-color: colors.$white;
    padding: 10px;
    box-shadow: variables.$chart-box-shadow;
  }

  &__error-text {
    text-align: center;
  }

  &__graph-title {
    display: block;
    font-size: variables.$font-size-s !important;
  }

  &__payout-container {
    @include mixins.flex-layout;
    margin-bottom: 1.25rem;
    margin-top: 1.875rem;
  }

  &__payout-center {
    max-width: 700px;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    padding: 0 2px 2px 2px;
    width: 100%;
    max-width: 100%;

    &__divider {
      display: none;
    }

    &__section {
      margin-top: 25px;
    }

    &__payout-center {
      max-width: 100% !important;
    }

    &__banner-message {
      margin: 1.25rem 0;
    }

    &__text {
      font-weight: variables.$font-bold;
    }

  }
}