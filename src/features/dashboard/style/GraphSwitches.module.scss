@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define graphSwitches */
.graphSwitches {
  @include mixins.flex-layout;
  margin-bottom: 0.625rem;
  z-index: 9999;
  margin-top: 3.125rem;

  &__container {
    @include mixins.flex-layout;
    width: 100%;
    gap: 1.875rem;

    // Switches variants
    &--button {
      @include mixins.flex-layout;
      width: 100%;
      gap: 0.625rem;
    }

    @media only screen and (max-width: variables.$mobile-devices) {
      gap: 0.3125rem;
    }
  }
}
