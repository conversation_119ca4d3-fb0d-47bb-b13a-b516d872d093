@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

@mixin bottom-cta-lite-layout($height: auto) {
  width: 700px;
  height: $height;
  margin-top: 30px;

  @include mixins.flex-layout($align-items: flex-start,
    $flex-direction: row,
    $gap: 20px);

  @media only screen and (max-width: variables.$mobile-devices) {
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 20px;
  }
}

@mixin mascot-icon($top: 0, $left: 0) {
  width: 100px;
  height: 100px;
  position: absolute;
  top: $top;
  left: $left;

  @media only screen and (max-width: variables.$mobile-devices) {
    display: none;
  }
}

/** @define bottom-cta-lite-layout */
.bottom-cta-lite-layout {
  position: relative;

  @include bottom-cta-lite-layout;

  &__mascot {
    @include mascot-icon($top: 80px, $left: 100px);

    &--auth {
      @include mascot-icon($top: 100px, $left: -100px);
    }
  }

  &__mascot-1 {
    @include mascot-icon($top: 100px, $left: 500px);

    &--auth {
      @include mascot-icon($top: 100px, $left: 700px);
    }
  }

  &__cta-to-slider {
    padding: 0 1rem;
    margin-top: 3.125rem;
  }

  &__buttons {
    width: 400px;

    // Not present on mobile
    &-back {
      width: 100px;
    }

    @media only screen and (max-width: variables.$mobile-devices) {
      margin-top: 20px;
      width: 100%;
    }
  }
}