@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

// Contains svg elements style and all chart's in containers where
// D3 svg drawn chart is inserted

.legend-item-text {
  @include mixins.font-style($font-size: variables.$font-size-m);
}

.annotation-text {
  @include mixins.font-style($font-size: variables.$font-size-xs);
}

.tick {
  text {
    @include mixins.font-style($font-size: variables.$font-size-xs,
      $font-weight: variables.$font-semibold );
  }

  line {
    color: colors.$white-faint;
    shape-rendering: crispEdges;
  }
}

.x-axis-cnb-months,
.x-axis-progress-graph-age {
  color: colors.$white-faint !important;
}

.forecast-graph-container {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 350px;

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    height: 300px;
  }
}

.progress-graph-container {
  padding: 0.5rem 0;
  width: 100%;
  height: 400px;

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    height: 300px;
    width: 100%;
  }
}

.nominal-balance-container {
  width: 100%;
  height: 400px;

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    height: 350px;
  }
}