@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define home */
.home {
  @include mixins.flex-layout(column);
  @include mixins.yellow-sun-bg-top-left($background: url('../../../assets/icon-onboarding_happy-sun-desktop.svg'));

  &__container {
    height: 90vh;
    overflow-y: scroll;
  }

  &__onboarding-buttons {
    @include mixins.flex-layout(row, space-between, null);
    gap: 1.25rem;
    width: 39rem;
    margin: 1.25rem 0;
  }

  &__bottom-section {
    @include mixins.flex-layout(column);
    margin-bottom: 20px;
  }

  &__btn-divider {
    width: 700px !important;
    margin-top: 3.125rem;

    //Small desktop screens
    @media screen and (min-width: 1025px) and (max-width: 1280px) {
      margin-top: 30px;
    }
  }

  &__language-dropdown {
    width: 7rem;
    height: 1rem;
    padding: 0;
    font-size: 0.875rem;
    margin-left: auto;
    margin-right: 1.25rem;
  }

  &__login-btn {
    display: none;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.flex-layout(column, $align-items: normal);
    background: none;
    background-position: initial;

    &__btn-divider {
      display: none;
    }

    &__bottom-section {
      width: 100%;
      position: fixed;
      bottom: 0;
      margin-bottom: 0;
    }

    &__login-btn {
      @include mixins.flex-layout(row, flex-end, null);
      padding-right: 1.875rem;
      padding-top: 1rem;
      padding-bottom: 1rem;
    }

    &__onboarding-buttons {
      flex-direction: column;
      background-color: colors.$white;
      padding: 1.25rem 0.9375rem;
      margin: 0;
      width: 100%;
    }
  }
}