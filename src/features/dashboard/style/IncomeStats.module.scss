@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

@mixin container($bg-color: colors.$white) {
  @include mixins.flex-layout($align-items: flex-end);
  background-color: colors.$white;
  border-radius: variables.$rounded-l;
  padding: 15px;
  width: 100%;
  gap: 40px;

  @media only screen and (max-width: variables.$mobile-devices) {
    background-color: colors.$white;
    width: 100%;
    max-width: 100%;
    gap: 5px;
  }
}

/** @define income-stats */
.income-stats {
  margin-top: 10px;
  width: 100%;
  @include mixins.flex-layout;
  @include mixins.no-user-select;

  &__container {
    @include container;

    &--grey {
      @include container(colors.$gray-cloud);
    }

    &--blue-faint {
      @include container(colors.$blue-faint);
    }

    &--yellow-faint {
      @include container(colors.$yellow-faint);
    }
  }
}

@mixin extended-container($bg-color: colors.$white) {
  @include mixins.flex-layout(column);
  background-color: colors.$white;
  border-radius: variables.$rounded-l;
  border: 2px solid $bg-color;

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
  }
}

@mixin explainer-text($bg-color: colors.$white) {
  text-align: left;
  background-color: $bg-color;
  padding: 5px 15px;
  border-bottom-right-radius: variables.$rounded-l;
  border-bottom-left-radius: variables.$rounded-l;

  @include mixins.no-user-select;
  @include mixins.font-style($font-size: variables.$font-size-s);

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
  }
}

/** @define income-stats-extended */
.income-stats-extended {
  @include mixins.flex-layout($align-items: flex-start);
  gap: 0.625rem;
  box-shadow: 0px 4px 4px 0px #00000040;
  position: relative;
  border-radius: variables.$rounded-l;



  &__expand-toggle {
    background-color: colors.$white;
    position: absolute;
    top: -10px;
    right: 0;
    cursor: pointer;
    width: 25px;
    height: 25px;
  }

  &__container {

    // border variants
    &--blue-faint {
      @include extended-container(colors.$blue-light);
    }

    &--yellow-faint {
      @include extended-container(colors.$yellow-light);
    }
  }

  &__bottom-container {
    @include mixins.flex-layout;
    @include mixins.no-user-select;
    gap: 5px;
  }

  &__strategy {
    position: absolute;
    background-color: colors.$white;
    padding: 0 10px;
    top: -16px;
    left: 30px;
    @include mixins.font-style($font-size: variables.$font-size-ml,
      $font-weight: variables.$font-semibold );

    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.font-style($font-size: variables.$font-size-m,
        $font-weight: variables.$font-semibold );
    }
  }

  &__explainer {
    &--blue-faint {
      @include explainer-text(colors.$blue-faint);
    }

    &--yellow-faint {
      @include explainer-text(colors.$yellow-faint);
    }
  }
}