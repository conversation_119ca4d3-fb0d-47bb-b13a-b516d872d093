@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

@mixin main-content($padding-top: variables.$split-layout-top-padding,
  $padding-left: variables.$dashboard-main-content,
  $padding-right: variables.$dashboard-main-content) {
  flex-basis: 100%;
  width: 100%;
  overflow-y: scroll;
  background-color: colors.$white;
  height: variables.$mytt-dashboard-layout-height;
  padding-top: $padding-top;
  padding-left: $padding-left;
  padding-right: $padding-right;
  margin-right: $padding-right;
}

/** @define tontine-dashboard-layout */
.tontine-dashboard-layout {
  //Triple split layout used in the dashboard section of my tontine
  height: variables.$mytt-dashboard-layout-height;
  background: linear-gradient(to right,
      colors.$blue-faint 50%,
      colors.$white 50%);
  @include mixins.flex-layout;

  &__container {
    @include mixins.flex-layout;
    width: variables.$dashboard-container-width;
    height: variables.$mytt-dashboard-layout-height;
  }

  &__nav {
    height: variables.$mytt-dashboard-layout-height;
    background-color: colors.$blue-faint;
    overflow-y: scroll;
    flex-basis: 15%;
    gap: 20px !important;
    padding-top: variables.$dashboard-nav-spacing;
    padding-bottom: variables.$dashboard-nav-spacing;
    padding-right: variables.$dashboard-nav-spacing;
    margin-left: variables.$dashboard-nav-spacing;
    padding-left: variables.$dashboard-nav-spacing;
    margin-bottom: 50px;
    margin-top: 50px;
    @include mixins.flex-layout(column, none);
    @include mixins.dashboard-balance(column, $gap: 20px);
  }

  &__main-content {
    @include main-content;

    &--no-padding {
      @include main-content($padding-top: 0,
        $padding-left: 0,
        $padding-right: 0);
    }
  }


  @media only screen and (max-width: variables.$mobile-devices) {
    height: 100vh;

    &__container {
      width: 100%;
      @include mixins.flex-layout;
    }

    &__nav,
    &__main-content {
      width: 100%;
      padding: 0;
      flex-basis: 100%;
      height: 100vh;
      margin: 0;
    }
  }
}