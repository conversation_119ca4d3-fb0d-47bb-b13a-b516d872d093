@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

.nominalBalancePage {
  &__divider-bottom {
    margin: 1.875rem 0;
  }
  .page-layout {
    overflow-y: visible;
  }

  &__navigation-btns {
    @include mixins.flex-layout;
    margin-bottom: variables.$bottom-layout-btns-margin;
    :first-child {
      width: 340px;
    }
    > :last-child {
      display: none;
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__divider-bottom {
      display: none;
    }
    .page-layout__container {
      margin-right: 0;
    }

    &__navigation-btns {
      display: block;
      position: fixed;
      bottom: 0;
      left: 16px;
      right: 16px;

      :first-child {
        width: 100%;
        margin-bottom: 0.625rem;
      }

      :last-child {
        display: initial;
      }
    }
  }
}
