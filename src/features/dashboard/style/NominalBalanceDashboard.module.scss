@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define nominalBalanceDashboard */
.nominalBalanceDashboard {
  @include mixins.flex-layout(column, $align-items: null);
  background-color: colors.$white !important;
  border-radius: variables.$rounded;
  padding-right: 40px;
  width: 95%;
  height: 400px;

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    height: 350px;
    padding: 1.25rem;
    margin-top: 2.5rem;
    &__legend {
      @include mixins.flex-layout(
        column,
        flex-start !important,
        flex-start !important
      );
    }
  }
}
