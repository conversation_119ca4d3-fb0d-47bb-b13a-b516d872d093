/* stylelint-disable */
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

/** @define sandbox-dashboard */
.sandbox-dashboard {
    &__dashboards {
        @include mixins.flex-layout($align-items: flex-start, $justify-content: space-between, $gap: 0.625rem);

        &--public {
            @include mixins.flex-layout($align-items: flex-start, $justify-content: center, $gap: 3.125rem);
        }
    }

    &__investment-strategy {
        height: 4.125rem
    }

    &__bottom-layout {
        @include mixins.flex-layout;
    }

    &__buttons {
        margin-top: 1.25rem;
        width: 700px;

        @media only screen and (max-width: variables.$mobile-devices) {
            width: 100%;
            padding: 0 0.625rem;

            @include mixins.flex-layout(column, $gap: 0.9375rem);
        }

    }

}