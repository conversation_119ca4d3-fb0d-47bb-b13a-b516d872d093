@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define myTontineDashboard */
.myTontineDashboard {
  @include mixins.dashboard-balance;

  &__card {
    margin-top: variables.$mytt-dashboard-element-spacing;
  }

  &__header {
    margin-top: variables.$mytt-dashboard-element-spacing;
    text-align: center;
  }

  &__top-section,
  &__bottom-section {
    display: none;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    padding-bottom: variables.$mobile-bottom-padding;

    &__top-section,
    &__bottom-section {
      @include mixins.flex-layout(
        column,
        center,
        unset,
        variables.$mytt-dashboard-element-spacing
      );
    }
  }
}
