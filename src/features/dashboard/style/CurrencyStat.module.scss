@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define currency-stat */
.currency-stat {
  flex-basis: 50%;
  @include mixins.flex-layout($flex-direction: column);

  &__label {
    @include mixins.font-style($font-weight: variables.$font-semibold,
      $line-height: none,
      $font-size: variables.$font-size-s );
    margin-bottom: 0.625rem;
    text-transform: capitalize;

    @media screen and (max-width: variables.$mobile-iphonex) {
      @include mixins.font-style($font-weight: variables.$font-semibold,
        $line-height: none,
        $font-size: variables.$font-size-xs );
    }
  }

  &__amount {
    @include mixins.font-style($color: colors.$blue,
      $font-size: variables.$font-size-large,
      $font-weight: variables.$font-semibold );

    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.font-style($color: colors.$blue,
        $font-size: variables.$font-size-m,
        $font-weight: variables.$font-semibold );
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
  }
}