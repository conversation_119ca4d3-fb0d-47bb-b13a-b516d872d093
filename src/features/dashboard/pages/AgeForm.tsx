import OnboardingFormLayout from '../components/OnboardingFlowLayout'
import TontinatorInputs from '../components/TontinatorInputs'
import style from '../style/OnboardingForm.module.scss'
import { AgeFormProps } from '../types/CalculatorForms.types'

/**
 * AgeForm step in the onboarding flow.
 * Lets users set their current and retirement age before proceeding.
 */
const AgeForm = ({
  formHeaderText,
  formData,
  goToStep,
  progress,
  setFormData,
}: AgeFormProps) => {
  return (
    <OnboardingFormLayout
      formHeaderText={formHeaderText}
      progress={progress}
      onNext={() => goToStep('contributionForm')}
      onBack={() => goToStep('sexForm')}
    >
      <TontinatorInputs
        hideContributionSliders
        setFormData={setFormData}
        formData={formData}
        contributionSlidersClassName={style['onboarding-form__spacing']}
      />
    </OnboardingFormLayout>
  )
}

export default AgeForm
