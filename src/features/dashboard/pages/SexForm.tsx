import CountryDropdown from '../../../common/components/CountryDropdown'
import SelectSex from '../../../common/components/SelectSex'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { SexType } from '../../../common/types/CommonTypes.types'
import { PUBLIC } from '../../../routes/Route'
import OnboardingFormLayout from '../components/OnboardingFlowLayout'
import { SexFormProps } from '../types/CalculatorForms.types'

/**
 * SexForm step in the onboarding flow.
 * Users select their sex and country of residence before proceeding.
 */
const SexForm = ({
  formHeaderText,
  progress,
  setFormData,
  goToStep,
  formData,
}: SexFormProps) => {
  const t = useTranslate()
  const navigate = useCustomNavigation()

  const handleSexSetting = (sex: SexType) => {
    setFormData((previousData) => ({
      ...previousData,
      sex,
    }))
  }

  const handleCountrySetting = (countryOfResidence: string) => {
    setFormData((previousData) => ({
      ...previousData,
      countryOfResidence,
    }))
  }

  return (
    <OnboardingFormLayout
      formHeaderText={formHeaderText}
      progress={progress}
      onNext={() => goToStep('ageForm')}
      onBack={() => navigate(PUBLIC.HOME)}
      nextButtonDisabled={!(formData?.sex && formData?.countryOfResidence)}
    >
      <SelectSex
        sex={formData?.sex}
        setSex={handleSexSetting}
        label={t('SEX_FORM.SELECT_SEX_LABEL')}
      />

      <CountryDropdown
        value={formData?.countryOfResidence ?? ''}
        onChange={handleCountrySetting}
        label={t('INPUT_LABEL.COUNTRY')}
        optional
      />
    </OnboardingFormLayout>
  )
}

export default SexForm
