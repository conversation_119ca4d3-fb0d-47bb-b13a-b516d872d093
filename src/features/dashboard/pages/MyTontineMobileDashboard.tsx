import CommonCard from '../../../common/components/card/CommonCard'
import Header from '../../../common/components/Header'
import MobileAppBar from '../../../common/components/MobileAppBar'
import PageLayout from '../../../common/components/PageLayout'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { renderNominalBalance } from '../../../common/utils/UtilFunctions'
import { DASHBOARD_NAVIGATION } from '../../../routes/Route'
import AccountSummary from '../../authentication/components/AccountSummary'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useBankingService } from '../../banking/hooks/useBankingService'
import NextPayoutCard from '../../dashboard/components/NextPayoutCard'
import UserFeedback from '../../feedback/components/UserFeedback'
import style from '../style/MyTontineMobileDashboard.module.scss'
import FundedDashboard from './FundedDashboard'

const MyTontineDashboard = () => {
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  const { formatAmount } = useLocalization()
  const { bankContext } = useBankingService()
  const { isMobileOrTablet } = useDeviceScreen()

  return (
    <>
      <AccountSummary />
      <main className={style.myTontineDashboard}>
        <PageLayout containerHeight="lite-build" containerMt="nomt">
          <section className={style['myTontineDashboard__top-section']}>
            {user_details?.kyc_status?.L2?.passed_level && (
              <NextPayoutCard className={style['myTontineDashboard__card']} />
            )}

            <Header
              className={style['myTontineDashboard__header']}
              title={t('DASHBOARD.CHART.TITLE.EXPECTED.MONTHLY.INCOME')}
            />
          </section>

          <FundedDashboard error={undefined} isLoading={false} />

          <section className={style['myTontineDashboard__bottom-section']}>
            <CommonCard
              icon={ASSET.iconmtnominalbalanceUS}
              subtitle={t('NOMINAL_BALANCE_LABEL')}
              title={
                renderNominalBalance(formatAmount, bankContext, 'standard')
                  ?.formattedAmountWithSymbol ?? '-'
              }
              href={DASHBOARD_NAVIGATION.NOMINAL_BALANCE}
              variant="gray-dirty"
            />

            <CommonCard
              title={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
              subtitle={t('EXPLORE.DIFFERENT.SCENARIOS.DASHBOARD.SUB')}
              icon={ASSET.iconmttontinator}
              href={DASHBOARD_NAVIGATION.TONTINATOR}
              variant="gray-dirty"
            />

            <CommonCard
              title={t('INVITE_FRIENDS.GIVE_GET_STATEMENT')}
              subtitle={t('INVITE_FRIENDS.WITH_FRIENDS_CAPTION')}
              icon={ASSET.iconaccountrewardbicolor}
              href={DASHBOARD_NAVIGATION.DASHBOARD_REWARDS}
              variant="gray-dirty"
            />
          </section>
        </PageLayout>
        {isMobileOrTablet && <UserFeedback />}
      </main>
      {isMobileOrTablet && (
        <MobileAppBar
          completedKyc={Boolean(user_details?.kyc_status?.L2?.passed_level)}
        />
      )}
    </>
  )
}

export default MyTontineDashboard
