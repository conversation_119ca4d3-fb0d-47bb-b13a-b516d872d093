import OnboardingFormLayout from '../components/OnboardingFlowLayout'
import TontinatorInputs from '../components/TontinatorInputs'
import style from '../style/OnboardingForm.module.scss'
import { ContributionFormProps } from '../types/CalculatorForms.types'

/**
 * ContributionForm step in the onboarding flow.
 * Users enter contribution details before continuing to the forecast.
 */
const ContributionForm = ({
  setFormData,
  formHeaderText,
  formData,
  goToStep,
  progress,
}: ContributionFormProps) => {
  return (
    <OnboardingFormLayout
      formHeaderText={formHeaderText}
      progress={progress}
      onNext={() => goToStep('forecast')}
      onBack={() => goToStep('ageForm')}
    >
      <TontinatorInputs
        hideRetirementSliders
        setFormData={setFormData}
        formData={formData}
        contributionSlidersClassName={style['onboarding-form__spacing']}
      />
    </OnboardingFormLayout>
  )
}

export default ContributionForm
