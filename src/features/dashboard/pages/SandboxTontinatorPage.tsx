import { useState } from 'react'
import { useSearchParams } from 'react-router'
import Layout from '../../../common/components/Layout'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import PublicTontinatorBottomSection from '../components/PublicTontinatorBottomSection'
import SandboxDashboard from '../components/SandboxDashboard'
import SandboxTontinatorBottomSection from '../components/SandboxTontinatorBottomSection'
import { useEmbeddedTon } from '../hooks/useEmbededTon'
import {
  chooseDefaultParams,
  useForecastParamsState,
} from '../hooks/useForecastParamsState'

const SandboxTontinatorPage = () => {
  const [urlSearchParams] = useSearchParams()
  const { isEmbedded } = useEmbeddedTon()
  const {
    isAuthenticated,
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()
  const { detectedCountry } = useLocalization()
  const { isMobileOrTablet } = useDeviceScreen()

  const navigation = useCustomNavigation()
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: user_details?.residency ?? detectedCountry?.alpha3,
  })
  const [isCompareOpen, setIsCompareOpen] = useState(false)
  const [isParamsOpen, setIsParamsOpen] = useState(false)
  const { tontinatorParams } = supportedCountry

  const defaultParams = chooseDefaultParams({
    tontinatorParams,
    supportedCountry,
    isAuthenticated,
    userDetails: user_details,
    urlSearchParams,
  })

  const {
    blueForecastParams,
    yellowForecastParams,
    incomeForecastParams,
    setBlueForecastParams,
    setYellowForecastParams,
    setIncomeForecastParams,
  } = useForecastParamsState({ defaultParams, tontinatorParams })

  return (
    <Layout
      hideMobileHeader={isEmbedded}
      hideDividerHeader={isEmbedded}
      pageTitle={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
      containerMt={isEmbedded ? 'nomt' : 'mt-20'}
      containerHeight={isEmbedded ? 'auto' : 'mh'}
      onClickAction={() => {
        // When slider page is open, close it
        if (isParamsOpen) {
          setIsParamsOpen(false)
        }
        // When compare page is open close it
        if (isCompareOpen) {
          setIsCompareOpen(false)
        }
        // When on the compare page and the slider compare page is open
        // close close only the slider page
        if (isParamsOpen && isCompareOpen) {
          setIsParamsOpen(false)
          setIsCompareOpen(true)
        }
        if (!isParamsOpen && !isCompareOpen) {
          navigation(PUBLIC.GO_TO_PREV_PAGE)
        }
      }}
      bottomSection={
        !isEmbedded && (
          <SandboxTontinatorBottomSection
            isCompareOpen={isCompareOpen}
            isMobileOrTablet={isMobileOrTablet}
            setIsCompareOpen={setIsCompareOpen}
            setIsParamsOpen={setIsParamsOpen}
            setIncomeForecastParams={setIncomeForecastParams}
            blueForecastParams={blueForecastParams}
            yellowForecastParams={yellowForecastParams}
          />
        )
      }
    >
      <SandboxDashboard
        blueForecastParams={blueForecastParams}
        yellowForecastParams={yellowForecastParams}
        incomeForecastParams={incomeForecastParams}
        setBlueForecastParams={setBlueForecastParams}
        setYellowForecastParams={setYellowForecastParams}
        setIncomeForecastParams={setIncomeForecastParams}
        isCompareOpen={isCompareOpen}
        isParamsOpen={isParamsOpen}
        setIsParamsOpen={setIsParamsOpen}
        isEmbedded={isEmbedded}
      />

      {isEmbedded && (
        <PublicTontinatorBottomSection
          isCompareOpen={isCompareOpen}
          setIsCompareOpen={setIsCompareOpen}
          setOpenSliderPage={setIsParamsOpen}
          incomeForecastParams={incomeForecastParams}
          setIncomeForecastParams={setIncomeForecastParams}
          blueForecastParams={blueForecastParams}
          yellowForecastParams={yellowForecastParams}
        />
      )}
    </Layout>
  )
}

export default SandboxTontinatorPage
