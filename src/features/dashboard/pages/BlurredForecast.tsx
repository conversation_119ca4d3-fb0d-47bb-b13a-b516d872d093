import Modal from '../../../common/components/Modal'
import style from '../../../common/style/BlurredForecastModal.module.scss'
import { parseParamsForEmail } from '../../../common/utils/UtilFunctions'
import RegisterFormModal from '../../authentication/pages/RegisterFormModal'
import TontineDashboardLayout from '../components/TontineDashboardLayout'
import type { BlurredForecastProps } from './BlurredForecast.types'
import SandboxTontinatorPage from './SandboxTontinatorPage'

const BlurredForecast = ({
  incomeForecastParams,
  isAuthenticated,
}: BlurredForecastProps) => {
  return (
    <>
      {!isAuthenticated && (
        <Modal
          isOpen
          backdrop
          className="blurredForecastModal"
          customStyle={style}
        >
          <RegisterFormModal
            forecastPageRegisterModal
            forecastUserData={
              incomeForecastParams
                ? parseParamsForEmail(incomeForecastParams)
                : undefined
            }
          />
        </Modal>
      )}
      <div
        style={{
          // Blurs the forecast page for external users
          filter: 'blur(5px)',
        }}
      >
        <TontineDashboardLayout>
          <SandboxTontinatorPage />
        </TontineDashboardLayout>
      </div>
    </>
  )
}

export default BlurredForecast
