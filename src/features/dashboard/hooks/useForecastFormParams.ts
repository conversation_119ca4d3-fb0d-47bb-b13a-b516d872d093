import { useEffect, useState } from 'react'
import {
  IncomeForecastParams,
  InvestmentStrategyId,
  SexType,
} from '../../../common/types/CommonTypes.types'
import { getSupportedTontinatorParams } from '../../../common/utils/UtilFunctions'
import { AgeMonth } from '../../CommonState.type'

/**
 * Initializes and manages the state of income forecast parameters
 * for the tontinator onboarding flow, based on the user's detected country and US residency status.
 *
 */
export const useForecastParams = (
  detectedCountry?: string,
  isUSA?: boolean
) => {
  const [incomeForecastParams, setIncomeForecastParams] = useState(() => {
    const defaults = getSupportedTontinatorParams(detectedCountry ?? '')
    return {
      contributionAge: defaults?.defaultCurrentAgeSlider as AgeMonth | number,
      retirementAge: defaults?.defaultRetirementAgeSlider as
        | AgeMonth
        | number
        | undefined,
      monthlyContribution: defaults?.defaultMonthlySliderValue,
      oneTimeContribution: defaults?.defaultOneTimeSliderValue,
      countryOfResidence: detectedCountry ?? '',
      sex: defaults?.defaultSex as SexType,
      // FII and BOL supported values for now
      strategy: (isUSA ? 'FII' : 'BOL') as InvestmentStrategyId,
      paramsMode: 'TTF',
    } as IncomeForecastParams
  })

  useEffect(() => {
    if (incomeForecastParams?.countryOfResidence) {
      const defaultParams = getSupportedTontinatorParams(
        incomeForecastParams?.countryOfResidence
      )
      setIncomeForecastParams((prev) => ({
        ...prev,
        contributionAge: defaultParams?.defaultCurrentAgeSlider,
        retirementAge: defaultParams?.defaultRetirementAgeSlider,
        monthlyContribution: defaultParams?.defaultMonthlySliderValue,
        oneTimeContribution: defaultParams?.defaultOneTimeSliderValue,
      }))
    }
  }, [incomeForecastParams?.countryOfResidence])

  return { incomeForecastParams, setIncomeForecastParams }
}
