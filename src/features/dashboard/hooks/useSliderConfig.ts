import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { generateRange } from '../../../common/utils/UtilFunctions'
import { SliderConfigProps } from '../types/SliderConfig.types'
import { DASHBOARD_CONSTANTS, paramModeToLabel } from '../utils/consts'

const disableRetirementSliderUSA = ({
  currentAge,
  maxRetirementAgeForCountry,
}: {
  currentAge: number
  maxRetirementAgeForCountry: number
}) => currentAge >= maxRetirementAgeForCountry

/**
 * Handles sliders min and max steps as well as anything visual related with
 * sliders, like disabling inc and dec buttons and disabling the whole slider
 */
export const useSliderConfig = ({
  formData,
  skipComparePlanRangeAdjustment,
}: SliderConfigProps) => {
  const t = useTranslate()
  const isRetired =
    formData?.contributionAge?.age === formData?.retirementAge?.age

  const { formatAmount, isUSA, detectedCountry } = useLocalization(
    // Allows for the "HOW IT WORKS" flow to change the country params on the
    // fly because we have a country selection dropdown there
    formData?.countryOfResidence
  )

  const { tontinatorParams } = detectedCountry

  // Overrides the backend max rules because of USA tax purposes and the slider
  // to be cut off
  if (isUSA && formData?.paramsMode === 'IRA') {
    tontinatorParams.maxRetirementAge = { age: 73, month: 0 }
  }

  const shouldDisabledUSA = disableRetirementSliderUSA({
    currentAge: formData?.contributionAge?.age,
    maxRetirementAgeForCountry: tontinatorParams?.maxRetirementAge?.age ?? 0,
  })

  /**
   * Controls the range of the sliders based on enabled and disabled ranges. If
   * only sliders would have `min` and `max` :(
   */
  const retirementSliderEnabledSteps = () => {
    if (shouldDisabledUSA) {
      return generateRange(0, 0)
    }

    const isAboveMinRetirementAge =
      formData?.contributionAge?.age >
      (tontinatorParams?.minRetirementAge?.age ?? 0)

    const isBelowMaxRetirementAge =
      formData?.contributionAge?.age <
      (tontinatorParams?.maxRetirementAge?.age ?? 0)

    const shouldAdjustRange = skipComparePlanRangeAdjustment

    // Makes sure that the sliders on the compare plan page do not adjust the
    // contribution age on the tontinator page
    if (
      isAboveMinRetirementAge &&
      shouldAdjustRange &&
      isBelowMaxRetirementAge
    ) {
      return generateRange(
        formData?.contributionAge?.age,
        tontinatorParams?.maxRetirementAge?.age ?? 0
      )
    }

    return generateRange(
      tontinatorParams?.minRetirementAge?.age ?? 0,
      tontinatorParams?.maxRetirementAge?.age ?? 0
    )
  }

  const {
    retirementAgeSliderLabel,
    currentAgeSliderLabel,
    disableRetirementSliderLabel,
    disabledMonthlySliderLabel,
    oneTimeContributionLabel,
    monthlyContributionLabel,
  } = paramModeToLabel[formData.paramsMode ?? 'TTF']

  const shouldDisableDecOneTime = () => {
    // If user is retired the one time contribution cannot be 0 because monthly
    // is 0
    if (isRetired) {
      return (
        formData?.oneTimeContribution ===
        tontinatorParams?.oneTimeContributionIfRetired[0]
      )
    }

    return (
      formData?.oneTimeContribution === tontinatorParams?.oneTimeContribution[0]
    )
  }

  const formatter = (amount: number) =>
    formatAmount({
      amount,
      style: 'currency',
      digits: DASHBOARD_CONSTANTS.CURRENCY_DIGITS_FORMATTING,
      currency: detectedCountry?.currency,
    })?.formattedAmountWithSymbol ?? ''

  return {
    retirementSliderEnabledSteps: retirementSliderEnabledSteps(),
    shouldDisabledUSA,
    isRetired,
    formatter,
    currentAgeSteps: generateRange(
      tontinatorParams?.minCurrentAge?.age ?? 0,
      tontinatorParams?.maxCurrentAge?.age ?? 0
    ),
    retirementAgeSteps: generateRange(
      tontinatorParams?.minCurrentAge?.age ?? 0,
      tontinatorParams?.maxCurrentAge?.age ?? 0
    ),
    retirementAgeSliderLabel: t(retirementAgeSliderLabel),
    currentAgeSliderLabel: t(currentAgeSliderLabel),
    disableRetirementSliderLabel: t(disableRetirementSliderLabel),
    disabledMonthlySliderLabel: t(disabledMonthlySliderLabel),
    oneTimeContributionLabel: t(oneTimeContributionLabel),
    monthlyContributionLabel: t(monthlyContributionLabel),
    disabledIncrementCurrentAge:
      formData?.contributionAge?.age === tontinatorParams?.maxCurrentAge?.age,
    disabledDecrementCurrentAge:
      formData?.contributionAge?.age === tontinatorParams?.minCurrentAge?.age,
    disabledIncrementRetirementAge:
      formData?.retirementAge?.age ===
        tontinatorParams?.maxRetirementAge?.age &&
      formData?.contributionAge?.age <
        (tontinatorParams?.maxRetirementAge?.age ?? 0),
    disabledDecrementRetirementAge:
      formData?.retirementAge?.age === tontinatorParams?.minRetirementAge?.age,
    shouldDisableDecOneTime,
    disabledIncOneTime:
      formData?.oneTimeContribution ===
      tontinatorParams?.oneTimeContribution[
        tontinatorParams?.oneTimeContribution?.length - 1
      ],
    disabledDecMonthly:
      formData?.monthlyContribution ===
      tontinatorParams?.monthlyContribution[0],
    disabledIncMonthly:
      formData?.monthlyContribution ===
      tontinatorParams?.monthlyContribution[
        tontinatorParams?.monthlyContribution?.length - 1
      ],
    tontinatorParams,
    oneTimeContributionSteps: isRetired
      ? tontinatorParams?.oneTimeContributionIfRetired
      : tontinatorParams?.oneTimeContribution,
    monthlyContributionSteps: tontinatorParams?.monthlyContribution,
  }
}
