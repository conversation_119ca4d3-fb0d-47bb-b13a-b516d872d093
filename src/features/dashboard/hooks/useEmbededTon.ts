import { useLocation } from 'react-router'
import { PUBLIC } from '../../../routes/Route'

export const useEmbeddedTon = () => {
  const location = useLocation()
  /**
   * If the tontinaotor is on the tontine.com website or on the /tontinator page
   * within the app
   */
  const isEmbedded =
    location.pathname === PUBLIC.PUBLIC_TONTINATOR ||
    location.pathname === PUBLIC.TONTINATOR

  /**
   * Specific only if the tontinator is a pubic page within the app
   */
  const isOnPublicPage = location.pathname === PUBLIC.TONTINATOR

  return { isEmbedded, isOnPublicPage }
}
