import { useEffect, useState } from 'react'
import { track } from '../../../common/analytics/Analytics'
import { ForecastParamsEvent } from '../../../common/analytics/EventData'
import { ChartId } from '../../../common/analytics/ObjectId'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { DASHBOARD_CONSTANTS } from '../utils/consts'

/**
 * Sends an `REQUEST_INCOME_FORECAST` to the xstate machine, also tracks the
 * data sent to the API with analytics, the default forecast is skipped because
 * the user has not interacted with the UI
 */
export const useRequestIncomeForecast = ({
  incomeForecastParams,
}: {
  incomeForecastParams: Array<IncomeForecastParams & { paramsId?: string }>
}) => {
  const { currentState, send, context } = useAccountService()
  const [isLoading, setIsLoading] = useState(true)
  const [skipDefaultParamsTrack, setSkipDefaultParamsTrack] = useState(true)
  ///useEffect dependencies ////////////

  const useEffectHash = generateHashFromForecastParams(incomeForecastParams)

  ///////////////

  useEffect(() => {
    setIsLoading(true)
    const debounceRequests = setTimeout(() => {
      send({
        type: 'REQUEST_INCOME_FORECAST',
        payload: {
          forecastParams: incomeForecastParams,
          successCallback: () => {
            const paramsId =
              incomeForecastParams?.[0]?.paramsId ??
              incomeForecastParams?.[1]?.paramsId

            if (!skipDefaultParamsTrack) {
              void track({
                event: ForecastParamsEvent.sent,
                properties: {
                  object_id: paramsId as ChartId,
                  object_value: incomeForecastParams,
                },
              })
            }
            if (skipDefaultParamsTrack) {
              setSkipDefaultParamsTrack(false)
            }
          },

          finallyCallback: () => setIsLoading(false),
        },
      })
    }, DASHBOARD_CONSTANTS.INCOME_FORECAST_DEBOUNCE_TIME)

    return () => {
      clearTimeout(debounceRequests)
    }
  }, [useEffectHash])

  return {
    isLoading: isLoading || currentState === 'FORECASTING_INCOME',
    forecastData: context?.forecastData?.forecastResults,
    error: context?.forecastData?.error,
  }
}

/**
 * Generates a hash from the income forecast params in order to determine if
 * params have changed in the UI so a new API request can be made
 */
const generateHashFromForecastParams = (
  incomeForecastParams: Array<IncomeForecastParams>
): string => {
  if (incomeForecastParams) {
    const changeHash = incomeForecastParams
      .map((param) => JSON.stringify(Object.values(param)))
      .join('')

    return changeHash
  }
  return ''
}
