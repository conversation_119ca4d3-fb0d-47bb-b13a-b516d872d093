import LottieAnimation from '../../../common/components/LottieAnimation'
import { ANIMATION } from '../../../common/constants/Animations'
import { useCountUp } from '../../../common/hooks/useCountUpAnimation'
import { useLocalization } from '../../../common/hooks/useLocalization'
import style from '../style/CurrencyStat.module.scss'
import { CurrencyStatProps } from '../types/Dashboard.types'
import { DASHBOARD_CONSTANTS } from '../utils/consts'

const compactFractionDigits = {
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
}

const defaultDigits = {
  maximumFractionDigits: 0,
  minimumFractionDigits: 0,
}

/**
 * Renders a label and formatted and animated currency amount. Only supported
 * currencies are `USD` and `JPY`.
 */
const CurrencyStat = ({
  currency,
  amount,
  label,
  disableAnimation,
  isLoading,
}: CurrencyStatProps) => {
  const { formatAmount } = useLocalization()

  const isVerySmallScreen =
    window?.innerWidth >= DASHBOARD_CONSTANTS.IPHONE5_WIDTH &&
    window?.innerWidth < DASHBOARD_CONSTANTS.IPHONEX_WIDTH

  const animatedAmount = useCountUp({
    targetValue: amount,
    durationInSeconds: 1_500,
    disableAnimation,
  })

  const isLargeDigit =
    animatedAmount?.toString()?.length >
      DASHBOARD_CONSTANTS.LARGE_DIGIT_LIMIT || isVerySmallScreen

  return (
    <article className={style[`currency-stat`]}>
      <h3 className={style[`currency-stat__label`]}>{label}</h3>
      {isLoading ? (
        <LottieAnimation
          loop
          autoplay
          animationName={ANIMATION.loadingLightBlueDots}
          style={{
            height: '38px',
            scale: '4',
          }}
        />
      ) : (
        <p className={style[`currency-stat__amount`]}>
          {
            formatAmount({
              notation: isLargeDigit ? 'compact' : 'standard',
              style: 'currency',
              currency,
              amount: animatedAmount,
              digits: isLargeDigit ? compactFractionDigits : defaultDigits,
            })?.formattedAmountWithSymbol
          }
        </p>
      )}
    </article>
  )
}

export default CurrencyStat
