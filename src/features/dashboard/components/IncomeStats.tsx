import PercentTag from '../../../common/components/PercentTag'
import { IncomeStatsProps } from '../../../common/types/DataDisplay.types'
import style from '../style/IncomeStats.module.scss'
import CurrencyStat from './CurrencyStat'

/**
 * Renders total contribution, income percentage, and total income by age 100
 */
const IncomeStats = ({
  contributionAmount,
  contributionLabel,
  incomeAmount,
  incomeLabel,
  incomePercentage,
  currency,
  variant,
  isLoading,
}: IncomeStatsProps) => {
  return (
    <main className={style['income-stats']}>
      <section
        className={
          style[`income-stats__container${variant ? `--${variant}` : ''}`]
        }
      >
        <CurrencyStat
          amount={contributionAmount}
          currency={currency}
          label={contributionLabel ?? ''}
          disableAnimation
          isLoading={isLoading}
        />
        <PercentTag
          percentage={incomePercentage}
          isLoading={isLoading}
          prefix="+"
          dividerTop
        />
        <CurrencyStat
          amount={incomeAmount}
          currency={currency}
          label={incomeLabel ?? ''}
          isLoading={isLoading}
        />
      </section>
    </main>
  )
}

export default IncomeStats
