import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Button from '../../../common/components/Button'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/ComparePlansButtons.module.scss'
import { ComparePlanButtonsProps } from '../types/Dashboard.types'

/**
 * Button layout for mobile and desktop for MyTontine lite
 */
const ComparePlanButtons = ({
  onClickPlan1,
  onClickPlan2,
  blueForecastParams,
  yellowForecastParams,
  onClickBack,
}: ComparePlanButtonsProps) => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()

  return (
    <section className={style['compare-plan-buttons']}>
      {!isMobileOrTablet && (
        <Button
          variant="back--light"
          onClick={onClickBack}
          className={style['compare-plan-buttons__back-btn']}
          icon={ASSET.icononboardinarrowback}
          dataTestID={UI_TEST_ID.backBtnDesktop}
          trackActivity={{
            trackId: 'compare_plan_back',
            eventDescription: EVENT_DESC.comparePlanBack,
          }}
        >
          {t('BACK_BUTTON_LABEL')}
        </Button>
      )}
      <Button
        variant="blue"
        onClick={onClickPlan1}
        className={style['compare-plan-buttons__btns']}
        dataTestID={UI_TEST_ID.pickPlan1Button}
        trackActivity={{
          trackId: 'compare_plan_choose_plan1',
          eventDescription: EVENT_DESC.plan1Choose,
          value: blueForecastParams,
        }}
      >
        {t('BUTTON_KEEP_PLAN_1')}
      </Button>
      <Button
        variant="yellow"
        className={style['compare-plan-buttons__btns']}
        onClick={onClickPlan2}
        dataTestID={UI_TEST_ID.pickPlan2Button}
        trackActivity={{
          trackId: 'compare_plan_choose_plan2',
          eventDescription: EVENT_DESC.plan2Choose,
          value: yellowForecastParams,
        }}
      >
        {t('BUTTON_KEEP_PLAN_2')}
      </Button>
    </section>
  )
}

export default ComparePlanButtons
