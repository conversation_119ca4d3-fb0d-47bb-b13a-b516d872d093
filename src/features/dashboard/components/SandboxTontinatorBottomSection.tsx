import Button from '../../../common/components/Button'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/SandboxDashboard.module.scss'
import { SandboxTontinatorBottomSectionProps } from '../types/SandboxTontinatorBottomSection.types'
import ComparePlanButtons from './ComparePlanButtons'

/**
 * Bottom section component for the SandboxTontinatorPage.
 * Renders action buttons for comparing plans and checking other scenarios.
 * Displays different button layouts based on comparison state and device type.
 */
const SandboxTontinatorBottomSection = ({
  isCompareOpen,
  isMobileOrTablet,
  setIsCompareOpen,
  setIsParamsOpen,
  setIncomeForecastParams,
  blueForecastParams,
  yellowForecastParams,
}: SandboxTontinatorBottomSectionProps) => {
  const t = useTranslate()

  return (
    <section className={style['sandbox-dashboard__bottom-layout']}>
      <div className={style['sandbox-dashboard__buttons']}>
        {!isCompareOpen && isMobileOrTablet && (
          <Button variant="alternative" onClick={() => setIsParamsOpen(true)}>
            {t('CHECK_OTHER_SCENARIOS')}
          </Button>
        )}

        {!isCompareOpen && (
          <Button variant="blue" onClick={() => setIsCompareOpen(true)}>
            {t('BUTTON_TO_COMPARE')}
          </Button>
        )}

        {isCompareOpen && (
          // We use MTL buttons here because there is no design for the full
          // version to what does what
          <>
            {isMobileOrTablet && (
              <Button
                variant="alternative"
                onClick={() => setIsParamsOpen(true)}
              >
                {t('CHECK_OTHER_SCENARIOS')}
              </Button>
            )}
            <ComparePlanButtons
              onClickBack={() => setIsCompareOpen(false)}
              onClickPlan1={() => {
                setIsCompareOpen(false)
                setIncomeForecastParams(blueForecastParams)
              }}
              onClickPlan2={() => {
                setIsCompareOpen(false)
                setIncomeForecastParams(yellowForecastParams)
              }}
              blueForecastParams={blueForecastParams}
              yellowForecastParams={yellowForecastParams}
            />
          </>
        )}
      </div>
    </section>
  )
}

export default SandboxTontinatorBottomSection
