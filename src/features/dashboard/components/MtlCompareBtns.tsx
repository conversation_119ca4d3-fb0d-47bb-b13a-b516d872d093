import Button from '../../../common/components/Button'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { MtlCompareBtnsProps } from '../types/MtlCompareButtons.types'
import ComparePlanButtons from './ComparePlanButtons'

/**
 * Renders the bottom CTA buttons for the Public Tontinator homepage
 * Displays different buttons based on authentication status
 */
const MtlCompareBtns = ({
  onSeeOtherScenarios,
  onClickBack,
  blueForecastParams,
  yellowForecastParams,
  onClickPlan1,
  onClickPlan2,
}: MtlCompareBtnsProps) => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()

  return (
    <>
      {isMobileOrTablet && (
        <Button
          dataTestID={UI_TEST_ID.openSliderPageButton}
          onClick={onSeeOtherScenarios}
          variant="alternative"
        >
          {t('CONFIGURE_PLANS_CTA')}
        </Button>
      )}

      <ComparePlanButtons
        onClickBack={onClickBack}
        blueForecastParams={blueForecastParams}
        onClickPlan1={onClickPlan1}
        onClickPlan2={onClickPlan2}
        yellowForecastParams={yellowForecastParams}
      />
      {isMobileOrTablet && (
        <Button variant="secondary" onClick={onClickBack}>
          {t('DISMISS_MTL_PLAN_CHANGES')}
        </Button>
      )}
    </>
  )
}

export default MtlCompareBtns
