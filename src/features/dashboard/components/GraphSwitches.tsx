import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Toggle from '../../../common/components/Toggle'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/GraphSwitches.module.scss'
import { GraphSwitchesProps } from '../types/Dashboard.types'

/**
 * GraphSwitches is a component that provides a group of toggles
 * that can be used to switch between different views of a graph.
 */
const GraphSwitches = ({
  breakevenLabel,
  inflationLabel,
  percentage,
  handlePercentage,
  breakeven,
  handleBreakeven,
  inflation,
  handleInflation,
  togglesVariant,
  currency,
}: GraphSwitchesProps) => {
  const { formatAmount } = useLocalization()
  const t = useTranslate()

  const changeCurrencyLabel = () =>
    t('FORECAST_PAGE.GRAPH_SWITCH_PERCENT', {
      symbol: percentage
        ? formatAmount({
            amount: 0,
            currency,
            style: 'currency',
          })?.symbol
        : formatAmount({
            amount: 0,
            style: 'percent',
          })?.symbol,
    })

  return (
    <article className={style.graphSwitches}>
      <div
        className={
          style[
            `graphSwitches__container${togglesVariant ? `--${togglesVariant}` : ``}`
          ]
        }
      >
        <Toggle
          toggled={percentage}
          onChange={handlePercentage}
          label={changeCurrencyLabel()}
          testID={UI_TEST_ID.showPercentToggle}
          variant={togglesVariant}
          trackActivity={{
            trackId: 'tontinator_percentage',
            eventDescription: EVENT_DESC.tontinatorPercentToggle,
          }}
        />

        <Toggle
          toggled={breakeven}
          label={breakevenLabel}
          onChange={handleBreakeven}
          testID={UI_TEST_ID.breakevenToggle}
          variant={togglesVariant}
          trackActivity={{
            trackId: 'tontinator_breakeven',
            eventDescription: EVENT_DESC.tontinatorBreakevenToggle,
          }}
        />

        <Toggle
          toggled={inflation}
          onChange={handleInflation}
          label={inflationLabel}
          testID={UI_TEST_ID.showInflationToggle}
          variant={togglesVariant}
          trackActivity={{
            trackId: 'tontinator_inflation',
            eventDescription: EVENT_DESC.tontinatorPercentToggle,
          }}
        />
      </div>
    </article>
  )
}

export default GraphSwitches
