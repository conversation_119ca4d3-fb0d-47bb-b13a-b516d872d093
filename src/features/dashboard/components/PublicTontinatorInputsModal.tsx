import Modal from '../../../common/components/Modal'
import { ModalProps } from '../../../common/types/Modal.types'
import { default as tontinatorInputsModalStyle } from '../style/TontinatorInputsModal.module.scss'
import { PublicTontinatorInputsProps } from '../types/PublicTontinatorInptus.types'
import PublicTontinatorInputs from './PublicTontinatorInputs'

const PublicTontinatorInputsModal = ({
  tontinatorProps,
  children,
  ...rest
}: {
  tontinatorProps: PublicTontinatorInputsProps
} & Omit<ModalProps, 'backdrop'>) => {
  return (
    <Modal
      {...rest}
      customStyle={tontinatorInputsModalStyle}
      backdrop
      // Do not wrap this in a style
      className={'tontinator-inputs-modal'}
    >
      <PublicTontinatorInputs {...tontinatorProps} />
      {children}
    </Modal>
  )
}

export default PublicTontinatorInputsModal
