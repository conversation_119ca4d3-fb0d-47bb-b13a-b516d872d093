import Button from '../../../common/components/Button'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import {
  IncomeForecastParams,
  InvestmentStrategyId,
} from '../../../common/types/CommonTypes.types'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useBankingService } from '../../banking/hooks/useBankingService'
import {
  chooseDefaultParams,
  modifyParamsForComparison,
} from '../hooks/useForecastParamsState'
import sandboxStyle from '../style/SandboxDashboard.module.scss'
import tontinatorInputsModalStyle from '../style/TontinatorInputsModal.module.scss'
import { InvestmentDetails } from '../types/MyTontineMobileDashboard.types'
import { SandboxDashboardProps } from '../types/SandboxDashboard.types'
import ExtendedTontinatorInputs from './ExtendedTontinatorInputs'
import InvStrategiesDropdown from './InvStrategiesDropdown'
import ParamModes from './ParamModes'
import PensionPlanDashboard from './PensionPlanDashboard'
import PublicTontinatorInputs from './PublicTontinatorInputs'
import PublicTontinatorInputsModal from './PublicTontinatorInputsModal'
import TontinatorDashboard from './TontinatorDashboard'

const idToProd = {
  TTF: 'TontineTrustFund',
  IRA: 'TontineIRA',
}

const SandboxDashboard = ({
  incomeForecastParams,
  setIncomeForecastParams,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  isCompareOpen,
  isParamsOpen,
  setIsParamsOpen,
  isEmbedded = false,
}: SandboxDashboardProps) => {
  const t = useTranslate()

  const { isMobileOrTablet } = useDeviceScreen()
  const {
    isAuthenticated,
    context: { user_details },
  } = useAccountService()
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode:
      user_details?.residency ?? incomeForecastParams?.countryOfResidence,
  })
  const { sendBankEvent } = useBankingService()

  const inputsParams = {
    blueForecastParams: blueForecastParams,
    comparison: isCompareOpen,
    incomeForecastParams: incomeForecastParams,
    setIncomeForecastParams: setIncomeForecastParams,
    setBlueForecastParams: setBlueForecastParams,
    setYellowForecastParams: setYellowForecastParams,
    yellowForecastParams: yellowForecastParams,
    isEmbedded: isEmbedded,
    extendDefault: (
      <ExtendedTontinatorInputs
        incomeForecastParams={incomeForecastParams}
        setIncomeForecastParams={setIncomeForecastParams}
        trackInvStrategies={{
          trackId: 'tontinator_investment_strategy',
        }}
        showSex={!isAuthenticated}
      />
    ),
    extendBlue: (
      <InvStrategiesDropdown
        value={blueForecastParams?.strategy}
        onChange={(strategy) => {
          setBlueForecastParams({
            ...blueForecastParams,
            strategy,
          })
        }}
        label={t('TTF.INVESTMENT_STRATEGY_DROPDOWN_LABEL')}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    extendYellow: (
      <InvStrategiesDropdown
        value={yellowForecastParams?.strategy}
        onChange={(strategy) => {
          setYellowForecastParams({
            ...yellowForecastParams,
            strategy,
          })
        }}
        label={t('TTF.INVESTMENT_STRATEGY_DROPDOWN_LABEL')}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    propsForDefaultLayout: {
      hideRetirementSliders: isAuthenticated,
      showRetirementScheduler: isAuthenticated,
    },
    propsForBlueLayout: {
      hideRetirementSliders: isAuthenticated,
      showRetirementScheduler: isAuthenticated,
    },
    propsForYellowLayout: {
      hideRetirementSliders: isAuthenticated,
      showRetirementScheduler: isAuthenticated,
    },
  }

  const extendedTopSection = (
    <ParamModes
      onChange={(value) => {
        sendBankEvent({
          type: 'UPDATE_PRODUCT',
          payload: {
            product: idToProd[value],
          },
        })
        const defaultParams = chooseDefaultParams({
          tontinatorParams: supportedCountry.tontinatorParams,
          supportedCountry,
        })

        setIncomeForecastParams({
          // reset to default params when the user changes the product
          // also known as params mode
          ...defaultParams,
          paramsMode: value,
        } as IncomeForecastParams)

        setBlueForecastParams({
          ...defaultParams,
          paramsMode: value,
        } as IncomeForecastParams)

        setYellowForecastParams({
          ...modifyParamsForComparison({
            retirementAge: defaultParams?.retirementAge,
            targetValue:
              supportedCountry?.tontinatorParams?.maxRetirementAge?.age ?? 0,
            targetIncrement: 5,
            strategies:
              supportedCountry?.supportedInvestments as InvestmentDetails,
            strategy: defaultParams?.strategy as InvestmentStrategyId,
          }),
          paramsMode: value,
        } as IncomeForecastParams)
      }}
      activeMode={incomeForecastParams.paramsMode ?? 'TTF'}
    />
  )

  return (
    <section
      className={
        isEmbedded
          ? sandboxStyle['sandbox-dashboard__dashboards--public']
          : sandboxStyle['sandbox-dashboard__dashboards']
      }
    >
      {isCompareOpen ? (
        <PensionPlanDashboard
          dataToDraw={[blueForecastParams, yellowForecastParams]}
          isEmbedded={isEmbedded}
          extendTopSection={extendedTopSection}
        />
      ) : (
        <TontinatorDashboard
          incomeForecastParams={incomeForecastParams}
          isEmbedded={isEmbedded}
          extendTopSection={extendedTopSection}
        />
      )}
      {!isMobileOrTablet && <PublicTontinatorInputs {...inputsParams} />}
      {isMobileOrTablet && isParamsOpen && (
        <PublicTontinatorInputsModal
          isOpen={isParamsOpen}
          tontinatorProps={inputsParams}
          className={tontinatorInputsModalStyle['tontinator-inputs-modal']}
        >
          <div
            className={
              tontinatorInputsModalStyle[
                'tontinator-inputs-modal__buttons-wrapper'
              ]
            }
          >
            <Button
              onClick={() => setIsParamsOpen(false)}
              variant="alternative"
            >
              {t('CHECK_UPDATED_CHART')}
            </Button>
          </div>
        </PublicTontinatorInputsModal>
      )}
    </section>
  )
}

export default SandboxDashboard
