import { generateUniqueId } from '../../../common/utils/UtilFunctions'
import style from '../style/NextPayout.module.scss'
import type { NextPayoutLoadingBarProps } from './NextPayoutLoadingBar.types'

const NextPayoutLoadingBar = ({
  maxProgress,
  currentProgress,
  renderStartCircle = true,
  renderEndCircle = true,
}: NextPayoutLoadingBarProps) => {
  const renderProgressDots = () =>
    Array.from({ length: maxProgress ?? 0 }).map((_, index) => {
      //Don't render the first and last dot
      if (index !== 0 && index < (maxProgress ?? 0) - 1) {
        return (
          <div
            key={generateUniqueId()}
            className={`${style[`nextPayout__dashes`]}
            ${style[`nextPayout__dashes${index < (currentProgress ?? 0) ? '--reached' : '--unreached'}`]}
            ${style[`nextPayout__dashes${index === (currentProgress ?? 0) - 1 ? '--current' : ''}`]}`}
          />
        )
      }
      return null
    })

  return (
    <div className={style[`nextPayout__prog-container`]}>
      {renderStartCircle && (
        <div className={style[`nextPayout__start-circle`]} />
      )}
      {renderProgressDots()}
      {renderEndCircle && (
        <div
          className={
            style[
              `nextPayout__end-circle${(currentProgress ?? 0) === (maxProgress ?? 0) ? '--payday' : ''}`
            ]
          }
        />
      )}
    </div>
  )
}

export default NextPayoutLoadingBar
