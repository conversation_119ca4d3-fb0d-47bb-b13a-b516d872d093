import { useState } from 'react'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { IncomeStatsExtendedProps } from '../../../common/types/DataDisplay.types'
import style from '../style/IncomeStats.module.scss'
import IncomeStats from './IncomeStats'

const idToExplanation = {
  FII: 'STAT_EXPLANATION_CDS',
  BOL: 'STAT_EXPLANATION_BOLD',
  VBI: 'STAT_EXPLANATION_VANGUARD',
  BTC: 'STAT_EXPLANATION_BTC',
  XAU: 'STAT_EXPLANATION_XAU',
}

const idToName = {
  FII: 'BANK_DEPOSITS_DD_OPTION',
  BOL: 'BOLD_DD_OPTION',
  VBI: 'VANGUARD_DD_OPTION',
  BTC: 'BTC_DD_OPTION',
  XAU: 'XAU_DD_OPTION',
}

/**
 * Adds a bottom text to the income stats component and a static tooltip
 */
const IncomeStatsExtended = ({
  contributionAmount,
  contributionLabel,
  currency,
  incomeAmount,
  incomeLabel,
  incomePercentage,
  isLoading,
  variant = 'blue-faint',
  investmentStrategy,
}: IncomeStatsExtendedProps) => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()
  const { detectedCountry } = useLocalization()
  const [isExpanded, setIsExpanded] = useState(!isMobileOrTablet)

  const returnsForProduct =
    detectedCountry?.supportedInvestments?.[investmentStrategy]?.rate?.toFixed(
      1
    ) ?? '-'

  return (
    <article className={style['income-stats-extended']}>
      <article
        className={
          style[
            `income-stats-extended__container${variant ? `--${variant}` : ''}`
          ]
        }
      >
        {isMobileOrTablet && (
          <Icon
            fileName={ASSET.infoIconSmall}
            className={style['income-stats-extended__expand-toggle']}
            onClick={() => setIsExpanded((prev) => !prev)}
          />
        )}
        <IncomeStats
          contributionAmount={contributionAmount}
          currency={currency}
          contributionLabel={contributionLabel}
          incomeAmount={incomeAmount}
          incomeLabel={incomeLabel}
          incomePercentage={incomePercentage}
          isLoading={isLoading}
          variant={variant}
        />
        <p className={style['income-stats-extended__strategy']}>
          {t(idToName[investmentStrategy], {
            return: returnsForProduct,
          })}
        </p>
        {isExpanded && (
          <p
            className={
              style[
                `income-stats-extended__explainer${variant ? `--${variant}` : ''}`
              ]
            }
          >
            {t(idToExplanation[investmentStrategy])}
          </p>
        )}
      </article>
    </article>
  )
}

export default IncomeStatsExtended
