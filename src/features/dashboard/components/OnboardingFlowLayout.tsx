import Header from '../../../common/components/Header'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/OnboardingForm.module.scss'
import { OnboardingFormLayoutProps } from '../types/CalculatorForms.types'
import FormProgress from './FormProgress'
import FormsLayout from './FormsLayout'

/**
 * Layout component for onboarding steps in the retirement income forecast flow
 *
 * It displays a header, progress bar, form layout wrapper, and navigation buttons.
 */
const OnboardingFormLayout = ({
  formHeaderText,
  progress,
  children,
  onNext,
  onBack,
  nextButtonDisabled = false,
}: OnboardingFormLayoutProps) => {
  const t = useTranslate()

  return (
    <main className={style['onboarding-form']}>
      <Header title={formHeaderText} variant="spaced" />

      <FormProgress progress={progress} skipSpacing />

      <FormsLayout>{children}</FormsLayout>

      <NavigationButtons
        onClickSecond={onNext}
        onClickFirst={onBack}
        secondButtonLabel={t('BUTTON_LABEL.NEXT')}
        backButtonWhite
        disabledSecond={nextButtonDisabled}
      />
    </main>
  )
}

export default OnboardingFormLayout
