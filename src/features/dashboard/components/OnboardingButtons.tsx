import Button from '../../../common/components/Button'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import style from '../style/Home.module.scss'
import { OnboardingButtonsProps } from '../types/Dashboard.types'

/**
 *Renders two buttons, one for signing up and one for going
 * trough the tontinator flow
 */
const OnboardingButtons = ({
  tontinatorButtonLabel,
  tontinatorButtonOnClick,
  signupButtonLabel,
  signupButtonOnClick,
  showSignupButton,
}: OnboardingButtonsProps) => {
  return (
    <article className={style['home__onboarding-buttons']}>
      <Button
        dataTestID={UI_TEST_ID.howItWorksBtn}
        onClick={tontinatorButtonOnClick}
      >
        {tontinatorButtonLabel}
      </Button>
      {showSignupButton && (
        <Button
          dataTestID={UI_TEST_ID.signUpHomeBtn}
          variant={'alternative'}
          onClick={signupButtonOnClick}
        >
          {signupButtonLabel}
        </Button>
      )}
    </article>
  )
}

export default OnboardingButtons
