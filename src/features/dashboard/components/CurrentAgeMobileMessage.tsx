import BannerMessage from '../../../common/components/BannerMessage'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import style from '../style/TontinatorDashboard.module.scss'
import { paramModeToLabel } from '../utils/consts'

const CurrentAgeMobileMessage = ({
  currentAge,
  paramsMode,
}: {
  currentAge: number
  paramsMode: IncomeForecastParams['paramsMode']
}) => {
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()

  if (!isMobileOrTablet) return null

  return (
    <BannerMessage
      hideIcon
      className={style['tontinatorDashboard__banner-message']}
      fontSize="s"
    >
      <div className={style['tontinatorDashboard__text']}>
        {t(paramModeToLabel?.[paramsMode ?? 'TTF']?.currentAgeMessage, {
          currentAge,
        })}
      </div>
    </BannerMessage>
  )
}

export default CurrentAgeMobileMessage
