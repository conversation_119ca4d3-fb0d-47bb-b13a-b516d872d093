import { track } from '../../../common/analytics/Analytics'
import { ButtonEvent } from '../../../common/analytics/EventData'
import { ObjectIdProperty } from '../../../common/analytics/ObjectId'
import Icon from '../../../common/components/Icon'
import ToggleButton from '../../../common/components/ToggleButton'
import { ASSET } from '../../../common/constants/Assets'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { TontinatorParamsMode } from '../../../common/types/CommonTypes.types'
import style from '../style/ParamModes.module.scss'
import { ParamModesProps } from '../types/Dashboard.types'

export const modes: Array<{
  label: string
  mode: TontinatorParamsMode
  trackId: string
  icon: string
}> = [
  {
    label: 'PARAM_MODE_TTF',
    mode: 'TTF',
    trackId: 'tontinator_enable_ttf',
    icon: ASSET.rowGray,
  },
  {
    label: 'PARAM_MODE_IRA',
    mode: 'IRA',
    trackId: 'tontinator_enable_ira',
    icon: ASSET.iraGray,
  },
]

/**
 * Change the mode of the calculator between IRA and trust fund, by simply
 * reverting to the default params for that mode
 */
const ParamModes = ({ onChange, activeMode }: ParamModesProps) => {
  const t = useTranslate()
  const { isUSA } = useLocalization()

  const filteredModes = modes.filter(({ mode }) => mode === 'IRA' && !isUSA)

  const handleModeChange = ({
    mode,
    label,
    trackId,
  }: {
    mode: TontinatorParamsMode
    label: string
    trackId: ObjectIdProperty
  }) => {
    onChange(mode)
    void track({
      event: ButtonEvent.clicked,
      properties: {
        object_id: trackId,
        label: t(label),
        object_value: activeMode,
      },
    })
  }

  return (
    <section className={style['param-modes']}>
      {modes?.map(({ label, mode, trackId, icon }) => {
        const isActive = activeMode === mode
        if (mode === 'IRA' && !isUSA) return null
        return (
          <ToggleButton
            key={mode}
            icon={
              <Icon
                fileName={icon}
                className={
                  isActive
                    ? style['param-modes--active']
                    : style['param-modes--inactive']
                }
              />
            }
            label={t(label)}
            toggled={isActive}
            onChange={
              filteredModes?.length === 1 || isActive
                ? undefined
                : () =>
                    handleModeChange({
                      mode,
                      label,
                      trackId: trackId as ObjectIdProperty,
                    })
            }
          />
        )
      })}
    </section>
  )
}

export default ParamModes
