import { useTranslate } from '../../../common/hooks/useTranslate'
import { CompareIncomeStatsProps } from '../types/Dashboard.types'
import IncomeStatsExtended from './IncomeStatsExtended'

/**
 * Renders two IncomeStatsExtended components, one for each plan.
 */
const CompareIncomeStats = ({
  plan1,
  plan2,
  currency,
}: CompareIncomeStatsProps) => {
  const t = useTranslate()

  const sharedProps = {
    incomeLabel: t('FORECAST_PAGE.PAYOUTS_BY_100_LABEL'),
    currency: currency as 'USD' | 'EUR',
  }

  return (
    <>
      {plan1 && (
        <IncomeStatsExtended
          variant="blue-faint"
          {...sharedProps}
          {...plan1}
          contributionLabel={t('INCOME_STAT_CONTRIBUTION_LABEL', {
            incomeStartAge: plan1?.incomeStartAge,
          })}
        />
      )}
      {plan2 && (
        <IncomeStatsExtended
          variant="yellow-faint"
          {...sharedProps}
          {...plan2}
          contributionLabel={t('INCOME_STAT_CONTRIBUTION_LABEL', {
            incomeStartAge: plan2?.incomeStartAge,
          })}
        />
      )}
    </>
  )
}

export default CompareIncomeStats
