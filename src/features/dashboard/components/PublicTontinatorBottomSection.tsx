import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { PUBLIC } from '../../../routes/Route'
import { useEmbeddedTon } from '../hooks/useEmbededTon'
import { serializeIncomeForecastParams } from '../hooks/useForecastParamsState'
import bottomStyle from '../style/BottomCtaLiteLayout.module.scss'
import style from '../style/PublicTontinatorPage.module.scss'
import { PublicTontinatorBottomSectionProps } from '../types/PublicTontinatorBottomSection.types'
import MtlCompareBtns from './MtlCompareBtns'
import PublicTontinatorHomeButtons from './MtlHomeButtons'

/**
 * Bottom CTA section for the PublicSandboxTontinatorPage that displays action buttons
 * for comparing plans, viewing scenarios, and signing up/in. Renders different button
 * layouts based on comparison state and provides secure window.open functionality for
 * external navigation.
 */
const PublicTontinatorBottomSection = ({
  isCompareOpen,
  setIsCompareOpen,
  setOpenSliderPage,
  incomeForecastParams,
  setIncomeForecastParams,
  blueForecastParams,
  yellowForecastParams,
}: PublicTontinatorBottomSectionProps) => {
  const { isOnPublicPage } = useEmbeddedTon()

  const navigate = useCustomNavigation()
  return (
    <section className={style['public-tontinator-page__bottom-cta-container']}>
      <section className={bottomStyle['bottom-cta-lite-layout']}>
        {!isCompareOpen && (
          <PublicTontinatorHomeButtons
            incomeForecastParams={incomeForecastParams}
            onSeeOtherScenarios={() => {
              setOpenSliderPage(true)
            }}
            onCompareChoices={() => {
              setIsCompareOpen(true)
            }}
            onClickSignUpButton={() => {
              const serializedParams =
                serializeIncomeForecastParams(incomeForecastParams).toString()

              if (isOnPublicPage) {
                navigate(`${PUBLIC.SIGN_UP}?${serializedParams}`)
                return
              }

              const windowFeatures = 'noopener,noreferrer'
              const handle = window.open(
                `${window.origin}${PUBLIC.SIGN_UP}?${serializedParams}`,
                '_blank',
                windowFeatures
              )

              if (handle) {
                handle.opener = null
              }
            }}
            setIsOpenSignInModal={() => {
              if (isOnPublicPage) {
                navigate(PUBLIC.SIGN_IN)
                return
              }

              const windowFeatures = 'noopener,noreferrer'
              const handle = window.open(
                `${window.origin}${PUBLIC.SIGN_IN}`,
                '_blank',
                windowFeatures
              )

              if (handle) {
                handle.opener = null
              }
            }}
          />
        )}

        {isCompareOpen && (
          <MtlCompareBtns
            onClickPlan2={() => {
              setIsCompareOpen(false)
              setIncomeForecastParams(yellowForecastParams)
            }}
            onSeeOtherScenarios={() => {
              setOpenSliderPage(true)
            }}
            onClickBack={() => {
              setIsCompareOpen(false)
            }}
            blueForecastParams={blueForecastParams}
            yellowForecastParams={yellowForecastParams}
            onClickPlan1={() => {
              setIsCompareOpen(false)
              setIncomeForecastParams(blueForecastParams)
            }}
          />
        )}
      </section>
    </section>
  )
}

export default PublicTontinatorBottomSection
