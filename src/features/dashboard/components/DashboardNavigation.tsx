import { BoxCard } from '../../../common/components/card/BoxCard'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { renderNominalBalance } from '../../../common/utils/UtilFunctions'
import { DASHBOARD_NAVIGATION } from '../../../routes/Route'
import { useBankingService } from '../../banking/hooks/useBankingService'
import { isDisabled } from '../../DisabledLaunchFeatures'

/**
 * Renders a **desktop** only navigation component for the MyTontine dashboard
 */
const DashboardNavigation = ({ className }: { className?: string }) => {
  const { bankContext } = useBankingService()
  const { formatAmount } = useLocalization()

  const t = useTranslate()

  return (
    <nav className={`${className}`}>
      <BoxCard
        title={t('DASHBOARD.CHART.TITLE.EXPECTED.MONTHLY.INCOME')}
        subtitle={t('DASHBOARD.SUBTITLE.PLAN')}
        icon={ASSET.iconmtprojectedincome}
        href={DASHBOARD_NAVIGATION.FUNDED_PROGRESS}
      />
      <BoxCard
        title={t('NOMINAL_BALANCE_LABEL')}
        subtitle={
          renderNominalBalance(formatAmount, bankContext)
            ?.formattedAmountWithSymbol ?? '-'
        }
        icon={ASSET.iconaccountmenufundyourpenionUS}
        href={isDisabled ? undefined : DASHBOARD_NAVIGATION.NOMINAL_BALANCE}
        dataTestID={UI_TEST_ID.menuItemNominalBalance}
        alternative
        disabled={isDisabled}
      />
      <BoxCard
        title={t('INVITE_FRIENDS.GIVE_GET_STATEMENT')}
        subtitle={t('INVITE_FRIENDS.WITH_FRIENDS_CAPTION')}
        icon={ASSET.iconaccountrewardbicolor}
        href={DASHBOARD_NAVIGATION.DASHBOARD_REWARDS}
        dataTestID={UI_TEST_ID.menuItemDashboardRewards}
      />
      <BoxCard
        title={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
        subtitle={t('EXPLORE.DIFFERENT.SCENARIOS.DASHBOARD.SUB')}
        icon={ASSET.iconmttontinator}
        href={DASHBOARD_NAVIGATION.TONTINATOR}
        dataTestID={UI_TEST_ID.menuItemTontinator}
      />
    </nav>
  )
}

export default DashboardNavigation
