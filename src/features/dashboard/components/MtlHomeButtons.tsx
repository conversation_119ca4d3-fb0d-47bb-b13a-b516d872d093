import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Button from '../../../common/components/Button'
import ButtonAndClickableText from '../../../common/components/ButtonAndClickableText'
import Divider from '../../../common/components/Divider'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useEmbeddedTon } from '../hooks/useEmbededTon'
import style from '../style/BottomCtaLiteLayout.module.scss'
import { PublicTontinatorHomeButtonsProps } from '../types/PublicTontinatorHomeBtns.types'

/**
 * Renders the bottom CTA buttons for the Public Tontinator homepage
 * Displays different buttons based on authentication status
 */
const PublicTontinatorHomeButtons = ({
  onSeeOtherScenarios,
  onCompareChoices,
  onClickSignUpButton,
  setIsOpenSignInModal,
}: PublicTontinatorHomeButtonsProps) => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()
  const { isOnPublicPage } = useEmbeddedTon()

  return (
    <>
      {!isOnPublicPage && (
        <>
          <Icon
            fileName={ASSET.tontiRaisedHands}
            className={style[`bottom-cta-lite-layout__mascot`]}
          />
          <Icon
            fileName={ASSET.tontiHappy}
            className={style[`bottom-cta-lite-layout__mascot-1`]}
          />
        </>
      )}

      {isMobileOrTablet && (
        <Button
          dataTestID={UI_TEST_ID.openSliderPageButton}
          onClick={onSeeOtherScenarios}
          variant="alternative"
        >
          {t('CHECK_OTHER_SCENARIOS')}
        </Button>
      )}

      {!isOnPublicPage && (
        <Button
          dataTestID={UI_TEST_ID.openComparePlanButton}
          onClick={onCompareChoices}
          variant="blue"
        >
          {t('BUTTON_TO_COMPARE')}
        </Button>
      )}

      {isMobileOrTablet && <Divider />}

      <ButtonAndClickableText
        buttonVariant="primary--animated"
        buttonLabel={t('BUTTON_TO_PRE_REG')}
        buttonOnClick={onClickSignUpButton}
        textLabel={isOnPublicPage ? undefined : t('REFERRAL_BANNER_SIGN_IN')}
        textOnClick={() => setIsOpenSignInModal?.(true)}
        buttonDataTestID={UI_TEST_ID.registerButton}
        textDataTestID={UI_TEST_ID.loginBtnDesktop}
        buttonTrackActivity={{
          trackId: 'tontinator_open_register_screen',
          eventDescription: EVENT_DESC.tontinatorOpenRegisterScreen,
        }}
        textTrackActivity={{
          trackId: 'tontinator_login_text',
          eventDescription: EVENT_DESC.tontinatorLoginText,
        }}
      />
    </>
  )
}

export default PublicTontinatorHomeButtons
