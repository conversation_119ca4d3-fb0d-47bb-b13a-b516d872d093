import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import style from '../style/TontineDashboardLayout.module.scss'
import { TontineDashboardLayoutProps } from '../types/TontineDashboardLayout.types'
import DashboardNavigation from './DashboardNavigation'

/**
 * Child component to be rendered between the
 * `<DashboardNavigation />`
 *
 * Layout used for the Tontine Dashboard, the child component is
 * rendered in the middle middle, sandwiched by the DashboardNavigation
 */
const TontineDashboardLayout = ({ children }: TontineDashboardLayoutProps) => {
  const { isMobileOrTablet } = useDeviceScreen()

  return (
    <main className={style['tontine-dashboard-layout']}>
      <section className={style[`tontine-dashboard-layout__container`]}>
        {!isMobileOrTablet && (
          <DashboardNavigation
            className={style[`tontine-dashboard-layout__nav`]}
          />
        )}

        <section className={style[`tontine-dashboard-layout__main-content`]}>
          <ErrorBoundaryAndSuspense hideNavButton>
            {children}
          </ErrorBoundaryAndSuspense>
        </section>
      </section>
    </main>
  )
}

export default TontineDashboardLayout
