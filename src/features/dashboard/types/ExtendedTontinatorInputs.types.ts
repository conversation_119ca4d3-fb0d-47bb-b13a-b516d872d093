import {
  IncomeForecastParams,
  TrackActivity,
} from '../../../common/types/CommonTypes.types'

type ExtendedTontinatorInputsProps = {
  incomeForecastParams: IncomeForecastParams
  setIncomeForecastParams: (params: IncomeForecastParams) => void
  trackInvStrategies?: TrackActivity
  trackSex?: Array<TrackActivity>
  showSex?: boolean
  defaultOpen?: boolean
}

export type { ExtendedTontinatorInputsProps }
