import { ReactNode } from 'react'
import { CardVariantType } from '../../../common/types/Card.types'
import {
  InvestmentStrategyId,
  ReducedParams,
  TontinatorParamsMode,
  TrackActivity,
  ValidationData,
} from '../../../common/types/CommonTypes.types'
import { IncomeStatsExtendedProps } from '../../../common/types/DataDisplay.types'

type CompareIncomeStatsProps = {
  plan1: IncomeStatsExtendedProps
  plan2?: IncomeStatsExtendedProps
  currency?: string
}

type ComparePlanButtonsProps = {
  onClickPlan1?: () => void
  onClickPlan2?: () => void
  blueForecastParams?: ReducedParams
  yellowForecastParams?: ReducedParams
  onClickBack?: () => void
}

type CurrencyStatProps = {
  currency: string
  amount: number
  label: string
  disableAnimation?: boolean
  isLoading?: boolean
}

type GraphSwitchesProps = {
  breakevenLabel: string
  inflationLabel: string
  percentage: boolean
  handlePercentage: () => void
  breakeven: boolean
  handleBreakeven: () => void
  inflation: boolean
  handleInflation: () => void
  togglesVariant?: 'button'
  currency?: string
}

type InfoBannerUnverifiedDoBProps = {
  onClickClickableText: () => void
  infoBannerText?: string
  infoBannerClickableText: string
  trans?: ReactNode
}

type InvStrategiesDropdownProps = {
  value: InvestmentStrategyId
  onChange: (value: InvestmentStrategyId) => void
  label?: string
  errorMessage?: ValidationData
  validatorFunction?: () => void
  placeholder?: string
  readOnly?: boolean
  dataTestID?: string
  className?: string
  trackActivity?: TrackActivity
  defaultOpen?: boolean
}

type NextPayoutProps = {
  previousAmount: string
  nextAmount: string
  maxProgress: number
  currentProgress: number
  variant: CardVariantType
}

type OnboardingButtonsProps = {
  tontinatorButtonLabel: string
  tontinatorButtonOnClick: () => void
  signupButtonLabel: string
  signupButtonOnClick: () => void
  showSignupButton?: boolean
}

type ParamModesProps = {
  activeMode: TontinatorParamsMode
  onChange: (mode: TontinatorParamsMode) => void
}

export type {
  CompareIncomeStatsProps,
  ComparePlanButtonsProps,
  CurrencyStatProps,
  GraphSwitchesProps,
  InfoBannerUnverifiedDoBProps,
  InvStrategiesDropdownProps,
  NextPayoutProps,
  OnboardingButtonsProps,
  ParamModesProps,
}
