import { TrackSliderButtonWithId } from '../../../common/analytics/Analytics.types'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { SliderColorVariants } from '../../../common/types/Range.types'

type SliderTestIds = {
  incrementButtonDataTestID: string
  decrementButtonDataTestID: string
  boxValueDataTestID: string
}

interface AdditionalProps {
  hideRetirementSliders: boolean
  hideContributionSliders: boolean
  ageMonthRetirementSlider: boolean
  contributionSlidersClassName: string
  hideCurrentAgeSlider: boolean
  disableRetAgeDecrement: boolean
  sliderVariant: SliderColorVariants
  trackCurrentAgeSlider: TrackSliderButtonWithId
  trackRetirementSlider: TrackSliderButtonWithId
  trackOneTimeContribution: TrackSliderButtonWithId
  trackMonthlyContribution: TrackSliderButtonWithId
  forceDisableRetAgeDecrement: boolean
  retirementSliderTestIds: SliderTestIds
  oneTimeContributionTestIds: SliderTestIds
  showRetirementScheduler: boolean
  skipComparePlanRangeAdjustment: boolean
}
interface TontinatorInputsProps extends Partial<AdditionalProps> {
  formData: IncomeForecastParams
  setFormData: (formData: IncomeForecastParams) => void
}

export type { TontinatorInputsProps }
