import { ReactNode } from 'react'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type TontinatorDashboardProps = {
  incomeForecastParams: IncomeForecastParams
  /**
   * If this component is placed side-by-side the ID needs to be unique per
   * instance
   */
  svgID?: string
  isEmbedded?: boolean
  extendTopSection?: ReactNode
}

export type { TontinatorDashboardProps }
