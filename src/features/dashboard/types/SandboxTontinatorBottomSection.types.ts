import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type SandboxTontinatorBottomSectionProps = {
  isCompareOpen: boolean
  isMobileOrTablet: boolean
  setIsCompareOpen: (value: boolean) => void
  setIsParamsOpen: (value: boolean) => void
  setIncomeForecastParams: (params: IncomeForecastParams) => void
  blueForecastParams: IncomeForecastParams
  yellowForecastParams: IncomeForecastParams
}

export type { SandboxTontinatorBottomSectionProps }
