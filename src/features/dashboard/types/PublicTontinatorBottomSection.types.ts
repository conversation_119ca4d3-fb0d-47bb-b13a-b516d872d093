import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type PublicTontinatorBottomSectionProps = {
  isCompareOpen: boolean
  setIsCompareOpen: (value: boolean) => void
  setOpenSliderPage: (value: boolean) => void
  incomeForecastParams: IncomeForecastParams
  setIncomeForecastParams: (params: IncomeForecastParams) => void
  blueForecastParams: IncomeForecastParams
  yellowForecastParams: IncomeForecastParams
}

export type { PublicTontinatorBottomSectionProps }
