import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type PublicTontinatorProps = {
  incomeForecastParams: IncomeForecastParams
  setIncomeForecastParams: (params: IncomeForecastParams) => void
  setRegisterForm: (value: boolean) => void
  blueForecastParams: IncomeForecastParams
  setBlueForecastParams: (params: IncomeForecastParams) => void
  yellowForecastParams: IncomeForecastParams
  setYellowForecastParams: (params: IncomeForecastParams) => void
  isOpenSignInModal: boolean
  setIsOpenSignInModal: (value: boolean) => void
}

export type { PublicTontinatorProps }
