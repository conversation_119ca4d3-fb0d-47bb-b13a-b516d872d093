import { Dispatch, SetStateAction } from 'react'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type PublicTontinatorInputLayoutProps = {
  setRegisterForm: (value: boolean) => void
  isSliderPageOpen: boolean
  incomeForecastParams: IncomeForecastParams
  setIncomeForecastParams: (incomeForecastParams: IncomeForecastParams) => void
  comparison: boolean
  blueForecastParams: IncomeForecastParams
  setBlueForecastParams: (incomeForecastParams: IncomeForecastParams) => void
  yellowForecastParams: IncomeForecastParams
  setYellowForecastParams: (incomeForecastParams: IncomeForecastParams) => void
  setOpenSliderPage: Dispatch<SetStateAction<boolean>>
}

export type { PublicTontinatorInputLayoutProps }
