import { Dispatch, ReactNode, SetStateAction } from 'react'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type ShowFormsType = {
  infoForm: boolean
  sexForm: boolean
  ageForm: boolean
  contributionForm: boolean
  forecast: boolean
}

interface FormProps {
  formHeaderText?: string
  progress: string
  formData: IncomeForecastParams
  setFormData: Dispatch<SetStateAction<IncomeForecastParams>>
  goToStep: (step: keyof ShowFormsType) => void
}

type SexFormProps = FormProps
type AgeFormProps = FormProps
type ContributionFormProps = FormProps

interface OnboardingFormLayoutProps
  extends Omit<FormProps, 'formData' | 'setFormData' | 'goToStep'> {
  onNext?: () => void
  onBack?: () => void
  nextButtonDisabled?: boolean
  children?: ReactNode
}

export type {
  AgeFormProps,
  ContributionFormProps,
  OnboardingFormLayoutProps,
  SexFormProps,
  ShowFormsType,
}
