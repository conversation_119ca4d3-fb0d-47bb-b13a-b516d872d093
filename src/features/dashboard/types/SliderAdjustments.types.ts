import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { TontinatorUIParams } from '../../../common/types/SupportedCountries.types'

type SliderAdjustmentProps = {
  formData: IncomeForecastParams
  setFormData: (formData: IncomeForecastParams) => void
  params: TontinatorUIParams
  /**
   * Makes sure that the sliders on the compare plan page do not adjust the
   * contribution age on the tontinator page
   * If true avoids making modifying the currentAge value
   * by moving the retirement age slider
   */
  skipComparePlanRangeAdjustment?: boolean
}
export type { SliderAdjustmentProps }
