import { fromPromise } from 'xstate'
import { enrollFaceToFaceAuth } from '../../facetec-web-sdk/API'
import { ScanType } from '../../facetec-web-sdk/FaceBiometrics'
import {
  LegalMachineContext,
  LegalMachineEvent,
} from './agreements/types/LegalMachineTypes.types'
import {
  AuthMachineContext,
  AuthMachineEvent,
  AuthMachineSelf,
} from './authentication/types/AuthMachineTypes.type'

/**
 * Extracts all of the possible states that an xstate machine can be in
 */
const generateStatesObject = (states: object): object => {
  return Object.keys(states).reduce(
    (acc, state) => ({
      // biome-ignore lint/performance/noAccumulatingSpread: <TODO: See if need rule>
      ...acc,
      [state]: state,
    }),
    {} as object
  )
}

/**
 * Used for mocking a facescan response based in the passed in scan type
 */
const mockFaceScan = async ({
  scanType,
}: {
  scanType: ScanType
  authToken: string
}) => {
  const scanTypeToEndpoint: { [key: string]: string } = {
    ENROLLMENT: 'http://localhost:8081/face_scan/enroll',
  }

  if (scanTypeToEndpoint[scanType]) {
    const data = await enrollFaceToFaceAuth({
      api: scanTypeToEndpoint.ENROLLMENT,
      email: 'ignore not important',
      scanResultBody: {
        audit_trail_image: 'fake',
        face_scan: 'fake',
        low_quality_audit_trail_image: 'fake',
        session_id: 'fake',
      },
      mockResponse: true,
      authToken: 'ignore not important',
    })

    console.log('Success mocking face scan response for scan type:', scanType)

    return {
      authTokenInfo: data.auth_token_info,
      userAccountInfo: data.user_account_info,
      enrollmentCompleted: data.enrollment_complete,
      idScanCompleted: false,
    }
  }

  console.warn(
    'Did not provide valid scan type, allowed scan types are: ENROLLMENT'
  )
  return undefined
}

/**
 * Wraps a formerly known as AuthMachine "service" functions from xstate4 to
 * newly actor pattern using `fromPromise` in xstate5
 */
const promiseFuncToPromiseActor = <T>(
  promiseFunction: (
    context: AuthMachineContext,
    event: AuthMachineEvent,
    self: AuthMachineSelf
  ) => Promise<T>
) =>
  fromPromise(
    ({
      input,
    }: {
      input: {
        context: AuthMachineContext
        event: AuthMachineEvent
        self: AuthMachineSelf
      }
    }) => promiseFunction(input?.context, input?.event, input?.self)
  )

/**
 * Wraps a formerly known as LegalMachine "service" functions from xstate4 to
 * newly actor pattern using `fromPromise` in xstate5
 */
const legalPromiseToPromiseActor = <T>(
  promiseFunction: (
    context: LegalMachineContext,
    event: LegalMachineEvent
  ) => Promise<T>
) =>
  fromPromise(
    ({
      input,
    }: {
      input: { context: LegalMachineContext; event: LegalMachineEvent }
    }) => promiseFunction(input?.context, input?.event)
  )

/**
 * Util function used for providing inputs to promise actors. Generally typed,
 * will be broken down to actual statically typed arguments
 */
const promiseActorInput = <T>(params: T) => ({
  ...params,
})

export {
  generateStatesObject,
  legalPromiseToPromiseActor,
  mockFaceScan,
  promiseActorInput,
  promiseFuncToPromiseActor,
}
