type ReferralStat = {
  count_funded: number
  count_paid_out: number
  count_redeemed: number
  referral_code: string
  // This data is not from the backend
  editingLimitReached: boolean
}
type UserReferralStats = Array<ReferralStat>

type ReferralDetails = {
  referralCode: string
  redeemCount: number
}

type ReferralCodeProps = {
  label: string
  referralCode: string
}

type ReferralLinkModalProps = {
  referralLink: string
  setOpenEditModal: (isOpen: boolean) => void
  isOpen: boolean
}

type ReferralLinkViewProps = {
  editingLimitReached: boolean
  openEditReferralCodeModal?: (value: boolean) => void
  referralLink: string
  className?: string
  label: string
  hideDescription?: boolean
  socialMediaIcons?: 'row'
}

type ReferralStatsProps = {
  referralCode?: string
  redeemCount?: number | '-'
  hideCount?: boolean
}

type StatCardsProps = {
  redeemCount: number | '-'
  countTitle: string
  valueTitle: string
}

type UseEditReferralCodeProps = {
  referralLink: string
  setOpenEditModal: (isOpen: boolean) => void
}

export type {
  ReferralCodeProps,
  ReferralDetails,
  ReferralLinkModalProps,
  ReferralLinkViewProps,
  ReferralStat,
  ReferralStatsProps,
  StatCardsProps,
  UseEditReferralCodeProps,
  UserReferralStats,
}
