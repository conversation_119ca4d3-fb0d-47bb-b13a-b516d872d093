import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { ReferredCount } from '../../banking/types/BankTypes.type'
import ReferralStats from '../components/ReferralStats'
import style from '../style/ViewRewards.module.scss'

type ViewRewardsProps = {
  referralRewards?: Array<ReferredCount>
  onClickBack: () => void
}

/**
 * @note Feature not fully done from product design
 *
 * Renders cards of referral rewards
 */
const ViewRewards = ({ onClickBack }: ViewRewardsProps) => {
  const t = useTranslate()
  const { context } = useAccountService()

  return (
    <Layout
      onClickAction={onClickBack}
      containerMt="nomt"
      className={style['view-rewards']}
      pageTitle={t('REWARDS.INVITE_FRIENDS_PAGE_TITLE')}
      bottomSection={
        <NavigationButtons hideActionButton onClickFirst={onClickBack} />
      }
    >
      <ReferralStats
        redeemCount={
          context?.user_details?.referralDetails?.count_redeemed ?? 0
        }
      />
    </Layout>
  )
}

export default ViewRewards
