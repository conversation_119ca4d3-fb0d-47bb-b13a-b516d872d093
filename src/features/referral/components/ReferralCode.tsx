import { track } from '../../../common/analytics/Analytics'
import { ReferralLinkEvent } from '../../../common/analytics/EventData'
import Icon from '../../../common/components/Icon'
import InputLabel from '../../../common/components/InputLabel'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { copyToClipboard, debounce } from '../../../common/utils/UtilFunctions'
import style from '../style/ReferralCode.module.scss'
import { ReferralCodeProps } from '../types/ReferralTypes.type'
import { REFERRAL_CONSTANTS } from '../utils/consts'

/**
 * Debounced copy to clipboard in order not to spam toast messages
 */
const debouncedCopy = debounce(
  copyToClipboard,
  REFERRAL_CONSTANTS.SEARCH_DEBOUNCE_MS + 300
)

/**
 * Renders a referral code and allows the user to copy it if they click
 * on the referral code
 */
const ReferralCode = ({ label, referralCode }: ReferralCodeProps) => {
  const t = useTranslate()

  return (
    <article
      className={style['referral-code']}
      onClick={() => {
        debouncedCopy(referralCode, t('REFERRAL_LINK_TO_CLIPBOARD'))
        void track({
          event: ReferralLinkEvent.share,
          properties: {
            object_id: 'clipboard',
            object_value: referralCode,
          },
        })
      }}
    >
      <InputLabel label={label} />
      <div className={style['referral-code__text-container']}>
        <p className={style['referral-code__text']}>{referralCode}</p>
        <Icon fileName={ASSET.iconaccountcopy} />
      </div>
    </article>
  )
}

export default ReferralCode
