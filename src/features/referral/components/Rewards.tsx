import { useEffect } from 'react'
import LottieAnimation from '../../../common/components/LottieAnimation'
import { ANIMATION } from '../../../common/constants/Animations'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import ReferralDetails from '../pages/ReferralDetails'

/**
 * Renders a referral component where a first time user can join a
 * referral program or renders referral details for a user that has already joined
 */
const Rewards = () => {
  const { send, currentState } = useAccountService()

  useEffect(() => {
    send({
      type: 'GET_REFERRAL_STATS',
    })
  }, [send])

  if (currentState === 'FETCHING_REFERRAL_STATS') {
    return (
      <LottieAnimation
        autoplay
        loop
        animationName={ANIMATION.loadingLightBlueDots}
      />
    )
  }

  return <ReferralDetails />
}

export default Rewards
