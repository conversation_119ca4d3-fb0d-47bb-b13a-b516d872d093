@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define stat-cards */
.stat-cards {
  @include mixins.flex-layout($justify-content: space-between);
  @include mixins.no-user-select;
  width: 100%;
  gap: 30px;

  &__card {
    background-color: colors.$white;
    pointer-events: none;
    width: 100%;
  }

  &__card-con {
    width: 100%;
    // Extra height so Dean can write paragraphs
    height: 120px;
  }

  &__card-explainer-text {
    @include mixins.font-style(
      $color: colors.$gray-dark,
      $font-size: variables.$font-size-xs
    );
    margin-top: 0.3125rem;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    flex-wrap: wrap;

    &__card-con {
      height: 120px;
    }
  }
}
