@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define referral-banner */
.referral-banner {
  @include mixins.flex-layout($justify-content: center, $align-items: center);
  padding: 0 10px;
  background-color: colors.$blue;

  &__top-container {
    @include mixins.flex-layout(
      $flex-direction: column,
      $justify-content: center,
      $align-items: center
    );
    gap: 0.5rem;
  }

  &__modal-space {
    height: 200px;
  }

  &__text {
    @include mixins.font-style(
      $font-size: variables.$font-size-large,
      $font-weight: variables.$font-bold
    );
    @include mixins.no-user-select;
    text-align: center;
    text-decoration: underline;
    cursor: pointer;
  }

  &__headline {
    @include mixins.font-style(
      $font-size: variables.$font-size-xxlarge,
      $color: colors.$white,
      $font-weight: variables.$font-bold
    );
    text-align: center;
  }

  &__sub-headline {
    @include mixins.font-style(
      $font-size: variables.$font-size-ml,
      $color: colors.$white
    );
    text-align: center;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__headline {
      @include mixins.font-style(
        $font-size: variables.$font-size-large,
        $color: colors.$white,
        $font-weight: variables.$font-bold
      );
    }

    &__sub-headline {
      @include mixins.font-style(
        $font-size: variables.$font-size-s,
        $color: colors.$white
      );
    }

    &__text {
      @include mixins.font-style(
        $font-size: variables.$font-size-m,
        $font-weight: variables.$font-bold,
        $line-height: 5px
      );
    }
  }
}
