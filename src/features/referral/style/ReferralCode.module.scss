@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

/** @definer referral-code */
.referral-code {
  width: 100%;

  &__text-container {
    @include mixins.flex-layout($justify-content: space-between);
    @include mixins.no-user-select;
    background-color: colors.$gray-faint;
    border-radius: variables.$rounded;
    cursor: pointer;
    padding: 1.25rem;
  }

  &__text {
    @include mixins.font-style(
      $font-size: variables.$font-size-m,
      $font-weight: variables.$font-semibold
    );
  }
}
