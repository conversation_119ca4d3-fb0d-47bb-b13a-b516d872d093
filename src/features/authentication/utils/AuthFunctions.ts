// Handles storing tokens for browser sessions

const authTokenKey = 'authTokenStorage'
const websiteOriginKey = 'websiteOrigin'

// lite uses localstorage because it persists sessions for 2 weeks
// full version uses sessionStorage because sessions last for 30 mins
const storage = sessionStorage

/**
 * Mtl uses localstorage because it persists sessions for 2 weeks
 * Full version uses sessionStorage because sessions last for 30 mins
 */
const setAuthToken = (authToken: string) => {
  if (!authToken) {
    throw new TypeError('No auth token provided')
  }
  storage?.setItem(authTokenKey, authToken)
}

const getAuthToken = () => {
  const parsedAuthToken = storage?.getItem(authTokenKey as string) as string

  if (!parsedAuthToken) {
    return ''
  }

  return parsedAuthToken
}

const removeAuthToken = () => storage?.removeItem(authTokenKey)

const setWebsiteOrigin = (origin: string) => {
  localStorage?.setItem(websiteOriginKey, origin)
}

const getWebsiteOrigin = () => {
  const origin = localStorage?.getItem(websiteOriginKey) as string
  if (!origin) {
    return ''
  }
  return origin
}

const removeWebsiteOrigin = () => localStorage?.removeItem(websiteOriginKey)

export {
  authTokenKey,
  getAuthToken,
  getWebsiteOrigin,
  removeAuthToken,
  removeWebsiteOrigin,
  setAuthToken,
  setWebsiteOrigin,
}
