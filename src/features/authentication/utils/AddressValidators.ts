import { StateData } from '../../../common/types/CommonTypes.types'

/**
 * Checks if a zip code matches the passed in USA state
 */
const isZipInState = (zipCode: number, state?: StateData): boolean => {
  // Check if state data is provided
  if (!state) {
    return false
  }

  // Loop through the zip ranges
  for (let zipIndex = 0; zipIndex < state.zip_ranges.length; zipIndex++) {
    const zipRange = state.zip_ranges[zipIndex]

    // If the zip code is within the range, return true
    if (zipCode >= zipRange.min && zipCode <= zipRange.max) {
      return true
    }
  }

  // If no match is found, return false
  return false
}

export { isZipInState }
