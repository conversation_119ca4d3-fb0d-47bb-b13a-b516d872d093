import braStates from '../../../common/constants/bra-states.json'
import usaStates from '../../../common/constants/usa-states.json'
import { StateData } from '../../../common/types/CommonTypes.types'
import { PageSetting, ScanRoutes } from '../types/FaceScan.type'

/**
 * Authentication-related constants
 */
export const AUTH_CONSTANTS = {
  // Component behavior config
  OTP_FIELDS: 6,
  PIN_INPUT_FIELDS: 4,
  HIDE_PIN: 'password',
  STEP_ONE: 1,
  STEP_TWO: 2,

  // Debounce times
  RESEND_EMAIL_TIMER_MILLISECONDS: 30_000,

  // Referral program values
  REFERRAL_CODE_EDITING_LIMIT: 2,
  REFERRAL_CODE_PREFIX: 'u/',
  CURRENT_AGE_MIN: 18,

  // Browser storage keys
  FACETEC_INIT_KEY: 'faceTecInitializedSuccessfully',
  PHONE_ADDED_KEY: 'phone_added',
  DOB_MODAL_KEY: 'dob_modal_show',
  CLOSED_SESSION_EXPIRE_MODAL: 'closed_session_expire_modal',

  // Session management
  SESSION_ABOUT_TO_EXPIRE_SECONDS: 120,
  SESSION_EXTENSION_LIMIT: 3,

  // Localization
  FALLBACK_DIAL_CODE: '+1',

  // Validation
  MAX_FEEDBACK_LENGTH: 1_200,
} as const

const ScanParamToPageSetting: { [key in ScanRoutes]: PageSetting } = {
  'enroll-face': {
    pageTitle: 'FACESCAN.REGISTER.FACE',
    scanType: 'ENROLLMENT',
    scanError: 'BIOMETRICS.FACE_ENROLL_FAILED',
  },
  'match-id': {
    pageTitle: 'FACE_SCAN.ID_SCAN',
    scanType: 'PHOTO_ID_SCAN',
    scanError: 'BIOMETRICS.FACE_MATCH_TO_ID_FAILED',
  },
  'auth-scan': {
    pageTitle: 'FACE_SCAN.AUTHENTICATION',
    scanType: 'AUTHENTICATION',
    scanError: 'BIOMETRIC.CARD_FACESCAN_ERROR',
  },
}

const sessionTerminationMessage = {
  normalExpiredNotification: 'ACCOUNT.MODAL_LOGIN_EXPIRED_MESSAGE',
  sessionErrorNotification: 'LOGIN.SESSION_ERROR',
} as const

const appDomains = [
  'https://ton.money',
  'https://app.mytontine.com',
  'https://5t4g1ng.robotontine.com',
  'http://localhost:9000',
  'http://localhost:8080',
  'https://tontine.com',
  'https://tontineira.com',
  'https://ira.tontine.com',
]

const allowedFileTypesForAddressVerification = [
  'application/pdf',
  'image/png',
  'image/jpeg',
  'image/heic',
] as const

const flowIsFinishedOnStatues = ['SUBMITTED', 'APPROVED']

const FILE_SIZE_LIMIT_MB = 75 * 1024 * 1024

const countriesWithStates: Record<string, Array<StateData>> = {
  USA: usaStates,
  BRA: braStates,
}

const COUNTRY_ZIP_FORMAT: Record<string, [number, number]> = {
  USA: [5, 4],
  BRA: [5, 3],
}

export {
  allowedFileTypesForAddressVerification,
  appDomains,
  countriesWithStates,
  COUNTRY_ZIP_FORMAT,
  FILE_SIZE_LIMIT_MB,
  flowIsFinishedOnStatues,
  ScanParamToPageSetting,
  sessionTerminationMessage,
}
