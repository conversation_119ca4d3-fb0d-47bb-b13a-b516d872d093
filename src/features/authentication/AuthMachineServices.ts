import axios, { AxiosResponse } from 'axios'
import { track } from '../../common/analytics/Analytics'
import { AuthEvent } from '../../common/analytics/EventData'
import { API } from '../../common/api/API'
import { axiosConfig } from '../../common/api/RequestConfig'
import { API_STATUS } from '../../common/constants/ApiErrors'
import { BackendErrorId } from '../../common/constants/ApiErrors.types.js'
import {
  IncomeForecastParams,
  IncomeForecastRequestBody,
  RaApiError,
} from '../../common/types/CommonTypes.types'
import {
  adjustAndConvertToAgeMonthString,
  logForecastParams,
  parseLiteParams,
  parseLiteReferralData,
  verifiedPersonalDetails,
  writeToConsoleAndIssueAlert,
} from '../../common/utils/UtilFunctions'
import { envs } from '../../config/envs'
import { FaceTecLng } from '../../config/facetec-strings'
import i18n from '../../config/i18n'
import { isDisabled } from '../DisabledLaunchFeatures'
import { parseIncomeForecastParams } from '../dashboard/utils/UtilFunctions'
import {
  ReferralStat,
  UserReferralStats,
} from '../referral/types/ReferralTypes.type'
import { ForecastResult } from '../visualization/types/Visualization.types'
import { _initSDK, _startFaceScan } from './Biometrics'
import {
  AuthMachineContext,
  AuthMachineEvent,
  AuthMachineSelf,
  AuthTokenInfo,
  LiteData,
  LitePensionPlan,
  MagicLoginResponse,
  PayoutAccountDetails,
  UserDetails,
  UserResidency,
} from './types/AuthMachineTypes.type'
import {
  getAuthToken,
  removeAuthToken,
  setAuthToken,
  setWebsiteOrigin,
} from './utils/AuthFunctions'
import { AUTH_CONSTANTS } from './utils/consts'

const { environment, envColor } = envs

const envToEmailEnv: Record<string, string> = {
  production: 'prod',
  staging: 'staging',
  development: 'dev',
} as const

const emailEnvironment =
  envToEmailEnv[environment as keyof typeof envToEmailEnv] ?? 'dev'

const withCredentials = false

let warningTimeout: NodeJS.Timeout | undefined
let expirationTimeout: NodeJS.Timeout | undefined

const authTokenService = async (
  context: AuthMachineContext,
  _: AuthMachineEvent,
  self: AuthMachineSelf
) => {
  try {
    // Clear existing timers if they exist
    clearTimeout(warningTimeout)
    clearTimeout(expirationTimeout)

    let hasIssuedEvent =
      // Has issues event is set to true because the user decided not to extend
      // their session, so we do not spam the user if they reload with F5
      sessionStorage?.getItem(AUTH_CONSTANTS?.CLOSED_SESSION_EXPIRE_MODAL) ??
      false

    // Session extension is limited to 3 times per login session
    const hasExtendedSessionExtensionLimit =
      context?.extendedSessionTimes === AUTH_CONSTANTS.SESSION_EXTENSION_LIMIT

    const { remainingTime: sessionRemainingTime } = context

    //Server has responded with 0 seconds, meaning there is no active session
    //for the sent auth token or the auth token was invalid
    if (sessionRemainingTime && sessionRemainingTime <= 0) {
      return Promise.reject(new Error('No active session on server'))
    }

    if (sessionRemainingTime && sessionRemainingTime > 0) {
      if (!hasIssuedEvent && !hasExtendedSessionExtensionLimit) {
        const { SESSION_ABOUT_TO_EXPIRE_SECONDS } = AUTH_CONSTANTS
        // No need to do any additional logic, because we only
        // issue this event once when the session is about end
        // so when the user refreshes they do not see the modal again
        // as per design guidelines
        const warningSeconds =
          sessionRemainingTime - SESSION_ABOUT_TO_EXPIRE_SECONDS

        const secondsRemaining =
          sessionRemainingTime <= SESSION_ABOUT_TO_EXPIRE_SECONDS
            ? sessionRemainingTime
            : SESSION_ABOUT_TO_EXPIRE_SECONDS

        warningTimeout = setTimeout(() => {
          self.send({
            type: 'SESSION_ABOUT_TO_EXPIRE',
            payload: {
              secondsRemaining,
              notifyUser: true,
            },
          })
          hasIssuedEvent = true
          clearTimeout(warningTimeout)
        }, warningSeconds * 1_000)
      }

      expirationTimeout = setTimeout(() => {
        self.send({
          type: 'EXPIRED_SESSION',
          payload: {
            reason: 'No active session for given auth token on server',
            notifyUiWith: 'normalExpiredNotification',
            renderNotification: true,
          },
        })
        clearTimeout(expirationTimeout)
      }, sessionRemainingTime * 1_000)

      return Promise.resolve(sessionRemainingTime)
    }
  } catch (error) {
    // User has an invalid auth token or the auth token is expired
    if (
      (error as { response: { status: number } })?.response?.status ===
        API_STATUS.UNAUTHORIZED ||
      (error as { response: { status: number } })?.response?.status ===
        API_STATUS.FORBIDDEN
    ) {
      // session error
      self.send({
        type: 'EXPIRED_SESSION',
        payload: {
          reason: 'Session error, bad auth token or stale auth token',
          notifyUiWith: 'sessionErrorNotification',
          renderNotification: true,
        },
      })
    }
    writeToConsoleAndIssueAlert({ error })
  }

  return Promise.resolve(undefined)
}

/**
 * Fetches user account information
 */
const fetchUserDetails = async (
  _: AuthMachineContext,
  event?: AuthMachineEvent
): Promise<UserDetails | undefined> => {
  try {
    const response = await axios.get(
      API.userDetails,
      axiosConfig({
        withCredentials,
        signal: event?.payload?.abortController?.signal,
        authToken: getAuthToken(),
      })
    )

    const { status, data } = response as {
      status: number
      data: UserDetails
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(data)
      return verifiedPersonalDetails(data)
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Redeems a magic link token for an auth token, if magic token is valid.
 * Otherwise a state transition won't happen and the user will not enter
 * authenticated state
 */
const redeemMagicToken = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
): Promise<
  | {
      userAccountInfo: UserDetails
      authTokenInfo: AuthTokenInfo
      forecastParams: object | null
    }
  | undefined
> => {
  try {
    if (!event?.payload?.magic_login_token) {
      throw new TypeError(
        `No magic login token  >> ${JSON.stringify(event?.payload)} <<`
      )
    }

    const response = await axios.post(
      API.loginMagicLinkNewTab,
      event?.payload?.magic_login_token,
      axiosConfig({
        signal: event?.payload?.abortController?.signal,
        rawBodyRequest: true,
      })
    )

    const { status, data } = response as {
      status: number
      data: MagicLoginResponse
    }

    if (status === API_STATUS.OK) {
      track({
        event: AuthEvent.logged_in,
        properties: {
          object_id: 'email',
        },
      })

      setAuthToken(data?.authTokenInfo?.authToken)

      const finalData = {
        ...data,
        isAuthenticated: Boolean(getAuthToken()),
      }

      event?.payload?.successCallback?.(finalData)
      return finalData
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Sends an email to the user in order for them to perform magic login
 */
const sendMagicLoginEmail = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    // TODO: Add email check for payload and output
    const { status } = await axios.post(
      API.sendEmailMagicLinkNewTab,
      {
        //Registration issues an internal event with `data`
        email: event?.payload?.email ?? event?.output?.payload?.email,
        forecastParams:
          event?.payload?.forecastUserData ??
          event?.output?.payload?.forecastUserData,
        uasHost: emailEnvironment,
      },
      // Timeout after 60 seconds
      axiosConfig({ timeout: 60_000 })
    )

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)
      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Terminates the `auth_token` on the server and logs the user out of the
 * application
 */
const logout = async (_: AuthMachineContext, event: AuthMachineEvent) => {
  try {
    const response = await axios.delete(
      API.logout,
      axiosConfig({
        withCredentials,
        authToken: getAuthToken(),
      })
    )

    const { status } = response

    // The isAuthenticated is set to false in the auth machine guards, when the
    // context is being cleared out
    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)
      removeAuthToken()

      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}
/**
 * Registers an user account with the UAS
 */
const registerUser = async (_: AuthMachineContext, event: AuthMachineEvent) => {
  // Needs to be outside the try catch block because the user can sign up on
  // an EXCEPTION FROM THE API!, and we need the forecast params they signed up with
  const {
    email,
    first_name,
    last_name,
    news,
    marketing,
    terms_and_conditions,
    referral_code,
    sex,
    birth_year,
    residency,
    payout_start_year,
    forecastParams,
  } = event?.payload as {
    email: string
    first_name: string
    last_name: string
    news: boolean
    marketing: boolean
    terms_and_conditions: number
    referral_code?: string
    sex: string
    birth_year: number
    //Needs to be ALPHA 3 code
    residency: string
    payout_start_year: number
    forecastParams: object
  }

  try {
    const response = await axios.post(
      API.register,
      {
        email,
        first_name,
        last_name,
        news,
        marketing,
        terms_and_conditions,
        referral_code,
        sex,
        birth_year,
        residency,
        dev_prod: emailEnvironment,
        // if true sends an email to the user from the backend
        //deprecated
        web_signup: false,
        payout_start_year,
        forecast_params: forecastParams
          ? { ...forecastParams, write_draft_plan: false }
          : null,
      },
      axiosConfig()
    )

    const { status, data } = response as {
      status: number
      data: {
        id: RaApiError
      }
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(data)

      track({
        event: AuthEvent.signup,
        properties: {
          object_id: referral_code ? 'no_referral_code' : 'referral_code',
          object_value: forecastParams,
        },
      })

      return event
    }
  } catch (error) {
    // Any other error
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })

    // Need to return the event because it contains the email data that the
    //SEND_MAGIC_LINK state needs to access
    return event
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Updates user's unverified account info
 */
const updateUserAccountInfo = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const response = await axios.post(
      API.editUserDetails,
      {
        ...event?.payload,
      },
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)

      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Extends user's current session with pin
 */
const extendSession = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.pin) {
      throw new TypeError(
        `No pin found  >> ${JSON.stringify(event?.payload)} <<`
      )
    }

    const { pin } = event?.payload as {
      pin: string
    }

    const response = await axios.post(
      API.extendLoginSessionWithPin,
      // " " Is absolutely necessary because application/json
      // coverts the string 1234 to a number
      `"${pin}"`,
      axiosConfig({ rawBodyRequest: true, authToken: getAuthToken() })
    )

    const { status, data } = response as {
      status: number
      data: AuthTokenInfo
    }

    if (status === API_STATUS.OK) {
      const finalData = { authTokenInfo: data, isAuthenticated: true }
      event?.payload?.successCallback?.(finalData)
      setAuthToken(finalData?.authTokenInfo?.authToken)

      return finalData
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Creates a pin for the user's account
 */
const createNewPin = async (_: AuthMachineContext, event: AuthMachineEvent) => {
  try {
    if (!event?.payload?.pin) {
      throw new TypeError(
        `No pin found  >> ${JSON.stringify(event?.payload)} <<`
      )
    }
    const { pin } = event?.payload as {
      pin: string
    }

    const response = await axios.post(
      API.savePin,
      {
        pin,
      },
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.({ pin_set: true })
      return { pin_set: true }
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Changes the user's current pin to a new one.
 */
const changeCurrentPin = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.oldPin || !event?.payload?.newPin) {
      throw new TypeError(
        `No pin found  >> ${JSON.stringify(event?.payload)} <<`
      )
    }

    const { newPin, oldPin } = event?.payload as {
      newPin: string
      oldPin: string
    }

    const response = await axios.post(
      API.changePin,
      {
        pin: newPin,
      },
      axiosConfig({
        userPin: oldPin,
        withCredentials,
        authToken: getAuthToken(),
      })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)

      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Sends a pin reset email
 */
const sendPinResetEmail = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const response = await axios.post(
      API.forgotPin,
      {
        dev_prod: emailEnvironment,
      },
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)
      return event
    }
  } catch (error) {
    console.log('DID YOU ENTER HERE?')
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Resets the user's pin with an reset token
 */
const resetPin = async (_: AuthMachineContext, event: AuthMachineEvent) => {
  try {
    if (!event?.payload?.pin || !event?.payload?.reset_token) {
      throw new TypeError(
        `No pin or reset token found  >> ${JSON.stringify(event?.payload)} <<`
      )
    }

    const { pin, reset_token } = event?.payload as {
      pin: string
      reset_token: string
    }

    const response = await axios.post(
      API.resetPin,
      {
        pin,
        reset_token,
      },
      axiosConfig()
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)
      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Generates a referral code for the user, server side if user does not have a
 * referral code. Also allows the user to create a new referral with their
 * custom params, BUT only once
 */
const createReferralCode = async (
  context: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (
      !event?.payload?.referralCode ||
      typeof event?.payload?.referralCode !== 'string'
    ) {
      throw new TypeError(
        `No referral code provided or code not a string got >> ${event?.payload?.referralCode} <<`
      )
    }

    const { referralCode, refCodePrefix } = event.payload

    if (typeof refCodePrefix !== 'string') {
      throw new TypeError(
        `Referral code prefix needs to be a string, got >> ${refCodePrefix} <<`
      )
    }

    let requestBody = referralCode

    if (refCodePrefix) {
      requestBody = `${refCodePrefix}${referralCode}`
    }

    const response = await axios.post(
      API.createReferralCode,
      requestBody,
      axiosConfig({
        rawBodyRequest: true,
        withCredentials,
        authToken: getAuthToken(),
      })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      const userDetails = {
        referralDetails: {
          // Keep the count, only update the referral code
          ...context?.user_details?.referralDetails,
          referral_code: `${refCodePrefix}${referralCode}`,
          //TODO: For now until backend sorts out RETURNING the editing limit
          editingLimitReached: true,
        },
      } as UserDetails

      event?.payload?.successCallback?.(userDetails)

      return userDetails
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 *
 * Adds unverified phone number to the user's account. If the phone number is
 * not verified within certain amount of time, then the phone number will be
 * removed.
 *
 */
const addUnverifiedPhoneNumber = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.phone_number) {
      throw new TypeError(
        `No phone number found >> ${JSON.stringify(event?.payload)} <<`
      )
    }

    const { phone_number } = event?.payload as {
      phone_number: string
    }

    const response = await axios.post(
      API.addPhoneNumber,
      {
        phone_number,
      },
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status, data } = response as {
      status: number
      data: {
        verification_code_expiry_seconds: number
      }
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(data)
      return data
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Verifies the unverified phone number, with the SMS code sent to the
 * unverified phone number,
 */
const verifyPhoneNumber = async (
  context: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.verification_code) {
      throw new TypeError(
        `No verification code found >> ${JSON.stringify(event?.payload)} <<`
      )
    }

    const { verification_code } = event?.payload as {
      verification_code: string
    }

    const response = await axios.post(
      API.verifyPhoneNumber,
      {
        verification_code,
      },
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.({
        phone_number: event?.payload?.phone_number,
      })
      // Returns the passed in verified phone number so the auth machine can
      // update the context with it
      return {
        phone_number: event?.payload?.phone_number,
        kyc_status: {
          ...context?.user_details?.kyc_status,
          L1: {
            ...context?.user_details?.kyc_status?.L1,
            requirements: {
              ...context?.user_details?.kyc_status?.L1?.requirements,
              phone_verified: true,
            },
          },
        },
      }
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Schedules the user's account for closing
 */
const closeUserAccount = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const { pin, closureFeedback } = event?.payload as {
      pin: string
      closureFeedback: string
    }

    const response = await axios.post(
      API.closeAccount,
      closureFeedback,
      axiosConfig({
        userPin: pin,
        rawBodyRequest: true,
        withCredentials,
        authToken: getAuthToken(),
      })
    )

    const { status, data } = response as {
      status: number
      data: string
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.({ closure_scheduled_time: data })
      return { closure_scheduled_time: data }
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Cancel the scheduled account closing action on the backend
 */
const cancelAccountClosing = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const response = await axios.post(
      API.cancelClosingAccount,
      null,
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.({ closure_scheduled_time: null })
      return { closure_scheduled_time: null }
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Updates or adds a new payout account for a logged in user and returns the
 * event with it's payload to the `bankMachine`
 */
const updatePayoutDetails = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
): Promise<AuthMachineEvent | undefined> => {
  const { payoutDetails } = event?.payload as {
    payoutDetails: PayoutAccountDetails
  }

  try {
    if (!payoutDetails) {
      throw new TypeError(
        `Payout details key not found got >> ${JSON.stringify(
          payoutDetails
        )} <<`
      )
    }
    const { status } = await axios.post(
      API.updatePayoutAccount,
      {
        account: payoutDetails?.account,
      },
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )
    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event.payload)

      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  }

  return undefined
}

/**
 * Deletes a user's payout account if there is one
 */
const deletePayoutDetails = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
): Promise<AuthMachineEvent | undefined> => {
  try {
    const { status } = await axios.delete(API.deletePayoutAccount, {
      ...axiosConfig({ withCredentials, authToken: getAuthToken() }),
    })

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event.payload)
      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  }

  return undefined
}

/**
 * Designed to be used with auth machine's face scan states
 *
 * Initializes the biometrics SDK and starts a face scan. Issues a callback if
 * the
 */
const startFaceScan = async (
  _: AuthMachineContext,
  event: AuthMachineEvent,
  self: AuthMachineSelf
) => {
  try {
    if (!event?.payload) {
      throw new Error(`Empty payload`)
    }

    const { email, scanType } = event.payload

    if (!email || !scanType) {
      throw new Error(`Missing email or scan type`)
    }

    let isSDKInitialized = false

    if (!isDisabled) {
      const isProd = environment === 'production' || environment === 'staging'
      let configResponse: undefined | AxiosResponse

      if (isProd) {
        configResponse = await axios.get(
          API.getFacetecConfig,
          axiosConfig({
            signal: event?.payload?.abortController?.signal,
            authToken: getAuthToken(),
          })
        )

        if (!configResponse?.data?.encryption_key) {
          throw new TypeError(
            'Did not get encryption key info from server, face scan cannot start'
          )
        }
      }

      const { data } =
        configResponse ??
        ({} as {
          status: number
          data: {
            encryption_key: {
              domains: string
              expiryDate: string
              key: string
            }
          }
        })

      const facetecConfig = {
        PublicFaceScanEncryptionKey: envs.facetecPublicFaceScanEncryptionKey,
        deviceKeyIdentifier: envs.facetecDeviceKeyIdentifier,
        productionKey: data?.encryption_key
          ? JSON.stringify(data?.encryption_key)
          : undefined,
        isProd,
      }

      isSDKInitialized = (await _initSDK({
        ...facetecConfig,
        languageObject: FaceTecLng[i18n.language],
      })) as boolean
    }

    // Starts a face scan only if sdk is init successfully
    if (isSDKInitialized) {
      const data = await _startFaceScan({
        email,
        authToken: getAuthToken(),
        scanType,
        onEnrollmentOnlyDone: (data) => {
          setAuthToken(data?.authTokenInfo?.authToken)
          // Needed to upgrade permissions in context
          self.send({
            type: 'FACE_ENROLL_COMPLETED',
            output: {
              ...data,
              isAuthenticated: true,
            },
          })
        },
      })

      if (!data) {
        throw new Error('Fatal error, no data resolved from face scan!')
      }

      const { idScanCompleted, enrollmentCompleted } = data

      event?.payload?.successCallback?.({
        enrollmentCompleted,
        idScanCompleted,
      })

      setAuthToken(data?.authTokenInfo?.authToken)

      return { ...data, isAuthenticated: true }
    }
    // Fatal error for the AuthMachine to transition to error state
    throw new Error('Unknown error occurred')
  } catch (exception) {
    // Ignore these SDK error codes
    const USER_CANCELLED_SCAN = 7
    const CONTEXT_SWITCH = 3
    const TIMEOUT = 2
    // Ignore these frontend SDK error codes
    const scanErrorsToIgnore = [USER_CANCELLED_SCAN, CONTEXT_SWITCH, TIMEOUT]

    if (exception) {
      if (
        (
          exception as {
            error?: {
              sessionResultStatusCode?: number
              idScanResultStatusCode?: number
            }
          }
        )?.error
      ) {
        const { error } = exception as {
          error: {
            sessionResultStatusCode?: number
            idScanResultStatusCode?: number
          }
        }

        if (
          // Ignore if the user cancels the scan or if a context switch occurs,
          // since these are not considered actual scan errors and are errors based
          // on user's interaction with the scan SDK
          !scanErrorsToIgnore.includes(
            error?.sessionResultStatusCode ?? error?.idScanResultStatusCode ?? 0
          )
        ) {
          // Error is thrown here as well
          writeToConsoleAndIssueAlert({
            error: error,
            failureCallback: event?.payload?.failureCallback,
          })
          removeAuthToken()
        }
      }
    }

    // Fatal error, if this is thrown
    throw new Error(
      `Error from startFaceScan >> ${JSON.stringify(exception)} <<`
    )
  }
}

/**
 * Refreshes the session with a refresh token
 */
const refreshSession = async (
  context: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const response = await axios.post(
      API.refreshSession,
      null,
      axiosConfig({
        withCredentials,
        rawBodyRequest: true,
        signal: event?.payload?.abortController?.signal,
        authToken: getAuthToken(),
      })
    )

    const { status, data } = response as {
      status: number
      data: AuthTokenInfo
    }

    setAuthToken(data?.authToken)

    const userDetails = await fetchUserDetails(context, {
      // Does not do anything, just to add type safety since the
      // fetchUserDetails expects a auth machine event type
      type: 'FETCH_USER_ACCOUNT',
    })

    if (!userDetails) {
      throw new Error(
        'Could not GET user account info with the /refresh, something went wrong'
      )
    }

    if (status === API_STATUS.OK) {
      const finalData = {
        authTokenInfo: data,
        userAccountInfo: userDetails,
        isAuthenticated: true,
      }

      event?.payload?.successCallback?.(finalData)
      return finalData
    }
  } catch (error) {
    removeAuthToken()
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Resend verification email, which can be only done in the Lite build of the app
 */
const resendVerificationEmailForLite = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const response = await axios.post(
      API.resendVerificationEmail,
      event?.payload?.email ?? event?.output?.payload?.email,
      axiosConfig({
        rawBodyRequest: true,
      })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)
      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Verifies user's email by providing a token and returns their referral code
 * and referral details
 */
const liteAuth = async (_: AuthMachineContext, event: AuthMachineEvent) => {
  try {
    const storedAuthToken = getAuthToken()

    if (!event?.payload?.verifyToken && !storedAuthToken) {
      throw new TypeError(
        `Did not get a verify token and could not find a auth token in local storage`
      )
    }

    const response = await axios.get(API.liteAuth, {
      ...axiosConfig({
        signal: event?.payload?.abortController?.signal,
        authToken: storedAuthToken,
        emailToken: event?.payload?.verifyToken,
      }),
    })

    const { status, data } = response as {
      status: number
      data: {
        auth_token: string
        forecast_params: IncomeForecastRequestBody
        referral_info: UserReferralStats
      }
    }

    const pensionPlan =
      parseLiteParams(data.forecast_params) ?? ({} as LitePensionPlan)

    const liteData: LiteData = {
      referralDetails: parseLiteReferralData(data.referral_info),
      pensionPlan,
      liteAuthToken: data.auth_token,
      // replace trailing slash it is not needed
      websiteOrigin: event?.payload?.websiteOrigin ?? '',
    }

    if (status === API_STATUS.OK) {
      setAuthToken(liteData?.liteAuthToken)
      setWebsiteOrigin(liteData?.websiteOrigin ?? '')

      track({
        event: AuthEvent.logged_in,
        properties: {
          object_id: 'email',
        },
      })

      const finalData = {
        ...liteData,
        isAuthenticated: true,
      }

      event?.payload?.successCallback?.(finalData)
      return finalData
    }
  } catch (error) {
    // Clean storage if obsolete data is found, and it is not
    // aborted request
    if (!axios.isCancel(error)) {
      removeAuthToken()
    }

    type ErrorType = {
      response: {
        data: {
          id: BackendErrorId
        }
      }
    }
    // Issue alert on every error except for UAS-AUTH-TOKEN-2 and UAS-AUTH-TOKEN-3 since it is
    // normal session expiry
    if (
      (error as ErrorType)?.response?.data?.id !== 'UAS-AUTH-TOKEN-2' &&
      (error as ErrorType)?.response?.data?.id !== 'UAS-AUTH-TOKEN-3'
    ) {
      writeToConsoleAndIssueAlert({
        error,
        failureCallback: event?.payload?.failureCallback,
      })
    }
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Gets user's referral information and binds all the info into one easy to use
 * referral details object
 */
const getReferralStats = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    const response = await axios.get(
      API.getReferralStats,
      axiosConfig({
        signal: event?.payload?.abortController?.signal,
        withCredentials,
        authToken: getAuthToken(),
      })
    )

    const { status, data } = response as {
      status: number
      data: UserReferralStats
    }

    if (!data) {
      throw new Error(`No data received from API`)
    }

    // Merge all referral info into one easy to use object
    const unifiedData: ReferralStat = data.reduce(
      (defaultCodeData, customCodeData) => {
        return {
          count_funded:
            defaultCodeData.count_funded + customCodeData.count_funded,
          count_paid_out:
            defaultCodeData.count_paid_out + customCodeData.count_paid_out,
          count_redeemed:
            defaultCodeData.count_redeemed + customCodeData.count_redeemed,
          // The default code will be used if no custom code is found
          referral_code: customCodeData.referral_code,
          editingLimitReached:
            data.length === AUTH_CONSTANTS.REFERRAL_CODE_EDITING_LIMIT,
        }
      }
    )

    if (status === API_STATUS.OK) {
      const userDetails = {
        referralDetails: unifiedData,
      } as UserDetails

      event?.payload?.successCallback?.(userDetails)
      return userDetails
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
    event?.payload?.abortController?.abort()
  }

  return undefined
}

/**
 * Submits user feedback rating with optional comment from the user
 */
const submitUserFeedback = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.userFeedback?.rating) {
      if (typeof event?.payload?.userFeedback?.rating !== 'number') {
        throw new TypeError(
          `Rating needs to be a number got >> ${event?.payload?.userFeedback?.rating} <<`
        )
      }

      throw new TypeError(
        `Rating not provided got >> ${event?.payload?.userFeedback?.rating} <<`
      )
    }

    const {
      userFeedback: { rating, comment },
    } = event.payload

    let requestBody = {
      rating,
    } as typeof event.payload.userFeedback

    if (comment && comment.length > 0) {
      requestBody = {
        ...requestBody,
        comment,
      }
    }

    const { status } = await axios.post(
      API.submitUserFeedback,
      requestBody,
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)

      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Requests an income forecast, wether it is a plan based or a tontinator
 * forecast is decided by the `plan_id` property
 */
const sendParamsForForecast = async (
  context: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (
      event?.payload?.forecastParams?.length === 0 &&
      context?.forecastData?.forecastParams?.length === 0
    ) {
      throw new Error(
        `Did not get payload or context empty for income forecast, check payload key >>incomeForecastParams<<`
      )
    }

    const incomeForecastParams =
      event?.payload?.forecastParams ?? context?.forecastData?.forecastParams

    const incomeForecastRequestBody = incomeForecastParams?.map(
      (incomeForecastParam: IncomeForecastParams) => {
        const {
          oneTimeContribution,
          monthlyContribution,
          sex,
          retirementAge,
          contributionAge,
          countryOfResidence,
          strategy,
        } = incomeForecastParam

        const {
          contributionAge: adjustedContributionAge,
          retirementAge: adjustedRetirementAge,
        } = adjustAndConvertToAgeMonthString(retirementAge, contributionAge)

        return {
          ...parseIncomeForecastParams({
            isAuthenticated: context?.isAuthenticated,
            oneTimeContribution,
            monthlyContribution,
            countryOfResidence,
            sex,
            contributionAge: adjustedContributionAge,
            payoutAge: adjustedRetirementAge,
            writeDraftPlan: false,
            strategy,
          }),
        }
      }
    )

    logForecastParams(
      incomeForecastRequestBody as Array<IncomeForecastRequestBody>
    )

    const { status, data } = (await axios.post(
      API.tontinatorForecast,
      incomeForecastRequestBody,
      axiosConfig({
        authToken: getAuthToken(),
        signal: event?.payload?.abortController?.signal,
      })
    )) as unknown as {
      status: number
      data: Array<ForecastResult>
    }

    if (status === API_STATUS.OK) {
      data?.forEach((forecast) => {
        console.log(
          `\n\nGot forecast response with view id:\n\n${forecast?.view_id}\n\n`
        )
      })
      console.log(`Environment: %c${environment}`, `color:${envColor}`)

      const forecastData = {
        forecastParams: incomeForecastParams,
        forecastResults: data,
      }

      event?.payload?.successCallback?.(forecastData)
      return forecastData
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Uploads an address document for address verification
 */
const uploadAddressDocument = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.addressDocument) {
      throw new TypeError(`No address document found`)
    }
    const { addressDocument } = event.payload

    const formData = new FormData()

    formData.append(addressDocument?.name, addressDocument ?? '')

    const { status } = await axios.post(API.uploadAddressDocument, formData, {
      headers: {
        ...axiosConfig({
          withCredentials,
          signal: event?.payload?.abortController?.signal,
          contentType: 'multipart/form-data',
          authToken: getAuthToken(),
        }).headers,
      },
      onUploadProgress: event?.payload?.uploadProgressCallback,
    })

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event)
      return event
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Submits user's address information for review
 */
const submitAddressInfo = async (
  _: AuthMachineContext,
  event: AuthMachineEvent
) => {
  try {
    if (!event?.payload?.userAddress) {
      throw new Error(
        `Did not get payload or context empty for address, check payload key >>address<<`
      )
    }

    const { status, data } = (await axios.post(
      API.submitAddressInfo,
      event?.payload?.userAddress,
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )) as unknown as {
      status: number
      data: UserResidency
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(data)
      return data
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

export {
  addUnverifiedPhoneNumber,
  authTokenService,
  cancelAccountClosing,
  changeCurrentPin,
  closeUserAccount,
  createNewPin,
  createReferralCode,
  deletePayoutDetails,
  extendSession,
  fetchUserDetails,
  getReferralStats,
  liteAuth,
  logout,
  redeemMagicToken,
  refreshSession,
  registerUser,
  resendVerificationEmailForLite,
  resetPin,
  sendMagicLoginEmail,
  sendParamsForForecast,
  sendPinResetEmail,
  startFaceScan,
  submitAddressInfo,
  submitUserFeedback,
  updatePayoutDetails,
  updateUserAccountInfo,
  uploadAddressDocument,
  verifyPhoneNumber,
}
