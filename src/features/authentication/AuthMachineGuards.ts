import { AuthMachineEvent } from './types/AuthMachineTypes.type'
import { getAuthToken } from './utils/AuthFunctions'

/**
 * Prevents the machine from attempting to refresh the session if there is no
 * refresh token present in session storage
 */
const hasRefreshToken = () => {
  const hasAuthedOnce = Boolean(getAuthToken())
  return hasAuthedOnce
}

/**
 * Makes sure all the needed auth data is present in order to allow a state transition
 */
const isAuthenticated = ({ event }: { event: AuthMachineEvent }) => {
  const hasAllAuthData = Boolean(getAuthToken())

  if (hasAllAuthData) {
    return true
  }

  // Do NOT log the missing data because it is considered very sensitive data !!
  console.error(
    `ERROR: Missing flag for successful response in auth service handler ${event?.type}, 
     state transition will be prevented by guard. Did you forget about sessionStorage?`
  )

  return false
}

/**
 * Starts the verification flow if the user has a verification token in order
 * not to spam missing key error
 */
const shouldStartVerifying = ({ event }: { event: AuthMachineEvent }) => {
  const hasAuthedOnce = getAuthToken()

  return Boolean(event?.payload?.verifyToken) || Boolean(hasAuthedOnce)
}

export { hasRefreshToken, isAuthenticated, shouldStartVerifying }
