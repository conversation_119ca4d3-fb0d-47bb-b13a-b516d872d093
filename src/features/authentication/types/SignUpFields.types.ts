import {
  IncomeForecastRequestBody,
  SexType,
} from '../../../common/types/CommonTypes.types'
import { ErrorStorage } from '../../CommonState.type'

type SignUpFieldsType = {
  email?: string
  firstName?: string
  lastName?: string
  emailUpdates?: boolean
  checkPolicy?: boolean
  sex?: string
  country?: string
  referralCode?: string
  termsVersion?: number
  currentAge?: number
  forecastUserData?: IncomeForecastRequestBody
}

type SignUpFieldsProps = {
  forecastUserData?: IncomeForecastRequestBody
  registerButtonLabel?: string
  forecastPageRegisterModal?: boolean
  hideAdditionalFields?: boolean
  hideDivider?: boolean
  backButtonAction?: () => void
  signUpError?: ErrorStorage | null
  handleRegister: (signUpFields: SignUpFieldsType) => void
}

type SupportedCountryParams = {
  defaultSex: SexType
  minCurrentAge: { age: number }
  maxCurrentAge: { age: number }
  minRetirementAge: { age: number }
  maxRetirementAge: { age: number }
  defaultCurrentAgeSlider: number
  defaultRetirementAgeSlider: number
}

export type { SignUpFieldsProps, SignUpFieldsType, SupportedCountryParams }
