import { SexType, StateData } from '../../../common/types/CommonTypes.types'

type UserInfoParams = Partial<{
  firstName: string
  lastName: string
  dateOfBirth: string
  email: string
  sex: SexType
  phoneNumber: string
  currAge: number
  referralCode: string
}>

interface ResidencyParams {
  line_1?: string
  line_2?: string
  city?: string
  state_province?: string | StateData
  postal_code?: string
  country?: string
}

export type { ResidencyParams, UserInfoParams }
