import { ReactNode } from 'react'
import { IncomeForecastRequestBody } from '../../../common/types/CommonTypes.types'
import { StateType } from './AuthMachineTypes.type'

type RegisterFormProps = {
  children: ReactNode
  currentState: StateType
  resendEmail?: string
  sendEmailAgain: () => void
  modalTextContent?: string
  dismissModal: () => void
}
type RegistrationHookProps = {
  forecastUserData?: IncomeForecastRequestBody
}

export type { RegisterFormProps, RegistrationHookProps }
