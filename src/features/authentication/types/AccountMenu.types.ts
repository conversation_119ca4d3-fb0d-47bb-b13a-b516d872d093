import { TestID } from '../../../../cypress/support/ui-component-ids'
import { UserDetails } from './AuthMachineTypes.type'

interface MenuItem {
  to: string
  mainText: string
  icon: string
  alertCount?: number
  writeProtected?: boolean
  dataTestID?: TestID
  disabled?: boolean
}

interface DividerItem {
  isDivider: true
}

type MenuGroupItem = MenuItem | DividerItem

interface MenuGroup {
  title: string
  items: Array<MenuGroupItem>
}

type MenuItemsFunction = (user_details?: UserDetails) => Array<MenuGroup>

export type {
  DividerItem,
  MenuGroup,
  MenuGroupItem,
  MenuItem,
  MenuItemsFunction,
}
