import { Dispatch, Mu<PERSON>RefObject, ReactNode, SetStateAction } from 'react'
import { InputProps } from '../../../common/types/InputTypes.type'
import { ErrorStorage } from '../../CommonState.type'
import {
  AuthMachineEventPayload,
  EventType,
  StateType,
} from './AuthMachineTypes.type'

// Shared types
type PinType = Array<string>
type InputRefArray = MutableRefObject<Array<HTMLInputElement>>
type SetPinState = Dispatch<SetStateAction<PinType>>

type PinChangeProps = {
  label?: string
  pinLength: number
  pin: PinType
  pinChangeRefs?: InputRefArray
  error?: ErrorStorage
  onChange: (values: PinType) => void
}

type PinInputBaseProps = Partial<{
  label: string
  pinLength: number
  errorMessage: string
  inputRefs: InputRefArray
  children: ReactNode
}> &
  Omit<InputProps, 'onChange'>

type PinInputProps = PinInputBaseProps & {
  values: PinType
  onChange: (values: PinType, index: number) => void
}

type PinAuthorizationProps = {
  mainTitle?: string
  pageTitle: string
  navigateTo: string
  onClickAction: () => void
  pinInputLabel?: string
  errorMessage: string
  onSuccessfulPinSubmit: () => void
  onFailedPinSubmit?: () => void
  hideMobileHeader: boolean
  actionConfirmText: string
  loadingType: StateType
  modalTitle: string
  pin: PinType
  setPin: SetPinState
}

type PinConfirmationOptionalProps = Partial<{
  autoFocus: boolean
  headerTitle: string
  initialPinLabel: string
  confirmPinLabel: string
  pinLength: number
  externalInputRefs: InputRefArray
  focusOnSuccess: boolean
  errorCallback: () => void
}>

type PinConfirmationProps = PinConfirmationOptionalProps & {
  handleSubmit: (pin: string) => void
}

type UsePinConfirmationProps = {
  initialPin: PinType
  length?: number
  onSubmit: {
    handleSubmit: (pin: string) => void
    successCallback: () => void
  }
  onError: (error?: ErrorStorage) => void
}

type PinCloseAccountProps = {
  payload?: AuthMachineEventPayload
  length?: number
  authMachineEvent: EventType
  successCallback: () => void
  failureCallback?: () => void
}

export type {
  PinAuthorizationProps,
  PinChangeProps,
  PinCloseAccountProps,
  PinConfirmationProps,
  PinInputProps,
  PinType,
  UsePinConfirmationProps,
}
