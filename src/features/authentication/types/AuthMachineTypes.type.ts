import { AxiosProgressEvent } from 'axios'
import { BackendErrorId } from '../../../common/constants/ApiErrors.types'
import {
  IncomeForecastParams,
  InvestmentDetail,
  InvestmentStrategyId,
  SexType,
  TranslatedError,
} from '../../../common/types/CommonTypes.types'
import { AgeMonth, EventWithCommonPayload } from '../../CommonState.type'
import {
  ReferralDetails,
  ReferralStat,
} from '../../referral/types/ReferralTypes.type'
import { ForecastResult } from '../../visualization/types/Visualization.types'

type BankAccountType = 'Checking' | 'Savings'
type BankAccountNumber = string
type ACHRoutingNumber = string
type TransferType = 'ACH'

type ResidencyReviewStatus = 'DENIED' | 'SUBMITTED' | 'APPROVED' | 'UPLOADED'
type ResidencyDeniedReason =
  | 'NAME_MISMATCH'
  | 'DOCUMENT_TOO_OLD'
  | 'ILLEGIBLE_DOCUMENT'
  | 'ADDRESS_NOT_SHOWN'
  | 'OTHER_REASON'
type AccountPermissions = 'read' | 'write'
type ScanType = 'AUTHENTICATION' | 'PHOTO_ID_SCAN' | 'ENROLLMENT'

type AuthTokenInfo = {
  authToken: string
  permissions: AccountPermissions
  refreshToken: string
  remainingTime: number
}

/**
 * Types the events in the `AuthMachine` and services
 */
interface Events {
  type: EventType
  payload?: AuthMachineEventPayload
}

type UserResidency = {
  country: string
  state_province: string
  city: string
  postal_code: string
  line_1: string
  line_2: string
}

type UserResidencyWithDeniedStatus = {
  reason: string
  reason_type: ResidencyDeniedReason
  proof_id: string
  denied_at: string
}

type ResidencyWithStatus = {
  status: ResidencyReviewStatus
  user_submitted_address: UserResidency & UserResidencyWithDeniedStatus
}

type PayoutAccountDetails = {
  account: {
    tag: TransferType
    ach: string
    accountNumber: BankAccountNumber
    checkingSavings: BankAccountType
  }
}

interface VerifiedUserDetails {
  verified_phone_number: string
  verified_date_of_birth: string
  verified_first_name: string
  verified_sex: SexType
  verified_last_name: string
  verified_residency: string
  verified_age: AgeMonth
}

interface UnverifiedUserDetails {
  unverified_date_of_birth: string
  unverified_age: AgeMonth
  unverified_date_of_birth_set_by_user: boolean
  unverified_first_name: string
  unverified_sex: SexType
  unverified_last_name: string
  unverified_residency: string
  unverified_phone_number: string
}

type KycRequirementsL0 = {
  email_verified: boolean
}

type KycRequirementsL1 = {
  L0: boolean
  face_scanned: boolean
  id_verified: boolean
  phone_verified: boolean
}

type KycRequirementsL2 = {
  L1: boolean
  address_verified: boolean
}

type KycL0 = {
  passed_level: boolean
  requirements: KycRequirementsL0
}

type KycL1 = {
  passed_level: boolean
  requirements: KycRequirementsL1
}

type KycL2 = {
  passed_level: boolean
  requirements: KycRequirementsL2
}

type KycStatus = {
  L0: KycL0
  L1: KycL1
  L2: KycL2
}

interface UserDetails
  extends UnverifiedUserDetails,
    Partial<VerifiedUserDetails> {
  payout_account?: PayoutAccountDetails | null
  referralDetails?: ReferralStat
  closure_scheduled_time: string
  email: string
  email_verified: boolean
  face_enrolled: boolean
  id_rejection_reason:
    | 'possibly_altered'
    | 'image_not_clear'
    | 'user_confirmed_fields_mismatch'
    | null
  /**
   * `null` means the id has never been submitted not even once
   */
  id_review_status: 'approved' | 'not_reviewed' | 'rejected' | null
  investment_account_status: 'none' | 'pending' | 'rejected' | 'opened'
  is_admin: boolean
  is_influencer: boolean
  pin_set: boolean
  referral_code_redeemed: null
  referral_tier: number
  date_of_birth?: string
  first_name?: string
  sex?: SexType
  last_name?: string
  residency?: string
  age: AgeMonth
  phone_number?: string
  user_address?: ResidencyWithStatus
  kyc_status: KycStatus
  ref_id: string
}

interface AuthMachineEventPayload extends EventWithCommonPayload {
  userAddress?: UserResidency
  addressDocument?: File
  invAccOpen?: boolean
  permissions?: AccountPermissions
  date_of_birth?: string
  unverified_date_of_birth_set_by_user?: boolean
  pin?: string
  newPin?: string
  oldPin?: string
  authToken?: string
  email?: string
  reset_token?: string
  closureFeedback?: string
  phone_number?: string
  verification_code?: string
  magic_login_token?: string
  referralCode?: string
  refCodePrefix?: string
  /**
   * @deprecated
   */
  forecastUserData?: object
  scanType?: ScanType
  payoutDetails?: PayoutAccountDetails | null
  reason?: string
  notifyUiWith?: 'normalExpiredNotification' | 'sessionErrorNotification'
  renderNotification?: boolean
  secondsRemaining?: number
  hasDeclinedSessionExtension?: boolean
  residency?: string
  notifyUser?: boolean
  marketing?: boolean
  news?: boolean
  verifyToken?: string
  currencyParamForRate?: 'USD'
  draftPensionPlan?: LitePensionPlan
  userFeedback?: {
    rating: number
    comment?: string
  }
  websiteOrigin?: string | null
  forecastParams?: Array<IncomeForecastParams & { paramsId?: string }>
  uploadProgressCallback?: (progressEvent: AxiosProgressEvent) => void
  first_name?: string
  last_name?: string
  age?: AgeMonth
}

type LitePensionPlan = Pick<
  IncomeForecastParams,
  | 'contributionAge'
  | 'countryOfResidence'
  | 'retirementAge'
  | 'oneTimeContribution'
  | 'sex'
  | 'monthlyContribution'
  | 'strategy'
  | 'paramsMode'
>

type LiteData = {
  referralDetails: ReferralDetails
  // Generic type for now until API is done
  pensionPlan: LitePensionPlan
  error?: TranslatedError
  liteAuthToken: string
  websiteOrigin?: string
  isAuthenticated?: boolean
}

type ForecastData = {
  forecastParams: Array<IncomeForecastParams & { paramsId: string }>
  forecastResults: Array<ForecastResult>
  error?: ErrorResponseData
}

/**
 * Context objects that the `AuthMachine` uses to store global data that is used
 * throughout the app
 */
interface AuthMachineContext {
  error?: string
  user_details?: UserDetails
  permissions?: AccountPermissions
  sessionExpired?: {
    reason?: string
    notifyUiWith?: 'normalExpiredNotification' | 'sessionErrorNotification'
    renderNotification?: boolean
  }
  sessionAboutToExpire?: {
    secondsRemaining?: number
    notifyUser?: boolean
  }
  extendedSessionTimes: number
  remainingTime?: number
  liteData?: LiteData
  returns?: InvestmentDetails
  forecastData?: ForecastData
  isAuthenticated?: boolean
  addressDocument?: File
}

/**
 * Data that can be returned when a service has successfully completed.
 * This is typing for the `output` property of an event which is from invoking a service
 */
type EventOutput = {
  authTokenInfo: AuthTokenInfo
  userAccountInfo: UserDetails
  forecastResults?: Array<ForecastResult>
  forecastParams?: Array<IncomeForecastParams & { paramsId: string }>
  /**
   * Always needs to be provided by the auth functions that return an auth
   * token, or in our case set a cookie with the auth token
   */
  isAuthenticated?: boolean
  invAccOpen?: boolean
  userAddress?: ResidencyWithStatus
  addressDocument?: File
}

type States = {
  REDEEMING_MAGIC_TOKEN: 'REDEEMING_MAGIC_TOKEN'
  SENDING_NEW_TAB_EMAIL: 'SENDING_NEW_TAB_EMAIL'
  REGISTERING_USER: 'REGISTERING_USER'
  SENT_NEW_TAB_EMAIL: 'SENT_NEW_TAB_EMAIL'
  AUTHENTICATED: 'AUTHENTICATED'
  IDLE: 'IDLE'
  UPDATING_USER_ACCOUNT_INFO: 'UPDATING_USER_ACCOUNT_INFO'
  EXTENDING_SESSION: 'EXTENDING_SESSION'
  RESETTING_PIN: 'RESETTING_PIN'
  CREATING_NEW_PIN: 'CREATING_NEW_PIN'
  CHANGING_CURRENT_PIN: 'CHANGING_CURRENT_PIN'
  SENDING_PIN_RESET_EMAIL: 'SENDING_PIN_RESET_EMAIL'
  ADDING_UNVERIFIED_PHONE: 'ADDING_UNVERIFIED_PHONE'
  VERIFYING_PHONE_NUMBER: 'VERIFYING_PHONE_NUMBER'
  CLOSING_USER_ACCOUNT: 'CLOSING_USER_ACCOUNT'
  CANCELLING_CLOSE_ACCOUNT: 'CANCELLING_CLOSE_ACCOUNT'
  CREATING_REFERRAL_CODE: ' CREATING_REFERRAL_CODE'
  FETCHING_USER_ACCOUNT: 'FETCHING_USER_ACCOUNT'
  DELETING_AUTH_TOKEN: 'DELETING_AUTH_TOKEN'
  READY_RESEND_EMAIL: 'READY_RESEND_EMAIL'
  UPDATING_PAYOUT_DETAILS: 'UPDATING_PAYOUT_DETAILS'
  DELETING_PAYOUT_DETAILS: 'DELETING_PAYOUT_DETAILS'
  ANON_FACE_SCAN_IN_PROGRESS: 'ANON_FACE_SCAN_IN_PROGRESS'
  AUTH_FACE_SCAN_IN_PROGRESS: 'AUTH_FACE_SCAN_IN_PROGRESS'
  REFRESHING_SESSION: 'REFRESHING_SESSION'
  AUTH_TOKEN: 'AUTH_TOKEN'
  VERIFYING_EMAIL_MTL: 'VERIFYING_EMAIL_MTL'
  TERMINATING_MTL_SESSION: 'TERMINATING_MTL_SESSION'
  NO_AUTH_TOKEN: 'NO_AUTH_TOKEN'
  DEV_LOGGING_IN: 'DEV_LOGGING_IN'
  UPDATING_MTL_PENSION_PLAN: 'UPDATING_MTL_PENSION_PLAN'
  FETCHING_REFERRAL_STATS: 'FETCHING_REFERRAL_STATS'
  FORECASTING_INCOME: 'FORECASTING_INCOME'
  SUBMITTING_USER_FEEDBACK: 'SUBMITTING_USER_FEEDBACK'
}

type ErrorResponseData = {
  response?: {
    data?: { id?: BackendErrorId; message?: string; code?: BackendErrorId }
  }
}

/**
 * Used to adding a type to `event` in every action, service and guard `payload`
 * property is again present in the `data` property, because there are scenarios
 * where we return the passed in event payload and that payload is resolved via
 * a promise, because most of the backend APIs don't return data on successful
 * response
 */
interface AuthMachineEvent {
  type: EventType
  payload?: AuthMachineEventPayload
  output?: EventOutput & { payload?: AuthMachineEventPayload }
  error?: ErrorResponseData
}

type FaceScanError = {
  uas: string
  sessionResultStatusCode?: number
  sessionStatus?: number
}

type FaceScanData = {
  authTokenInfo: AuthTokenInfo
  error?: FaceScanError
  enrollmentCompleted: boolean
  idScanCompleted: boolean
  userAccountInfo: UserDetails
}

/**
 * Event types supported by the `AuthMachine`
 */
type EventType =
  | 'SEND_NEW_TAB_EMAIL'
  | 'REDEEM_AUTH_TOKEN'
  | 'DELETE_AUTH_TOKEN'
  | 'STORE_AUTH_TOKEN'
  | 'SESSION_ABOUT_TO_EXPIRE'
  | 'UPDATE_ACCOUNT_INFO'
  | 'CLOSE_SUCCESS_MODAL'
  | 'FACE_SCAN_COMPLETED'
  | 'START_FACE_SCAN'
  | 'EXTEND_SESSION'
  | 'CREATE_REFERRAL_CODE'
  | 'ADD_UNVERIFIED_PHONE'
  | 'CREATE_NEW_PIN'
  | 'CLOSE_USER_ACCOUNT'
  | 'CANCEL_CLOSING_ACCOUNT'
  | 'SEND_PIN_RESET_EMAIL'
  | 'RESET_PIN'
  | 'CHANGE_CURRENT_PIN'
  | 'UPDATE_PAYOUT_DETAILS'
  | 'DELETE_PAYOUT_DETAILS'
  | 'EXPIRED_SESSION'
  | 'VERIFY_PHONE_NUMBER'
  | 'REDEEM_MAGIC_TOKEN'
  | 'DEV_LOGIN'
  | 'REGISTER_USER'
  | 'FETCH_USER_ACCOUNT'
  | 'FACE_ENROLL_COMPLETED'
  | 'MODIFY_EXPIRED_SESSION_DATA'
  | 'REFRESH_SESSION'
  | 'GET_REFERRAL_STATS'
  | 'VERIFY_EMAIL_MTL'
  | 'TERMINATE_MTL_SESSION'
  | 'UPDATE_MTL_DRAFT_PLAN'
  | 'SUBMIT_USER_FEEDBACK'
  | 'REQUEST_INCOME_FORECAST'
  | 'UPLOAD_ADDRESS_DOCUMENT'
  | 'SUBMIT_ADDRESS_INFO'
  | 'REMOVE_UPLOADED_DOCUMENT'

type StateType = keyof States

type AuthMachineSelf = {
  send: (params: AuthMachineEvent) => void
}

type MagicLoginResponse = {
  userAccountInfo: UserDetails
  authTokenInfo: AuthTokenInfo
  forecastParams: IncomeForecastParams
}

type InvestmentDetails = {
  [key in InvestmentStrategyId]: InvestmentDetail
}

type ContextPropertyParams = {
  event: AuthMachineEvent
  context: AuthMachineContext
}

type SubmissionStatus = null | 'approved' | 'not_reviewed' | 'rejected'

export type {
  AccountPermissions,
  ACHRoutingNumber,
  AuthMachineContext,
  AuthMachineEvent,
  AuthMachineEventPayload,
  AuthMachineSelf,
  AuthTokenInfo,
  BankAccountNumber,
  BankAccountType,
  ContextPropertyParams,
  ErrorResponseData,
  Events,
  EventType,
  FaceScanData,
  ForecastData,
  InvestmentDetails,
  KycStatus,
  LiteData,
  LitePensionPlan,
  MagicLoginResponse,
  PayoutAccountDetails,
  ResidencyDeniedReason,
  ResidencyReviewStatus,
  States,
  StateType,
  SubmissionStatus,
  TransferType,
  UserDetails,
  UserResidency,
}
