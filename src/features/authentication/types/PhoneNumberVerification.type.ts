import { ValidationData } from '../../../common/types/CommonTypes.types'

type PhoneNumberAndOtpProps = {
  onPhoneNumberVerified: (value: string) => void
  onChange: (value: string) => void
  phoneNumberValidated?: ValidationData
  validatePhoneNumber: (value: string) => void
  onFailedPhoneVerification?: () => void
  unverifiedPhoneNum?: string
  smsSent?: boolean
}

export type { PhoneNumberAndOtpProps }
