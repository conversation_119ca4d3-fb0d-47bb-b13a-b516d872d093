import { TranslatedError } from '../../../common/types/CommonTypes.types'
import { ReferralDetails } from '../../referral/types/ReferralTypes.type'
import { LitePensionPlan } from './AuthMachineTypes.type'

type LiteAuthContext = {
  isAuth?: boolean
  referralDetails?: ReferralDetails
  litePensionPlan?: LitePensionPlan
  error?: TranslatedError
  isLoading: boolean
  verifyToken?: string
  currentState?: unknown
}

export type { LiteAuthContext }
