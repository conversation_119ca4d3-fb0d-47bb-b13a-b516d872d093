import {
  StateData,
  ValidationData,
} from '../../../common/types/CommonTypes.types'

interface AddressFieldsProps {
  readOnly?: boolean
  streetAddress?: string
  streetAddressTwo?: string
  city?: string
  state: StateData | string
  postalCode: string
  onStreetAddressChange: (value: string) => void
  onStreetAddressTwoChange: (value: string) => void
  onCityChange: (value: string) => void
  onStateChange: (value: StateData | string) => void
  onPostalCodeChange: (value: string) => void
  streetAddressLineOneValidated?: ValidationData
  streetAddressLineTwoValidated?: ValidationData
  cityValidated?: ValidationData
  usaStateValidated?: ValidationData
  postalCodeValidated?: ValidationData
  validateStreetAddressLineOne: (value: string) => void
  validateStreetAddressLineTwo: (value: string) => void
  validateCity: (value: string) => void
  country?: string
  onCountryChange?: (country: string) => void
}

export type { AddressFieldsProps }
