import { Dispatch, SetStateAction } from 'react'
import { SelectionItem } from '../../../common/types/MultiSelection.types'

type FeedbackOption = SelectionItem & {
  id: number
  title: string
}

type BeforeClosingAccountFeedback = {
  activeSelection: SelectionItem | undefined
  data: Array<FeedbackOption>
}

type CloseAccountButtonsProps = {
  confirmClosing: boolean
  reactivatedAccount: boolean
  setConfirmClosing: Dispatch<SetStateAction<boolean>>
  scheduledClosingTime: string | undefined
  setReactivatedAccount: Dispatch<SetStateAction<boolean>>
  onClickCloseAccount: () => void
}

type PinAuthorizationCallbacks = {
  onSuccessfulAccountScheduling: () => void
  onFailedAccountScheduling: (error: { translatedError: string }) => void
}

type PinChangeHandlerProps = {
  pin: Array<string>
}

type PinSubmitHandlerProps = {
  pin: string
  payload: {
    closureFeedback: string
  }
}

type UseSubmitPinConfig = {
  authMachineEvent: 'CLOSE_USER_ACCOUNT'
  successCallback: () => void
  failureCallback?: () => void
}

type UseSubmitPinReturn = {
  setPin: Dispatch<SetStateAction<Array<string>>>
  pin: Array<string>
  handleSubmitPin: (props: PinSubmitHandlerProps) => void
}

export type {
  BeforeClosingAccountFeedback,
  CloseAccountButtonsProps,
  FeedbackOption,
  PinAuthorizationCallbacks,
  PinChangeHandlerProps,
  PinSubmitHandlerProps,
  UseSubmitPinConfig,
  UseSubmitPinReturn,
}
