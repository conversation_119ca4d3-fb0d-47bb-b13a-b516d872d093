type RenderControlParams = {
  showDobInput: boolean
  saveDobLabel: string
  addDobLabel: string
  onClickCloseButton: () => void
  onClickSaveButton: () => void
}

type RenderControlReturn = {
  saveButtonLabel: string
  saveButtonOnClick: () => void
  closeModalButtonOnClick: () => void
}

type SaveChoiceAndChangeStateParams = {
  onClickCloseButton: () => void
  saveInLocalStorage: (dismissedModal: boolean) => void
}

export type {
  RenderControlParams,
  RenderControlReturn,
  SaveChoiceAndChangeStateParams,
}
