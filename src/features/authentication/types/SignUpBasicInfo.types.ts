import { ValidationData } from '../../../common/types/CommonTypes.types'

type SignUpBasicInfoProps = {
  firstName?: string
  setFirstName: (firstName: string) => void
  validateFirstName: (firstName: string) => void
  firstNameValidated?: ValidationData
  lastName?: string
  setLastName: (lastName: string) => void
  validateLastName: (lastName: string) => void
  lastNameValidated?: ValidationData
  userEmail?: string
  setUserEmail: (userEmail: string) => void
  validateEmail: (userEmail: string) => void
  emailValidated?: ValidationData
}

export type { SignUpBasicInfoProps }
