import { useEffect, useState } from 'react'
import { API_ERROR } from '../../../common/constants/ApiErrors'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ErrorStorage } from '../../CommonState.type'
import { ForecastData } from '../types/AuthMachineTypes.type'
import { RegistrationHookProps } from '../types/RegisterForm.types'
import { SignUpFieldsType } from '../types/SignUpFields.types'
import { parseRegisterBody } from '../utils/UtilsFunctions'
import { useAccountService } from './useAccountService'

export const useRegistration = ({
  forecastUserData,
}: RegistrationHookProps) => {
  const { send, currentState } = useAccountService()
  const t = useTranslate()

  const [signUpError, setSignUpError] = useState<
    ErrorStorage | null | undefined
  >(null)
  const [resendEmail, setResendEmail] = useState<string>()
  const [modalTextContent, setModalTextContent] = useState<string>('')

  /**
   * Register a new user to the UAS, also this function is used to
   * redeem a referral code if provided
   */
  const handleRegister = (signUpFields: SignUpFieldsType) => {
    const payload = parseRegisterBody(
      forecastUserData ?? signUpFields?.forecastUserData,
      signUpFields
    )

    setSignUpError(null)

    send({
      type: 'REGISTER_USER',
      payload: {
        ...payload,
        marketing: Boolean(payload?.email_updates),
        news: Boolean(payload?.email_updates),
        residency: String(payload?.residency),
        forecastParams: (forecastUserData ??
          signUpFields?.forecastUserData) as unknown as ForecastData['forecastParams'],
        successCallback: (data) => {
          const { id } = data as { id: string }

          if (id === 'UAS-USER-ACCOUNT-17') {
            setModalTextContent(t(API_ERROR['UAS-USER-ACCOUNT-17']))
          } else {
            setModalTextContent(t('MODAL.BODY.TEXT.VERIFICATION.SENT'))
          }
          setResendEmail(payload?.email)
        },
        failureCallback: (error) => {
          setSignUpError(error)
        },
        //Will be sent after the API request is successful
        forecastUserData,
      },
    })
  }

  const dismissModal = () => {
    //Puts the app back to the initial NO_AUTH token, so the completed
    //registration modal does not show again on the login page
    send({
      type: 'CLOSE_SUCCESS_MODAL',
    })
  }

  //Closes the modal so it does not persist on the logins screen
  useEffect(() => {
    return () => dismissModal()
  }, [])

  const sendEmailAgain = () => {
    send({
      type: 'SEND_NEW_TAB_EMAIL',
      payload: { email: resendEmail, forecastUserData },
    })
  }

  return {
    currentState,
    signUpError,
    resendEmail,
    modalTextContent,
    dismissModal,
    handleRegister,
    sendEmailAgain,
    send,
    setSignUpError,
  }
}
