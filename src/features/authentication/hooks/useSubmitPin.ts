import { useState } from 'react'
import { AuthMachineEventPayload } from '../types/AuthMachineTypes.type'
import { PinCloseAccountProps, PinType } from '../types/Pin.types'
import { AUTH_CONSTANTS } from '../utils/consts'
import { useAccountService } from './useAccountService'

/**
 * Custom hook to handle the submission of a PIN for various authentication-related events.
 *
 * Provides a stateful PIN value and a method for setting the PIN.
 * The `handleSubmitPin` function is used to validate the PIN length and send the event with
 * the PIN and additional payload if valid.
 */
export const useSubmitPin = ({
  length = AUTH_CONSTANTS.PIN_INPUT_FIELDS,
  authMachineEvent,
  successCallback,
  failureCallback,
}: PinCloseAccountProps) => {
  const [initialPin, setInitialPin] = useState<PinType>([])
  const { send } = useAccountService()

  const handleSubmitPin = ({
    pin,
    payload,
  }: {
    pin?: string
    payload?: AuthMachineEventPayload
  }) => {
    if (pin?.length === length) {
      send({
        type: authMachineEvent,
        payload: {
          ...payload,
          pin: pin,
          successCallback,
          failureCallback,
        },
      })
    }
  }
  return {
    setPin: setInitialPin,
    pin: initialPin,
    handleSubmitPin,
  }
}
