import { toast } from 'react-toastify'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { getDetectedIpCountry } from '../../../common/utils/UtilFunctions'
import { handleStateOption } from '../utils/UtilsFunctions'
import { useAddressForm } from './FormHooks'
import { useAccountService } from './useAccountService'

/**
 * Handles address verification fields by validating and handling their state,
 * and submits the field values to the address review API
 */
export const useAddressInfo = () => {
  const t = useTranslate()
  const { send, context } = useAccountService()
  const { country, state_province, city, postal_code, line_1, line_2 } =
    context?.user_details?.user_address?.user_submitted_address ?? {}

  const {
    addressLine1: userStreetAddress,
    setAddressLine1: setUserStreetAddress,
    addressLine2: userStreetAddressTwo,
    setAddressLine2: setUserStreetAddressTwo,
    userCity,
    setUserCity,
    stateProvince: userState,
    setStateProvinceWithCrossValidation: setUserState,
    postalCode: userPostalCode,
    setPostalCodeWithCrossValidation: setUserPostalCode,
    userCountry,
    setUserCountry: setCountry,
    userIsTyping: userHasTypedData,
    addressValidators,
  } = useAddressForm({
    line_1,
    line_2,
    city,
    state_province,
    postal_code,
    country: country ?? getDetectedIpCountry(),
  })

  /**
   * Submits the user entered address information to the auth machine
   */
  const submitInfo = () => {
    send({
      type: 'SUBMIT_ADDRESS_INFO',
      payload: {
        userAddress: {
          country: userCountry ?? '',
          state_province: handleStateOption(userState ?? ''),
          city: userCity ?? '',
          line_1: userStreetAddress ?? '',
          line_2: userStreetAddressTwo ?? '',
          postal_code: userPostalCode ?? '',
        },
        successCallback: () =>
          toast.success(t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')),
        failureCallback: (error) => toast.error(error?.translatedError),
      },
    })
  }

  return {
    addressValidators,
    userHasTypedData,
    userCountry,
    setCountry,
    userStreetAddress,
    setUserStreetAddress,
    userStreetAddressTwo,
    setUserStreetAddressTwo,
    userCity,
    setUserCity,
    userState,
    setUserState,
    userPostalCode,
    setUserPostalCode,
    submitInfo,
  }
}
