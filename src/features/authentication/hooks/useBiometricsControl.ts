import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { PERSONAL_DETAILS } from '../../../routes/Route'
import { UseBiometricsControlParams } from '../types/UseBiometricsControl.types'
import { useAccountService } from './useAccountService'

/**
 * Controls the navigation to biometrics depending on the KYC status
 */
export const useBiometricsControl = ({
  userIsTyping,
  userIsTypingCallback,
  setOpenFaceScanModal,
}: UseBiometricsControlParams) => {
  const navigate = useCustomNavigation()

  const {
    context: { user_details },
  } = useAccountService()

  /**
   * Opens the facescan modal and starts a facescan
   */
  const startIdVerification = () => {
    if (user_details?.id_review_status === 'rejected') {
      navigate(PERSONAL_DETAILS.REJECTED_ID)
    } else {
      setOpenFaceScanModal(
        user_details?.id_review_status === 'not_reviewed' ||
          user_details?.id_review_status === null
      )
    }
  }

  const safeToStartIDV = () => {
    if (!userIsTyping) {
      startIdVerification()
    } else {
      userIsTypingCallback?.()
    }
  }

  /**
   * Prevents the user from editing their personal details if id review status
   * is `submitted` or `approved`
   */
  const isReadOnly = () => {
    const idReviewStatus = user_details?.id_review_status
    return idReviewStatus === 'approved' || idReviewStatus === 'not_reviewed'
  }

  return { safeToStartIDV, isReadOnly }
}
