import { useState } from 'react'
import { inputRestrictionRegex, regex } from '../../../common/constants/Regex'
import {
  SexType,
  StateCodesAlpha2,
  StateData,
} from '../../../common/types/CommonTypes.types'
import { formatPostalCode } from '../../../common/utils/Formatters'
import {
  getStateInformation,
  sanitizeInputValue,
} from '../../../common/utils/UtilFunctions'
import { useValidateAddress } from '../../agreements/hooks/useValidateAddress'
import { ResidencyParams, UserInfoParams } from '../types/FormHooks.types'
import {
  AUTH_CONSTANTS,
  COUNTRY_ZIP_FORMAT,
  countriesWithStates,
} from '../utils/consts'
import {
  excludeDomainFromUrl,
  handleStateOption,
} from '../utils/UtilsFunctions'
import { useUserInfoValidation } from './useUserInfoValidation'

const useUserInfo = (params?: UserInfoParams) => {
  const {
    firstName: first_name,
    lastName: last_name,
    dateOfBirth: date_of_birth,
    email,
    sex,
    referralCode: referral_code,
    currAge: current_age,
    phoneNumber: phone_number,
  } = params ?? {}

  const inputValidation = useUserInfoValidation()

  const [userIsTyping, setUserIsTyping] = useState(false)
  const userTypedData = () => setUserIsTyping(true)

  const [firstName, _setFirstName] = useState<string | undefined>(first_name)
  const [lastName, _setLastName] = useState<string | undefined>(last_name)
  const [userDob, _setUserDob] = useState<string | Date | undefined | number>(
    date_of_birth
  )
  const [userEmail, _setUserEmail] = useState<string | undefined>(email)
  const [userSex, _setSex] = useState<SexType | undefined>(sex)
  const [referralCode, _setReferralCode] = useState<string | undefined>(
    excludeDomainFromUrl(referral_code ?? '')
  )
  const [currentAge, _setCurrentAge] = useState(current_age)

  const [phoneNumber, _setPhoneNumber] = useState<string | undefined>(
    phone_number
  )

  const setPhoneNumber = (phone: string) => {
    _setPhoneNumber(sanitizeInputValue({ inputValue: phone }))
    userTypedData()
  }

  const setFirstName = (firstName: string) => {
    _setFirstName(
      sanitizeInputValue({ inputValue: firstName, onlySpaces: true })
    )
    userTypedData()
  }

  const setLastName = (lastName: string) => {
    _setLastName(sanitizeInputValue({ inputValue: lastName, onlySpaces: true }))
    userTypedData()
  }

  const setUserDob = (userDob: Date | string | number) => {
    _setUserDob(userDob)
    userTypedData()
  }

  const setUserEmail = (userEmail: string) => {
    _setUserEmail(sanitizeInputValue({ inputValue: userEmail }))
    userTypedData()
  }

  const setSex = (sex: SexType) => {
    _setSex(sex)
    userTypedData()
  }

  const setReferralCode = (referralCode: string) => {
    const cleanReferralCode = excludeDomainFromUrl(referralCode)
    // Clean up string
    const cleanValue = cleanReferralCode
      ?.replaceAll(regex.duplicateSpaces, ' ')
      ?.replaceAll(inputRestrictionRegex.referralCodeFormat, '')
      ?.replaceAll(AUTH_CONSTANTS.REFERRAL_CODE_PREFIX, '')
      ?.trim()

    _setReferralCode(sanitizeInputValue({ inputValue: cleanValue }))
    userTypedData()
  }

  const setCurrentAge = (currentAge: number) => {
    _setCurrentAge(currentAge)
    userTypedData()
  }

  return {
    firstName,
    setFirstName,
    lastName,
    setLastName,
    userDob,
    setUserDob,
    userEmail,
    setUserEmail,
    userIsTyping,
    setUserIsTyping,
    userSex,
    setSex,
    inputValidation,
    referralCode,
    setReferralCode,
    setCurrentAge,
    currentAge,
    phoneNumber,
    setPhoneNumber,
  }
}

/**
 * Temporary workaround for postal data formatting.
 * Will be removed when backend provides states/provinces per country.
 * @todo Remove when backend API is updated with state/province data
 */
export const countryPostalData: Record<
  string,
  {
    zipFormat?: [number, number]
    regex?: RegExp
    postalCodeFormatter?: typeof formatPostalCode
  }
> = {
  USA: {
    zipFormat: COUNTRY_ZIP_FORMAT.USA,
    regex: regex.usaPostalCode,
    postalCodeFormatter: formatPostalCode,
  },
  BRA: {
    zipFormat: COUNTRY_ZIP_FORMAT.BRA,
    regex: regex.brazilPostalCode,
    postalCodeFormatter: formatPostalCode,
  },
  ROW: {
    postalCodeFormatter: (postalCode: string) => postalCode,
  },
}

const useAddressForm = (params?: ResidencyParams) => {
  const { line_1, line_2, city, state_province, postal_code, country } =
    params ?? {}

  const addressValidators = useValidateAddress()
  const { validateCountryState, validatePostalCode } = addressValidators

  const [userIsTyping, setUserIsTyping] = useState(false)
  const userTypedData = () => setUserIsTyping(true)

  const [postalCode, _setPostalCode] = useState<string | undefined>(postal_code)
  const [userCountry, _setUserCountry] = useState<string | undefined>(country)
  const [addressLine1, _setAddressLine1] = useState<string | undefined>(line_1)
  const [addressLine2, _setAddressLine2] = useState<string | undefined>(line_2)
  const [userCity, _setUserCity] = useState<string | undefined>(city)
  const [stateProvince, _setStateProvince] = useState<
    string | undefined | StateData
  >(
    getStateInformation(
      handleStateOption(state_province ?? '') as StateCodesAlpha2,
      countriesWithStates?.[userCountry ?? '']
    )
  )

  const setAddressLine1 = (address: string) => {
    _setAddressLine1(
      sanitizeInputValue({ inputValue: address, onlySpaces: true })
    )
    userTypedData()
  }

  const setAddressLine2 = (address: string) => {
    _setAddressLine2(
      sanitizeInputValue({ inputValue: address, onlySpaces: true })
    )
    userTypedData()
  }

  const setUserCity = (city: string) => {
    _setUserCity(sanitizeInputValue({ inputValue: city, onlySpaces: true }))
    userTypedData()
  }

  const setStateProvince = (state?: StateData | string) => {
    _setStateProvince(state)
    userTypedData()
  }

  const setPostalCode = (postalCode: string) => {
    _setPostalCode(sanitizeInputValue({ inputValue: postalCode }))

    userTypedData()
  }

  const setUserCountry = (country: string) => {
    if (postalCode) {
      _setPostalCode(undefined)
      _setStateProvince(undefined)
      validatePostalCode(undefined)
    }

    _setUserCountry(country)
    userTypedData()
  }

  const setPostalCodeWithCrossValidation = (postalCode?: string) => {
    const { postalCodeFormatter, zipFormat, regex } =
      countryPostalData[userCountry ?? ''] ?? countryPostalData.ROW

    const formattedPostalCode =
      postalCodeFormatter?.(postalCode ?? '', zipFormat ?? [0, 0]) ?? ''

    setPostalCode(formattedPostalCode)

    const numberOnlyPostalCode = formattedPostalCode?.replaceAll('-', '')

    validateCountryState?.(
      stateProvince as StateData,
      numberOnlyPostalCode,
      true
    )

    validatePostalCode?.(
      numberOnlyPostalCode,
      stateProvince as StateData,
      false,
      regex,
      postalCodeFormatter,
      zipFormat
    )
  }

  const setStateProvinceWithCrossValidation = (state?: StateData | string) => {
    _setStateProvince(state)

    if (postalCode) {
      const { postalCodeFormatter, zipFormat, regex } =
        countryPostalData[userCountry ?? ''] ?? countryPostalData.ROW

      const numberOnlyPostalCode = postalCode?.replaceAll('-', '')

      validateCountryState?.(state as StateData, numberOnlyPostalCode)

      validatePostalCode?.(
        numberOnlyPostalCode,
        state as StateData,
        true,
        regex,
        postalCodeFormatter,
        zipFormat
      )
    }
  }

  return {
    addressLine1,
    setAddressLine1,
    addressLine2,
    setAddressLine2,
    userCity,
    setUserCity,
    stateProvince,
    setStateProvince,
    postalCode,
    setPostalCode,
    userCountry,
    setUserCountry,
    userIsTyping,
    setPostalCodeWithCrossValidation,
    setStateProvinceWithCrossValidation,
    addressValidators,
  }
}

type UseUserInfo = ReturnType<typeof useUserInfo>
type UseAddressForm = ReturnType<typeof useAddressForm>

export type { UseAddressForm, UseUserInfo }

export { useAddressForm, useUserInfo }
