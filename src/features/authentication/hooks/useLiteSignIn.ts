import { useEffect, useState } from 'react'
import { useUserInfo } from './FormHooks'
import { useAccountService } from './useAccountService'
import { useUserInfoValidation } from './useUserInfoValidation'

/**
 * Controls the sign in process for the MyTontine Lite
 */
export const useLiteSignIn = () => {
  const [isOpenConfirm, setIsOpenConfirm] = useState(false)
  const { send, currentState } = useAccountService()
  const { emailValidated, validateEmail } =
    useUserInfoValidation() as unknown as {
      emailValidated: { valid: boolean; message?: string }
      validateEmail: (email?: string) => boolean
    }
  const { userEmail, setUserEmail } = useUserInfo()

  const dismissConfirmModal = () => {
    setIsOpenConfirm(false)
    send({ type: 'CLOSE_SUCCESS_MODAL' })
    // Prevents user spamming login emails
    setUserEmail('')
    validateEmail(undefined)
  }

  const onSignInClick = () => {
    send({
      type: 'SEND_NEW_TAB_EMAIL',
      payload: {
        email: userEmail,
        successCallback: () => setIsOpenConfirm(true),
      },
    })
  }

  useEffect(() => {
    return () => dismissConfirmModal()
  }, [])

  return {
    onSignInClick,
    emailValidated,
    validateEmail,
    setUserEmail,
    currentState,
    userEmail,
    dismissConfirmModal,
    isOpenConfirm,
  }
}
