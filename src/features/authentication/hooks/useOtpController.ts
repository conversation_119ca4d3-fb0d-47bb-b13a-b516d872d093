import { useState } from 'react'
import { SmsVerificationProps } from '../../../common/types/CommonTypes.types'
import { ErrorStorage } from '../../CommonState.type'

/**
 * Controls the state and handlers for the SMS verification
 */
export const useOtpController = ({
  onPhoneNumberVerified,
  onFailedPhoneVerification,
  unverifiedPhoneNumber,
}: SmsVerificationProps) => {
  const [errorMessage, setErrorMessage] = useState<string | undefined>(
    undefined
  )

  const onSuccessfulVerification = () => {
    onPhoneNumberVerified?.(unverifiedPhoneNumber ?? '')
  }

  const onFailedVerification = (error?: ErrorStorage) => {
    onFailedPhoneVerification?.(error)
    setErrorMessage(error?.translatedError)
  }

  return {
    onSuccessfulVerification,
    onFailedVerification,
    errorMessage,
    setErrorMessage,
  }
}
