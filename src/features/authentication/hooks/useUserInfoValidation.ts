import { isValidPhoneNumber } from 'libphonenumber-js'
import { useState } from 'react'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { regex } from '../../../common/constants/Regex'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ValidationData } from '../../../common/types/CommonTypes.types'
import {
  getCurrentAge,
  validateInputWithError,
} from '../../../common/utils/UtilFunctions'
import { UserInfoValidationProps } from '../types/UserInfoValidation.types'
import { AUTH_CONSTANTS } from '../utils/consts'

export const useUserInfoValidation = ({
  firstName,
  lastName,
  email,
  dateOfBirth,
  genericText,
  referralCode,
  textFeedback,
}: UserInfoValidationProps = {}) => {
  const t = useTranslate()

  // Validation states
  const [firstNameValidated, setFirstNameValidated] = useState<
    ValidationData | undefined
  >(firstName)
  const [lastNameValidated, setLastNameValidated] = useState<
    ValidationData | undefined
  >(lastName)
  const [emailValidated, setEmailValidated] = useState<
    ValidationData | undefined
  >(email)
  const [dateOfBirthValidated, setDateOfBirthValidated] = useState<
    ValidationData | undefined
  >(dateOfBirth)
  const [genericTextValidated, setGenericTextValidated] = useState<
    ValidationData | undefined
  >(genericText)
  const [referralCodeValidated, setReferralCodeValidated] = useState<
    ValidationData | undefined
  >(referralCode)
  const [textFeedbackValidated, setTextFeedbackValidated] = useState<
    ValidationData | undefined
  >(textFeedback)
  const [phoneNumberValidated, setPhoneNumberValidated] = useState<
    ValidationData | undefined
  >()

  /**
   * Validates email if it is in RFC 5322 format
   */
  const validateEmail = (email?: string): void => {
    validateInputWithError({
      input: email?.toLowerCase()?.trim(),
      emptyInputErrorI18nKey: 'ERROR_EMPTY_EMAIL',
      validateFormat: (email: string) => !regex.emailFormat.test(email),
      invalidInputErrorI18nKey: 'ERROR_EMAIL_WRONG_FORMAT',
      setStateAction: setEmailValidated,
    })
  }

  /**
   * Validates a first name input field, by checking if the first
   * name variable has a value and if the length of string matches the server's
   * max and min lengths
   */
  const validateFirstName = (firstName?: string): void =>
    validateInputWithError({
      input: firstName,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_FIRST_NAME',
      validateFormat: (firstName: string) => !regex.names.test(firstName),
      invalidInputErrorI18nKey: 'ERROR_INVALID_CHARACTER',
      extendedValidator: (firstName: string) =>
        firstName?.length > INPUT_LIMIT.GENERIC_MAX,
      extendedValidationErrorI18nKey: 'ERROR_LONG_FIRSTNAME',
      setStateAction: setFirstNameValidated,
    })

  /**
   * Validates a last name input field, by checking if the last
   * name variable has a value and if the length of string matches the server's
   * max and min lengths
   */
  const validateLastName = (lastName?: string): void => {
    validateInputWithError({
      input: lastName,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_LASTNAME',
      validateFormat: (lastName: string) => !regex.names.test(lastName),
      invalidInputErrorI18nKey: 'ERROR_INVALID_CHARACTER',
      extendedValidator: (lastName: string) =>
        lastName?.length > INPUT_LIMIT.GENERIC_MAX,
      extendedValidationErrorI18nKey: 'ERROR_LONG_LASTNAME',
      setStateAction: setLastNameValidated,
    })
  }

  /**
   * Validated if text area is empty or not
   */
  const validateFeedback = (text?: string): void => {
    if (text?.length ?? 0 > AUTH_CONSTANTS.MAX_FEEDBACK_LENGTH) {
      setTextFeedbackValidated({
        message: t('ERROR_FEEDBACK_TOO_LONG'),
        valid: false,
      })
    } else {
      setTextFeedbackValidated({ message: undefined, valid: true })
    }
  }

  /**
   * Validates a referral code input field, format and length
   */
  const validateReferralCode = (
    referralCode?: string,
    optionalField?: boolean
  ): void => {
    validateInputWithError({
      optionalField: Boolean(optionalField),
      input: referralCode,
      emptyInputErrorI18nKey: 'ERROR_REFERRAL_CODE_EMPTY',
      validateFormat: (referralCode: string) =>
        !regex.referralCodeFormat.test(referralCode),
      invalidInputErrorI18nKey: 'ERROR_REFERRAL_INVALID_FORMAT',
      extendedValidator: (referralCode: string) =>
        referralCode?.length < INPUT_LIMIT.REF_CODE_MIN,
      extendedValidationErrorI18nKey: 'ERROR_REFERRAL_CODE_SHORT',
      setStateAction: setReferralCodeValidated,
    })
  }

  /**
   * Check if a string is empty or contains a value
   */
  const validateGenericText = (text?: string): void => {
    if (text === null || text === undefined) {
      setGenericTextValidated({
        message: undefined,
        valid: false,
      })
    } else {
      setGenericTextValidated({ message: undefined, valid: true })
    }
  }

  /**
   * Validates if the input date is between the current year and
   * 1900 also uses `getCurrentAge()` to validate if the user is over 18 years
   * of age
   */
  const validateDateOfBirth = (date?: string): void => {
    if (date) {
      if (getCurrentAge(date) < AUTH_CONSTANTS.CURRENT_AGE_MIN) {
        setDateOfBirthValidated({
          message: t('ERROR_USER_NOT_18'),
          valid: false,
        })
      } else setDateOfBirthValidated({ message: undefined, valid: true })
    }
  }

  /**
   * The phone number must contain the dial code!
   * Validate a phone number input by checking if it is empty and
   * if the entered phone number matches the user's country formatting.
   */
  const validatePhoneNumber = (phoneNumber?: string): void => {
    validateInputWithError({
      input: phoneNumber,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_PHONE',
      validateFormat: (phoneNumber: string) => !isValidPhoneNumber(phoneNumber),
      invalidInputErrorI18nKey: 'ERROR_PHONE_NUMBER_FORMAT',
      setStateAction: setPhoneNumberValidated,
    })
  }

  return {
    emailValidated,
    firstNameValidated,
    lastNameValidated,
    dateOfBirthValidated,
    genericTextValidated,
    textFeedbackValidated,
    referralCodeValidated,
    phoneNumberValidated,
    validateEmail,
    validateFirstName,
    validateLastName,
    validateFeedback,
    validateReferralCode,
    validateGenericText,
    validateDateOfBirth,
    validatePhoneNumber,
  }
}

export type UseUserInfoValidation = ReturnType<typeof useUserInfoValidation>
