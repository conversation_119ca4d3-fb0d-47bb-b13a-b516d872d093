import { useState } from 'react'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ErrorStorage } from '../../CommonState.type'
import { useUserInfo } from './FormHooks'
import { useAccountService } from './useAccountService'

/**
 * Custom hook for handling authentication logic.
 */
export const useLoginForm = () => {
  // Hooks
  const {
    send,
    context: { sessionExpired },
    currentState,
  } = useAccountService()
  const t = useTranslate()

  const {
    userEmail,
    setUserEmail,
    inputValidation: { emailValidated, validateEmail },
  } = useUserInfo()

  // Render states
  const [error, setError] = useState<ErrorStorage | null | undefined>(null)
  const [emailSent, setEmailSent] = useState(false)
  const [faceTecLogin, setFaceTecLogin] = useState(false)

  /**
   * Issues a state transition from `READ_RESEND_EMAIL` to `NO_AUTH_TOKEN` state
   * which dismisses the email sent modal
   */
  const resetAuthMachineToNoAuthState = () => {
    // Necessary to close the success modal
    send({
      type: 'CLOSE_SUCCESS_MODAL',
    })
    setEmailSent(false)
  }
  /**
   * Sends an event to the auth machine, to start the login flow
   * depending if the user is from a phone or from a desktop pc
   */
  const handleMagicLogin = () => {
    send({
      type: 'SEND_NEW_TAB_EMAIL',
      payload: {
        email: userEmail,
        successCallback: () => setEmailSent(true),
        failureCallback: (error) => setError(error),
      },
    })
    // Wipes previous error if there is one
    setError(null)
  }

  /**
   * Wrapper function that sets the facetec login state to `true`
   */
  const showFaceTecAuthentication = () => setFaceTecLogin(true)

  /**
   * Dismisses the session expired modal and clears all the error sessions,
   * assuming the user has gotten the message
   */
  const dismissExpiredSessionModal = () => {
    // Modify session data so the notification is not shown
    send({
      type: 'MODIFY_EXPIRED_SESSION_DATA',
      payload: {
        renderNotification: false,
      },
    })
  }

  return {
    context: {
      sessionExpired,
    },
    currentState,
    t,
    userEmail,
    emailValidated,
    validateEmail,
    handleTyping: setUserEmail,
    error,
    setError,
    faceTecLogin,
    setFaceTecLogin,
    isLoginButtonDisabled: !emailValidated?.valid,
    showFaceTecAuthentication,
    handleMagicLogin,
    emailSent,
    sessionExpired,
    dismissExpiredSessionModal,
    resetAuthMachineToNoAuthState,
  }
}
