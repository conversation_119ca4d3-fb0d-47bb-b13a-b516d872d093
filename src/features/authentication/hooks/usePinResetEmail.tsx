import { toast } from 'react-toastify'
import ToastMessage from '../../../common/components/ToastMessage'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from './useAccountService'

/**
 * <PERSON>les sending pin reset email to the user in case they forgot their pin
 */
export const usePinResetEmail = () => {
  const t = useTranslate()

  const { send, currentState } = useAccountService()
  /**
   * Sends a pin reset link via the user's verified email address
   */
  const sendPinResetLinkViaEmail = () => {
    send({
      type: 'SEND_PIN_RESET_EMAIL',
      payload: {
        successCallback: () => {
          toast.success(
            <ToastMessage title={t('PIN_RESET_SENT_EMAIL.CONTENT')} />
          )
        },
        failureCallback: () => {
          toast.error(
            <ToastMessage title={t('PIN_RESET_EMAIL.FAILED_SENDING_CONTENT')} />
          )
        },
      },
    })
  }

  return {
    sendPinResetLinkViaEmail,
    isLoading: currentState === 'SENDING_PIN_RESET_EMAIL',
  }
}
