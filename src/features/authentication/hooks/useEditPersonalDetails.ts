import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useBankingService } from '../../banking/hooks/useBankingService'
import { ErrorStorage } from '../../CommonState.type'
import { getAuthToken } from '../utils/AuthFunctions'
import { useUserInfo } from './FormHooks'
import { useAccountService } from './useAccountService'

export const useEditPersonalDetails = () => {
  const t = useTranslate()
  const { sendBankEvent } = useBankingService()
  const {
    context: { user_details },
    send,
    currentState,
  } = useAccountService()

  const {
    firstName,
    setFirstName,
    lastName,
    setLastName,
    userDob,
    setUserDob,
    userIsTyping,
    setUserIsTyping,
    inputValidation,
  } = useUserInfo({
    firstName: user_details?.first_name,
    lastName: user_details?.last_name,
    dateOfBirth: user_details?.date_of_birth,
  })

  //Render states
  const [editApiError, setSetEditApiError] = useState<string>()

  const handleOnSuccessfulEdit = () => {
    //Removes any leftover API errors
    setSetEditApiError(undefined)

    toast.success(t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT'))

    sendBankEvent({
      type: 'GET_RETURNS',
      payload: {
        authToken: getAuthToken(),
      },
    })
  }

  /**
   * Enters edit mode, and if the component is in edit mode calling this
   * function again makes an API request to the edit user details endpoint
   */
  const saveEditedDetails = (onSuccess: () => void) => {
    send({
      type: 'UPDATE_ACCOUNT_INFO',
      payload: {
        date_of_birth: typeof userDob === 'string' ? userDob : undefined,
        first_name: firstName,
        last_name: lastName,
        unverified_date_of_birth_set_by_user: Boolean(userDob),
        successCallback: () => {
          handleOnSuccessfulEdit()
          onSuccess?.()
        },
        failureCallback: (error?: ErrorStorage) =>
          setSetEditApiError(error?.translatedError),
      },
    })
  }

  useEffect(() => {
    if (userIsTyping) {
      if (
        firstName === user_details?.first_name &&
        lastName === user_details?.last_name &&
        userDob === user_details?.date_of_birth
      ) {
        setUserIsTyping(false)
      }
    }
  }, [
    userIsTyping,
    firstName,
    lastName,
    userDob,
    user_details?.first_name,
    user_details?.last_name,
    user_details?.date_of_birth,
  ])

  return {
    firstName,
    setFirstName,
    lastName,
    setLastName,
    userDob,
    setUserDob,
    editApiError,
    setSetEditApiError,
    saveEditedDetails,
    inputValidation,
    loading: currentState === 'UPDATING_USER_ACCOUNT_INFO',
    userIsTyping,
  }
}
