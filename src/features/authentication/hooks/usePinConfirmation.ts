import { useTranslate } from '../../../common/hooks/useTranslate'
import { UsePinConfirmationProps } from '../types/Pin.types'
import { AUTH_CONSTANTS } from '../utils/consts'
import { validatePin } from '../utils/UtilsFunctions'

/**
 * This hook is used to manage the pin confirmation workflow.
 */
export const usePinConfirmation = ({
  initialPin,
  length = AUTH_CONSTANTS.PIN_INPUT_FIELDS,
  onSubmit,
  onError,
}: UsePinConfirmationProps) => {
  const t = useTranslate()

  const validatePins = (confirmPin: Array<string>) => {
    const isInitialComplete = validatePin(initialPin, length)
    const isConfirmComplete = validatePin(confirmPin, length)

    if (!isInitialComplete || !isConfirmComplete) return false

    if (initialPin.join('') !== confirmPin.join('')) {
      onError?.({
        translatedError: t('ERROR_PINS_NOT_MATCHING'),
      })
      return false
    }

    return true
  }

  const onPinsMatching = (confirmPin: Array<string>) => {
    const pinsValid = validatePins(confirmPin)

    if (pinsValid) {
      const pin = initialPin.join('')

      onSubmit?.handleSubmit(pin)
      onSubmit?.successCallback()
    }
  }

  return {
    onPinsMatching,
  }
}
