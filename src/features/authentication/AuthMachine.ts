import { assign, setup } from 'xstate'
import { promiseActorInput, promiseFuncToPromiseActor } from '../StateUtils'
import {
  clearAuthContext,
  clearLiteData,
  removeAddressDocument,
  storeAuthData,
  storeAuthTokenFromEvent,
  storeExpiredSessionData,
  storeForecastData,
  storePayoutDetails,
  storeSessionAboutToExpire,
  updateAddressDocument,
  updateLiteDetails,
  updateUserAddress,
  updateUserDetailsFromEvent,
  wipePayoutDetails,
} from './AuthMachineActions'
import {
  hasRefreshToken,
  isAuthenticated,
  shouldStartVerifying,
} from './AuthMachineGuards'
import {
  addUnverifiedPhoneNumber,
  authTokenService,
  cancelAccountClosing,
  changeCurrentPin,
  closeUserAccount,
  createNewPin,
  createReferralCode,
  deletePayoutDetails,
  extendSession,
  fetchUserDetails,
  getReferralStats,
  liteAuth,
  logout,
  redeemMagicToken,
  refreshSession,
  registerUser,
  resendVerificationEmailForLite,
  resetPin,
  sendMagicLoginEmail,
  sendParamsForForecast,
  sendPinResetEmail,
  startFaceScan,
  submitAddressInfo,
  submitUserFeedback,
  updatePayoutDetails,
  updateUserAccountInfo,
  uploadAddressDocument,
  verifyPhoneNumber,
} from './AuthMachineServices'
import {
  AuthMachineContext,
  AuthMachineEvent,
  UserDetails,
} from './types/AuthMachineTypes.type'
import { AUTH_CONSTANTS } from './utils/consts'

/*
General rules and good practices 
- Actions are FIRE and FORGET and should be used like that
- Assign is ASYNC, keep this in mind
- Invoking a service with timeout,interval and API call ALWAYS needs to have a
  clean up function
- All invoked services need to contain a unique ID
*/

//Initial context values
const initialContextValues: AuthMachineContext = {
  sessionExpired: undefined,
  error: undefined,
  remainingTime: undefined,
  // Needs to be an empty object to prevent destruction errors
  user_details: {} as UserDetails,
  permissions: undefined,
  extendedSessionTimes: 0,
  liteData: undefined,
  returns: undefined,
  forecastData: undefined,
  isAuthenticated: false,
  addressDocument: undefined,
}

export const authMachine = setup({
  types: {
    context: {} as AuthMachineContext,
    events: {} as AuthMachineEvent,
  },
  actors: {
    sendMagicLoginEmail: promiseFuncToPromiseActor(sendMagicLoginEmail),
    addUnverifiedPhoneNumber: promiseFuncToPromiseActor(
      addUnverifiedPhoneNumber
    ),
    authTokenService: promiseFuncToPromiseActor(authTokenService),
    cancelAccountClosing: promiseFuncToPromiseActor(cancelAccountClosing),
    changeCurrentPin: promiseFuncToPromiseActor(changeCurrentPin),
    closeUserAccount: promiseFuncToPromiseActor(closeUserAccount),
    createNewPin: promiseFuncToPromiseActor(createNewPin),
    createReferralCode: promiseFuncToPromiseActor(createReferralCode),
    deletePayoutDetails: promiseFuncToPromiseActor(deletePayoutDetails),
    extendSession: promiseFuncToPromiseActor(extendSession),
    fetchUserDetails: promiseFuncToPromiseActor(fetchUserDetails),
    logout: promiseFuncToPromiseActor(logout),
    redeemMagicToken: promiseFuncToPromiseActor(redeemMagicToken),
    registerUser: promiseFuncToPromiseActor(registerUser),
    resetPin: promiseFuncToPromiseActor(resetPin),
    sendPinResetEmail: promiseFuncToPromiseActor(sendPinResetEmail),
    updatePayoutDetails: promiseFuncToPromiseActor(updatePayoutDetails),
    updateUserAccountInfo: promiseFuncToPromiseActor(updateUserAccountInfo),
    verifyPhoneNumber: promiseFuncToPromiseActor(verifyPhoneNumber),
    startFaceScan: promiseFuncToPromiseActor(startFaceScan),
    refreshSession: promiseFuncToPromiseActor(refreshSession),
    resendVerificationEmailForLite: promiseFuncToPromiseActor(
      resendVerificationEmailForLite
    ),
    getReferralStats: promiseFuncToPromiseActor(getReferralStats),
    verifyEmailAndGetData: promiseFuncToPromiseActor(liteAuth),
    submitUserFeedback: promiseFuncToPromiseActor(submitUserFeedback),
    sendParamsForForecast: promiseFuncToPromiseActor(sendParamsForForecast),
    uploadAddressDocument: promiseFuncToPromiseActor(uploadAddressDocument),
    submitAddressInfo: promiseFuncToPromiseActor(submitAddressInfo),
  },
  actions: {
    clearAuthContext: assign(clearAuthContext),
    storeAuthData: assign(storeAuthData),
    storePayoutDetails: assign(storePayoutDetails),
    wipePayoutDetails: assign(wipePayoutDetails),
    storeAuthTokenFromEvent: assign(storeAuthTokenFromEvent),
    updateUserDetailsFromEvent: assign(updateUserDetailsFromEvent),
    storeExpiredSessionData: assign(storeExpiredSessionData),
    storeSessionAboutToExpire: assign(storeSessionAboutToExpire),
    updateLiteDetails: assign(updateLiteDetails),
    clearLiteData: assign(clearLiteData),
    storeForecastData: assign(storeForecastData),
    updateUserAddress: assign(updateUserAddress),
    updateAddressDocument: assign(updateAddressDocument),
    removeAddressDocument: assign(removeAddressDocument),
  },
  guards: {
    /**
     * Sanity check if all the necessary info is present before transitioning
     * to a new state
     */
    isAuthenticated,
    hasRefreshToken,
    shouldStartVerifying,
  },
}).createMachine({
  id: 'AuthMachine',
  context: initialContextValues,
  initial: 'NO_AUTH_TOKEN',
  states: {
    NO_AUTH_TOKEN: {
      on: {
        SEND_NEW_TAB_EMAIL: {
          target: 'SENDING_NEW_TAB_EMAIL',
        },
        REDEEM_MAGIC_TOKEN: {
          target: 'REDEEMING_MAGIC_TOKEN',
        },
        REGISTER_USER: {
          target: 'REGISTERING_USER',
        },
        RESET_PIN: {
          target: 'RESETTING_PIN',
        },
        START_FACE_SCAN: {
          target: 'ANON_FACE_SCAN_IN_PROGRESS',
        },
        CLOSE_SUCCESS_MODAL: {
          target: 'NO_AUTH_TOKEN',
        },
        MODIFY_EXPIRED_SESSION_DATA: {
          actions: { type: 'storeExpiredSessionData' },
        },
        REFRESH_SESSION: {
          target: 'REFRESHING_SESSION',
          guard: { type: 'hasRefreshToken' },
        },
        REQUEST_INCOME_FORECAST: {
          target: 'FORECASTING_INCOME',
          actions: { type: 'storeForecastData' },
        },
      },
      description:
        'There is no `auth_token` in context and it should **NOT** be there in this state',
    },

    FORECASTING_INCOME: {
      invoke: {
        id: 'sendParamsForForecastID',
        src: 'sendParamsForForecast',
        input: promiseActorInput,
        onDone: {
          target: 'NO_AUTH_TOKEN',
          actions: { type: 'storeForecastData' },
        },
        onError: {
          target: 'NO_AUTH_TOKEN',
          actions: { type: 'storeForecastData' },
        },
      },
    },

    REFRESHING_SESSION: {
      invoke: {
        id: 'refreshSessionID',
        src: 'refreshSession',
        input: promiseActorInput,
        onDone: {
          target: 'AUTH_TOKEN',
          guard: { type: 'isAuthenticated' },
          actions: { type: 'storeAuthData' },
        },
        onError: {
          target: 'NO_AUTH_TOKEN',
          actions: [
            { type: 'clearAuthContext' },
            { type: 'storeExpiredSessionData' },
          ],
        },
      },
    },

    RESETTING_PIN: {
      invoke: {
        id: 'resetPinID',
        src: 'resetPin',
        input: promiseActorInput,
        onDone: {
          target: 'NO_AUTH_TOKEN',
        },
        onError: {
          target: 'NO_AUTH_TOKEN',
        },
      },
      description: `Used for resetting a user's 
      PIN with a pin reset token`,
    },

    REGISTERING_USER: {
      invoke: {
        id: 'registerUserID',
        src: 'registerUser',
        input: promiseActorInput,
        onDone: [
          {
            target: 'SENT_NEW_TAB_EMAIL',
          },
        ],
        onError: {
          // Goes back to no auth state to allow the user to retry again
          target: 'NO_AUTH_TOKEN',
        },
      },
      description: 'Registers a user to the app',
    },

    SENDING_NEW_TAB_EMAIL: {
      invoke: {
        id: 'sendMagicEmailNewTabServiceID',
        src: 'sendMagicLoginEmail',

        input: promiseActorInput,
        onDone: {
          target: 'SENT_NEW_TAB_EMAIL',
        },
        onError: {
          target: 'NO_AUTH_TOKEN',
        },
      },
      description: `Sends a login email to the user, the email can contain 
        forecast params if the user signs up via "HOW IT WORKS" flow`,
    },

    REDEEMING_MAGIC_TOKEN: {
      invoke: {
        id: 'redeemMagicTokenId',
        src: 'redeemMagicToken',
        input: promiseActorInput,
        onDone: {
          target: 'AUTH_TOKEN',
          actions: { type: 'storeAuthData' },
          guard: { type: 'isAuthenticated' },
        },
        onError: {
          target: 'NO_AUTH_TOKEN',
        },
      },
      description: `Redeems a magic token from UAS and in response gets
        an auth_token, permissions and user_account_info
        `,
    },

    ANON_FACE_SCAN_IN_PROGRESS: {
      invoke: {
        id: 'startFaceScanAnonID',
        src: 'startFaceScan',
        input: promiseActorInput,
        onDone: {
          target: 'AUTH_TOKEN',
          actions: { type: 'storeAuthData' },
          guard: { type: 'isAuthenticated' },
        },
        onError: {
          target: 'NO_AUTH_TOKEN',
        },
      },
    },

    AUTH_FACE_SCAN_IN_PROGRESS: {
      invoke: {
        id: 'startFaceScanAuthID',
        src: 'startFaceScan',
        input: promiseActorInput,
        onDone: {
          target: 'AUTH_TOKEN',
          actions: { type: 'storeAuthData' },
          guard: { type: 'isAuthenticated' },
        },
        // If an error has occurred no new auth token can be obtained, then go
        // back to AUTH_TOKEN state and use the auth_token that is already
        // there, in order for the session not to drop
        onError: {
          target: 'AUTH_TOKEN',
        },
      },
      on: {
        FACE_ENROLL_COMPLETED: {
          actions: { type: 'storeAuthData' },
        },
      },
    },

    AUTH_TOKEN: {
      type: 'parallel',
      invoke: {
        id: 'authTokenStateServiceId',
        src: 'authTokenService',
        input: promiseActorInput,
      },
      //Main events
      on: {
        EXPIRED_SESSION: {
          target: 'NO_AUTH_TOKEN',
          //No need to call logout, server will terminate the session by
          //default since it has expired
          actions: ['clearAuthContext', 'storeExpiredSessionData'],
        },

        DELETE_AUTH_TOKEN: {
          target: 'DELETING_AUTH_TOKEN',
        },

        FETCH_USER_ACCOUNT: {
          target: 'FETCHING_USER_ACCOUNT',
        },

        EXTEND_SESSION: {
          target: 'EXTENDING_SESSION',
        },

        START_FACE_SCAN: {
          target: 'AUTH_FACE_SCAN_IN_PROGRESS',
        },
      },

      states: {
        AUTHENTICATED: {
          initial: 'IDLE',

          states: {
            IDLE: {
              on: {
                DELETE_PAYOUT_DETAILS: {
                  target: 'DELETING_PAYOUT_DETAILS',
                },
                UPDATE_PAYOUT_DETAILS: {
                  target: 'UPDATING_PAYOUT_DETAILS',
                },
                UPDATE_ACCOUNT_INFO: {
                  target: 'UPDATING_USER_ACCOUNT_INFO',
                },
                CREATE_NEW_PIN: {
                  target: 'CREATING_NEW_PIN',
                },
                CHANGE_CURRENT_PIN: {
                  target: 'CHANGING_CURRENT_PIN',
                },
                SEND_PIN_RESET_EMAIL: {
                  target: 'SENDING_PIN_RESET_EMAIL',
                },
                ADD_UNVERIFIED_PHONE: {
                  target: 'ADDING_UNVERIFIED_PHONE',
                },
                VERIFY_PHONE_NUMBER: {
                  target: 'VERIFYING_PHONE_NUMBER',
                },
                CLOSE_USER_ACCOUNT: {
                  target: 'CLOSING_USER_ACCOUNT',
                },
                CANCEL_CLOSING_ACCOUNT: {
                  target: 'CANCELLING_CLOSE_ACCOUNT',
                },
                CREATE_REFERRAL_CODE: {
                  target: 'CREATING_REFERRAL_CODE',
                },
                SESSION_ABOUT_TO_EXPIRE: {
                  actions: ['storeSessionAboutToExpire'],
                },
                GET_REFERRAL_STATS: {
                  target: 'FETCHING_REFERRAL_STATS',
                },
                SUBMIT_USER_FEEDBACK: {
                  target: 'SUBMITTING_USER_FEEDBACK',
                },
                REQUEST_INCOME_FORECAST: {
                  target: 'FORECASTING_INCOME',
                },
                UPLOAD_ADDRESS_DOCUMENT: {
                  target: 'UPLOADING_ADDRESS_DOCUMENT',
                },
                SUBMIT_ADDRESS_INFO: {
                  target: 'SUBMITTING_ADDRESS_INFO',
                },
                REMOVE_UPLOADED_DOCUMENT: {
                  actions: { type: 'removeAddressDocument' },
                },
              },
            },

            UPLOADING_ADDRESS_DOCUMENT: {
              invoke: {
                id: 'uploadAddressDocumentID',
                src: 'uploadAddressDocument',
                input: promiseActorInput,
                onDone: {
                  target: 'IDLE',
                  actions: { type: 'updateAddressDocument' },
                },
                onError: {
                  target: 'IDLE',
                },
              },
            },

            SUBMITTING_ADDRESS_INFO: {
              invoke: {
                id: 'submitAddressInfoID',
                src: 'submitAddressInfo',
                input: promiseActorInput,
                onDone: {
                  target: 'IDLE',
                  actions: {
                    type: 'updateUserAddress',
                  },
                },
                onError: {
                  target: 'IDLE',
                },
              },
            },

            FORECASTING_INCOME: {
              invoke: {
                id: 'sendParamsForForecastID',
                src: 'sendParamsForForecast',
                input: promiseActorInput,
                onDone: {
                  target: 'IDLE',
                  actions: { type: 'storeForecastData' },
                },
                onError: {
                  target: 'IDLE',
                  actions: { type: 'storeForecastData' },
                },
              },
            },

            SUBMITTING_USER_FEEDBACK: {
              invoke: {
                src: 'submitUserFeedback',
                id: 'submitUserFeedbackID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                },
              },
            },

            FETCHING_REFERRAL_STATS: {
              invoke: {
                src: 'getReferralStats',
                id: 'getReferralStatsID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
            },

            DELETING_PAYOUT_DETAILS: {
              invoke: {
                src: 'deletePayoutDetails',
                id: 'deletePayoutDetailsID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['wipePayoutDetails'],
                },
              },
              description: 'Deletes user payout details',
            },

            UPDATING_PAYOUT_DETAILS: {
              invoke: {
                src: 'updatePayoutDetails',
                id: 'updatePayoutDetailsID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['storePayoutDetails'],
                },
              },
              description: 'Updates/adds user payout account details',
            },

            UPDATING_USER_ACCOUNT_INFO: {
              invoke: {
                src: 'updateUserAccountInfo',
                id: 'updateUserAccountInfoID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
              description: `Updates user's unverified account information`,
            },

            CREATING_NEW_PIN: {
              invoke: {
                src: 'createNewPin',
                id: 'createNewPinID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
            },

            CHANGING_CURRENT_PIN: {
              invoke: {
                src: 'changeCurrentPin',
                id: 'changeCurrentPinID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                },
              },
            },

            SENDING_PIN_RESET_EMAIL: {
              invoke: {
                src: 'sendPinResetEmail',
                id: 'sendPinResetEmailID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                },
              },
            },

            ADDING_UNVERIFIED_PHONE: {
              invoke: {
                src: 'addUnverifiedPhoneNumber',
                id: 'addUnverifiedPhoneNumberID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: { type: 'updateUserDetailsFromEvent' },
                },
              },
            },

            VERIFYING_PHONE_NUMBER: {
              invoke: {
                src: 'verifyPhoneNumber',
                id: 'verifyPhoneNumberID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
            },

            CLOSING_USER_ACCOUNT: {
              invoke: {
                src: 'closeUserAccount',
                id: 'closeUserAccountID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
            },

            CANCELLING_CLOSE_ACCOUNT: {
              invoke: {
                src: 'cancelAccountClosing',
                id: 'cancelAccountClosingID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
            },

            CREATING_REFERRAL_CODE: {
              invoke: {
                src: 'createReferralCode',
                id: 'createReferralCodeID',
                input: promiseActorInput,
                onError: {
                  target: 'IDLE',
                },
                onDone: {
                  target: 'IDLE',
                  actions: ['updateUserDetailsFromEvent'],
                },
              },
              description: `Edits user's referral code`,
            },
          },
        },
      },

      description: `Checks for session and opens a web socket
        connection with the UAS`,
    },

    FETCHING_USER_ACCOUNT: {
      invoke: {
        src: 'fetchUserDetails',
        id: 'fetchUserDetailsID',
        input: promiseActorInput,
        onError: {
          target: 'AUTH_TOKEN',
        },
        onDone: {
          target: 'AUTH_TOKEN',
          actions: { type: 'updateUserDetailsFromEvent' },
        },
      },
      description: `Fetches fresh user account info from the backend`,
    },

    EXTENDING_SESSION: {
      invoke: {
        src: 'extendSession',
        id: 'extendSessionID',
        input: promiseActorInput,
        onError: {
          target: 'AUTH_TOKEN',
        },
        onDone: {
          target: 'AUTH_TOKEN',
          actions: ['storeAuthTokenFromEvent', 'storeSessionAboutToExpire'],
        },
      },
      description: `Extends the user's current session`,
    },

    DELETING_AUTH_TOKEN: {
      invoke: {
        id: 'logoutService',
        src: 'logout',
        input: promiseActorInput,
        onDone: {
          target: 'NO_AUTH_TOKEN',
          actions: { type: 'clearAuthContext' },
        },
        onError: {
          //Log the user out anyway
          target: 'NO_AUTH_TOKEN',
          actions: { type: 'clearAuthContext' },
        },
      },
      description: `Logs the user out of the app, and issues a delete
        request to the server to delete current auth token`,
    },

    READY_RESEND_EMAIL: {
      on: {
        CLOSE_SUCCESS_MODAL: {
          target: 'NO_AUTH_TOKEN',
        },

        SEND_NEW_TAB_EMAIL: {
          target: 'SENDING_NEW_TAB_EMAIL',
        },
      },
    },

    SENT_NEW_TAB_EMAIL: {
      after: {
        [AUTH_CONSTANTS.RESEND_EMAIL_TIMER_MILLISECONDS]: {
          target: 'READY_RESEND_EMAIL',
        },
      },
      on: {
        CLOSE_SUCCESS_MODAL: {
          target: 'NO_AUTH_TOKEN',
        },
      },
    },
  },
})
