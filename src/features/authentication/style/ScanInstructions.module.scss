@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define scanInstructions */
.scanInstructions {
  @include mixins.no-user-select;

  &__title {
    text-align: center;
    max-width: 25rem;
    @include mixins.font-style($font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-large );
  }

  &__main-container {
    background-color: colors.$white;
    border-radius: variables.$rounded;
    margin: 0 0 1.875rem 0;
    @include mixins.flex-layout(column);
  }

  &__explainer {
    text-align: center;
    max-width: 31.25rem;
    height: auto;
    @include mixins.font-style($font-size: variables.$font-size-ml);
  }

  &__requirements-container {
    margin-top: 2.25rem;
    @include mixins.flex-layout(column, flex-start, flex-start, 1.25rem);
  }

  &__title-block {
    width: 100%;
    @include mixins.flex-layout(column, flex-start, flex-start, 0.25rem);
  }

  &__title-container {
    @include mixins.flex-layout(row, center, center, 0.625rem);
    @include mixins.font-style($font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-ml,
    );
  }

  &__text {
    @include mixins.font-style($font-size: variables.$font-size-s,
    );
  }

  &__subtext {
    color: colors.$gray-dark;
    background-color: colors.$blue-faint;
    padding: 15px 20px;
    margin-top: 0.625rem;
    width: 100%;
    border-radius: variables.$rounded;
    @include mixins.font-style($font-size: variables.$font-size-s,
    );
  }

  &__country-dropdown {
    width: 100%;
    max-width: unset;
  }
}