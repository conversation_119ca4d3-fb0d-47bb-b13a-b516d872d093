@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define register */
.register {
  // FIXME:Necessary to have this here, because a tests depends on this class
  // and it is needed for the module to generate this class into an object,
  // otherwise the tests will fail because this class does not get generated
  visibility: visible;

  &__age-slider {
    height: 100px;
  }

  &__modal-button {
    width: 100% !important;

    &--mt {
      width: 100% !important;
      margin-top: 40px;
    }
  }

  &__terms {
    @include mixins.font-style($font-weight: variables.$font-bolder !important,
      $color: colors.$blue !important,
      $font-size: variables.$font-size-s );
    cursor: pointer;
    text-decoration: underline;
  }

  &__title {
    @include mixins.font-style($font-size: variables.$font-size-ml,
      $font-weight: variables.$font-semibold );
    margin-bottom: 1.25rem;
    text-align: center;
  }

  &__checkboxes {
    @include mixins.flex-layout(column,
      $justify-content: flex-start,
      $align-items: flex-start);
    gap: 0.9375rem;
    margin: 1.25rem 0;
  }

  &__nav-buttons {
    position: static !important;
    margin-bottom: 20px;
    margin-top: 40px;

    @media screen and (min-width: 901px) and (max-width: 1280px) {
      /* Styles for Desktops */
      margin-top: 40px;
    }
  }

  &__divider {
    margin-top: 40px;
  }

  &__error-msg {
    text-align: center;
    position: relative !important;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    &__age-slider {
      height: initial;
    }

    &__modal-button {
      position: relative;

      &--mt {
        width: 70% !important;
        margin-top: 40px;
      }
    }

    &__divider {
      display: none;
    }

    &__error-msg {
      text-align: center;
      margin: 10px 0;
    }

    &__country-dropdown {
      margin-top: 1.25rem;
    }

    &__nav-buttons {
      width: 100% !important;
      margin-top: 0;
    }
  }
}