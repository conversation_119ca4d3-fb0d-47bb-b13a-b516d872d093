@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/mixins';

/** @define pin-setup-page */
.pin-setup-page {
  padding: 0.625rem;

  &__header {
    @include mixins.font-style($font-weight: variables.$font-bold,
      $font-size: variables.$font-size-xxlarge );
    text-align: center;
  }

  &__container {
    @include mixins.flex-layout($flex-direction: column, $gap: .75rem);
  }

  &__forgot-pin-button {
    @include mixins.flex-layout;
    background-color: transparent;
    color: colors.$blue !important;
    width: 200px;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    padding: 0;
  }
}