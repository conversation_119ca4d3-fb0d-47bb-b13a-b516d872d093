@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

@mixin border-left {
  .userInput__input-element--default {
    border-top-left-radius: variables.$rounded;
    border-bottom-left-radius: variables.$rounded;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

@mixin no-border-left {
  .userInput__input-element--default {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

/** @define phone-input */
.phone-input {
  @include no-border-left;
  &__inputs-con {
    @include mixins.flex-layout($align-items: flex-start);
  }

  &__dial-code {
    flex-basis: 61%;
    @include border-left;
  }

  &__phone-num {
    flex-basis: 50%;
  }
}
