@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define login-form */
.login-form {
  @include mixins.yellow-sun-bg-top-left;

  &__error-message {
    text-align: center;
    margin-bottom: 10px;
  }

  &__buttons {
    text-align: center;
    margin-top: 2rem;
    height: 300px;
  }

  &__divider {
    margin-top: 2.5rem;
    &-top {
      margin-bottom: 2.875rem;
    }
  }

  &__explainer-text {
    @include mixins.font-style;
    @include mixins.no-user-select;
    margin: 1.5625rem 0;
    text-align: justify;
  }

  &__facetec-browser-error-text {
    @include mixins.font-style($font-size: variables.$font-size-sm);
    @include mixins.no-user-select;
    margin: 0.625rem 0;
  }

  &__facetec-browser-error-link {
    @include mixins.font-style(
      $font-size: variables.$font-size-sm,
      $color: colors.$blue
    );
    text-decoration: underline;
    cursor: pointer;
  }

  &__btn-separator {
    @include mixins.font-style;
    margin: 0.625rem 0;
  }

  //Bottom footer on the login form
  &__sign-up-section {
    display: none;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    background: none;

    &__sign-up-section {
      @include mixins.font-style($font-size: variables.$font-size-ml);
      display: block;
      position: absolute;
      width: 100%;
      bottom: 12%;
      left: 0;
      padding: 1.25rem;
      text-align: center;
    }

    &__sign-up-btn {
      @include mixins.font-style(
        $font-weight: variables.$font-bold,
        $color: colors.$blue
      );
      text-decoration: underline;
      margin-left: 5px;
    }
  }
}
