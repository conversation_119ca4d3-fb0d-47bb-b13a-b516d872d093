@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define supported-browsers */
.supported-browsers {
  &__container {
    @include mixins.font-style;
    @include mixins.no-user-select;
    position: relative;
    border-radius: variables.$rounded;
    background-color: colors.$white;
    text-align: center;
    width: 80%;
    margin: 8rem auto;
  }
  &__logo {
    position: absolute;
    top: -110px;
    left: calc(50% - 68px);
    width: 136px;
    height: 162px;
  }
  &__back-btn {
    position: absolute;
    height: 3.5rem;
    padding: 0.5rem;
    left: 0rem;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }
  &__title {
    @include mixins.font-style(
      $font-size: variables.$font-size-xl,
      $font-weight: variables.$font-semibold
    );
    padding-top: 2.5rem;
  }

  &__text {
    @include mixins.font-style($font-size: variables.$font-size-xlarge);
    width: 70%;
    margin: 1.8rem auto;
  }

  &__qr-mockup {
    width: 10rem;
    height: 10rem;
    background-color: colors.$gray-dark;
    margin: 0 auto;
  }
  @media only screen and (max-width: variables.$mobile-devices) {
    &__container {
      margin: 7rem auto;
    }

    &__back-btn {
      height: 2.5rem;
      padding: 0.5rem;
    }

    &__logo {
      top: -40px;
      left: calc(50% - 32px);
      width: 64px;
      height: 70px;
    }

    &__title {
      @include mixins.font-style(
        $font-size: variables.$font-size-xxlarge,
        $font-weight: variables.$font-semibold
      );
    }

    &__text {
      @include mixins.font-style($font-size: variables.$font-size-m);
      width: 90%;
      margin: 1rem auto;
    }
  }
}
