@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define pinInput */
.pinInput {
  position: relative;
  @include mixins.flex-layout(column);

  &__pinInputLabel {
    position: relative;
    top: unset;
    width: unset;
  }

  &__title {
    font-size: variables.$font-size-large;
    font-weight: variables.$font-semibold;
    text-align: center;
    color: colors.$gray-dark;
  }

  &__container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 8px;
  }

  &__error-text {
    text-align: center;
    position: relative !important;
  }

  &__field {
    @include mixins.pin-field;

    &--error {
      @include mixins.pin-field($border: 1px solid red,
        $focus-border: 1px solid red);
    }
  }
}