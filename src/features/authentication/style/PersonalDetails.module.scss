@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define personalDetails */
.personalDetails {
  &__phone-input {
    @include mixins.flex-layout($justify-content: flex-start);
    width: 100%;
  }

  &__id-verify {
    @include mixins.no-user-select;
    margin: 1.875rem 0;
    max-width: 25rem;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__nav-btns {
      padding: 0 15px;
    }

    &__divider {
      display: none;
    }

    &__phone-input {
      height: 3.75rem;
      margin-top: 20px;
    }

    &__id-verify {
      max-width: 100%;
      padding: 0 variables.$mobile-spacing;
      margin-bottom: 1.25rem;
    }

    &__bottom-card-divider {
      display: none;
    }
  }
}