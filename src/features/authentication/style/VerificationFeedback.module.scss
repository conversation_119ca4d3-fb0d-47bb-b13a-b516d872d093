@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define verificationFeedback */
.verificationFeedback {
    @include mixins.flex-layout(column, flex-start, flex-start, .5rem);
    height: 100%;

    &__title {
        margin-top: 1.25rem;
        @include mixins.font-style($font-weight: variables.$font-semibold,
            $font-size: variables.$font-size-ml,
        );
    }

    &__subtitle {
        @include mixins.font-style($font-size: variables.$font-size-ml,
        );
    }

    &__card {
        margin-top: auto;
        width: 100%;
    }
}