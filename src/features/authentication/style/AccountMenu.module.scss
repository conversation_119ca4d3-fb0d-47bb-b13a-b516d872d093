@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define accountMenu */
.accountMenu {
  @include mixins.list-menu;
  @include mixins.no-user-select;
  padding-top: variables.$split-layout-top-padding;
  padding-left: 3.75rem;
  padding-right: 3.75rem;
  margin-bottom: 2.5rem;

  &__auth-button {
    margin-top: 20px;
    margin-bottom: 30px;
    max-width: 350px;
    display: none;
  }

  &__divider {
    width: 95% !important;
    margin-inline: auto;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    margin-bottom: initial;
    padding: 2rem 2rem 0rem 2rem;
    &__auth-button {
      display: block;
    }
  }

  @media only screen and (max-width: variables.$mobile-devices-s) {
    padding: 1.5rem;
  }
}
