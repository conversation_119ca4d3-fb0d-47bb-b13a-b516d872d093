@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define missingInformation */
.missingInformation {
  background-color: colors.$white;
  @include mixins.flex-layout(column, center, unset, 1rem);

  &__container {
    margin-bottom: 1rem;
  }

  &__explainer {
    @include mixins.font-style($font-size: variables.$font-size-s);
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    background-color: transparent;
  }
}
