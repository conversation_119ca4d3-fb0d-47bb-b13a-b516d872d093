@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

$mytt-init-bg: url('../../../assets/icon-mt_avatar-shape.svg');

/** @define accountSummary */
.accountSummary {
  display: none;
  &__icon {
    @include mixins.flex-layout;
    font-weight: variables.$font-semibold;
    text-transform: uppercase;
    background: $mytt-init-bg no-repeat center;
    color: colors.$white;
    width: 38px;
    height: 38px;
    margin-left: 10px;
    padding: 0.625rem;
  }

  &__user-initials {
    color: colors.$gray-dark;
  }

  &__info {
    @include mixins.flex-layout(row-reverse, flex-start);
    max-width: 350px;
  }

  &__mail-name {
    font-size: variables.$font-size-s;
    line-height: 20px;
    color: colors.$white;
    margin: 0;
    text-align: right;
  }

  &__fullname {
    margin: 0;
    font-weight: variables.$font-semibold;
    text-transform: capitalize;
  }
  &__email {
    margin: 0;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.flex-layout(row);
    width: 100%;
    height: 60px;
    background-color: colors.$blue;
    position: sticky;
    top: 0;
    left: 0;
    box-shadow: 0px 4px 4px colors.$black-ligher;
    z-index: 9999; //Maybe this is an overkill?
    border-bottom: 1px solid colors.$blue-light;

    &__info {
      @include mixins.flex-layout(row, flex-start);
      min-width: 350px;
    }

    &__mail-name {
      font-size: variables.$font-size-s;
      line-height: 20px;
      color: colors.$white;
      margin: 0;
      text-align: left;
    }

    &__icon {
      margin-right: 10px;
    }
  }
}
