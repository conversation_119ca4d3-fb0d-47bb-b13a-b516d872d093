@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define sessionExtensionModal */
.sessionExtensionModal {
  text-align: center;
  padding: 0;

  &__title {
    @include mixins.font-style(
      $font-size: variables.$font-size-l,
      $color: colors.$blue
    );
    color: colors.$blue;
    font-size: variables.$font-size-l;
    margin-bottom: 1.25rem;
  }

  &__explainer-text {
    font-size: variables.$font-size-m;
    margin-bottom: 1.25rem;
    @include mixins.font-style($font-size: variables.$font-size-m);
  }

  &__countdown-timer {
    @include mixins.flex-layout;
    @include mixins.font-style(
      $font-size: variables.$font-size-large,
      $font-weight: variables.$font-semibold
    );
    width: 100%;
    margin: 1.25rem 0;
    color: red;
  }
}
