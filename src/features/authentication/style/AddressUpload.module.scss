@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/mixins';

/* @define address-upload */
.address-upload {
  &__note {
    margin: 1.875rem 0;
    @include mixins.no-user-select;
    @include mixins.font-style($font-size: variables.$font-size-m);
    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.font-style($font-size: variables.$font-size-s);
    }
  }

  &__warning {
    @include mixins.no-user-select;
    @include mixins.font-style;
    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.font-style($font-size: variables.$font-size-s);
    }
  }

  &__uploaded-files {
    margin-top: 1.5625rem;
    @include mixins.no-user-select;
    @include mixins.font-style($font-size: variables.$font-size-s);
  }

  &__no-files-txt {
    text-align: center;
    margin-top: 4.375rem;
    opacity: 0.6;
    @include mixins.no-user-select;
    @include mixins.font-style;
    @media only screen and (max-width: variables.$mobile-devices) {
      margin-top: 15px;
      @include mixins.font-style($font-size: variables.$font-size-s);
    }
  }

  &__error {
    margin-top: 0.625rem;
  }
}
