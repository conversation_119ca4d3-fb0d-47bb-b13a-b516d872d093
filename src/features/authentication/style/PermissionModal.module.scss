@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/mixins';

/** @define permissionModal */
.permissionModal {
	&__content-wrapper {
		width: 30rem;
		max-width: unset;
		padding: 3rem 2rem;
		@include mixins.flex-layout($flex-direction: row, $gap: 1rem);
	}

	&__title {
		text-align: left;
		margin-bottom: 0.9375rem;
		@include mixins.font-style($font-size: variables.$font-size-l,
			$font-weight: variables.$font-semibold );
	}

	&__content {
		text-align: left;
		margin-bottom: 0.9375rem;
		@include mixins.font-style;
	}

	&_text-wrapper {
		width: 100%;
		@include mixins.flex-layout($flex-direction: column, $gap: 0.5rem, $align-items: 'start');
	}
}