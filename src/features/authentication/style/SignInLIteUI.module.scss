@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define sign-in-lite-ui */
.sign-in-lite-ui {
  @include mixins.flex-layout(column);
  &__input {
    width: 21.875rem;
  }
  &__button {
    width: 21.875rem;
    margin-top: 25px;
  }

  &__header {
    @include mixins.font-style(
      $color: colors.$gray-dark,
      $font-weight: variables.$font-bold,
      $font-size: variables.$font-size-l
    );
    @include mixins.no-user-select;
    text-align: center;
    margin-bottom: 20px;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__input {
      width: 100%;
    }
    &__button {
      width: 100%;
    }
  }
}
