@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define account-layout */
.account-layout {
  //Split layout used in the account section of the app
  @include mixins.flex-layout;
  background: linear-gradient(
    to right,
    colors.$blue-faint 50%,
    colors.$white 50%
  );
  height: variables.$split-page-layout-height;

  &__container {
    @include mixins.flex-layout;
    width: variables.$account-section-container-width;
  }

  &__left {
    background-color: colors.$blue-faint;
    height: variables.$split-page-layout-height;
    overflow-y: scroll;
  }

  &__right {
    flex-basis: 75%;
    padding-top: variables.$split-layout-top-padding;
    padding-left: 6.25rem;
    padding-right: 10%;
    height: variables.$split-page-layout-height;
    background-color: colors.$white;
    overflow-y: scroll;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__container {
      width: 100%;
    }
    &__left {
      display: none;
    }
    &__right {
      flex-basis: 100%;
      padding-top: 0;
      padding-left: 0;
      padding-right: 0;
      margin-top: 1.25rem;
      margin-bottom: 1.25rem;
      overflow-y: scroll;
    }
  }
}
