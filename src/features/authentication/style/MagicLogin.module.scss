@use './../../../common/style/abstracts/mixins';
@use './../../../common/style/abstracts/variables';

/** @define magic-login */
.magic-login {
  @include mixins.flex-layout(column, center, center);
  &__suspense {
    background-color: white;
    position: fixed;
    height: 100vh;
    width: 100%;
    z-index: 99999999;
  }

  &__icon {
    width: 50px;
    height: 50px;
  }

  &__message {
    margin: 1.25rem 0;
    text-align: center;
    @include mixins.font-style(
      $font-size: variables.$font-size-large,
      $font-weight: variables.$font-bold
    );
  }

  &__countdown {
    @include mixins.font-style($font-size: variables.$font-size-large);
  }
}
