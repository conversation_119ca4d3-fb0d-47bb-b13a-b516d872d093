import TextInput from '../../../common/components/TextInput'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { inputRestrictionRegex } from '../../../common/constants/Regex'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { sanitizeInputValue } from '../../../common/utils/UtilFunctions'
import { SignUpBasicInfoProps } from '../types/SignUpBasicInfo.types'

/**
 * BasicUserInfo renders input fields
 * for a user's first name, last name, and email address during sign-up.
 * It provides validation for each input and displays error messages if the
 * inputs are invalid.
 */
const SignUpBasicInfo = ({
  firstName,
  setFirstName,
  validateFirstName,
  firstNameValidated,
  lastName,
  setLastName,
  validateLastName,
  lastNameValidated,
  userEmail,
  setUserEmail,
  validateEmail,
  emailValidated,
}: SignUpBasicInfoProps) => {
  const t = useTranslate()

  return (
    <>
      <TextInput
        value={firstName}
        onChange={(name) =>
          setFirstName(
            sanitizeInputValue({ inputValue: name, onlySpaces: true })
          )
        }
        validatorFunction={validateFirstName}
        label={t('INPUT_LABEL.SIGN_UP_FIRSTNAME')}
        placeholder={t('FIRST_NAME_PLACEHOLDER')}
        errorMessage={firstNameValidated}
        dataTestID={UI_TEST_ID.firstNameInput}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.names}
        trackActivity={{
          trackId: 'register_first_name',
        }}
      />
      <TextInput
        value={lastName}
        onChange={(lastName) =>
          setLastName(
            sanitizeInputValue({ inputValue: lastName, onlySpaces: true })
          )
        }
        validatorFunction={validateLastName}
        label={t('INPUT_LABEL.SIGN_UP_LASTNAME')}
        placeholder={t('LAST_NAME_PLACEHOLDER')}
        errorMessage={lastNameValidated}
        dataTestID={UI_TEST_ID.lastNameInput}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.names}
        trackActivity={{
          trackId: 'register_last_name',
        }}
      />
      <TextInput
        value={userEmail}
        onChange={(value) => setUserEmail(value.trim())}
        validatorFunction={validateEmail}
        label={t('INPUT_LABEL.SIGN_IN_EMAIL')}
        placeholder={t('EMAIL_PLACEHOLDER_TEXT')}
        errorMessage={emailValidated}
        dataTestID={UI_TEST_ID.emailInput}
        inputMode={'email'}
        autoComplete={'on'}
        restrictionRegex={inputRestrictionRegex.noWhiteSpace}
        trackActivity={{
          trackId: 'register_email',
        }}
      />
    </>
  )
}

export default SignUpBasicInfo
