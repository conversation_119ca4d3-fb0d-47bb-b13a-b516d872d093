import Button from '../../../common/components/Button'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { PUBLIC } from '../../../routes/Route'
import { useRegistration } from '../hooks/useRegistration'
import style from '../style/Register.module.scss'
import { SignUpNavigationButtonsProps } from '../types/SignUpNavigationButtons.types'

/**
 * Renders the navigation buttons for the sign-up page.
 *
 * This component includes a custom button for submitting the registration form
 * and a back button that navigates to the home page. The submit button is only
 * enabled if the required registration information is validated. It also shows
 * a loading state when the user registration process is ongoing.
 */
const PageSignUpNavigationButtons = ({
  forecastUserData,
  registerButtonLabel,
  forecastPageRegisterModal,
  validatedRegisterInfo,
  handleRegistration,
}: SignUpNavigationButtonsProps) => {
  const navigate = useCustomNavigation()
  const { currentState } = useRegistration({ forecastUserData })

  return (
    <NavigationButtons
      hideBackButton={forecastPageRegisterModal}
      className={style['register__nav-buttons']}
      hideActionButton
      backButtonWhite
      disabledFixedOnMobile
      onClickFirst={() => navigate(PUBLIC.HOME)}
      trackActivityBackButton={{
        trackId: 'register_back',
      }}
      customButton={
        <Button
          disabled={!validatedRegisterInfo}
          variant="primary"
          onClick={handleRegistration}
          loading={
            currentState === 'REGISTERING_USER' ||
            currentState === 'SENDING_NEW_TAB_EMAIL'
          }
          trackActivity={{
            trackId: 'register_submit_signup',
          }}
        >
          {registerButtonLabel}
        </Button>
      }
    />
  )
}

export default PageSignUpNavigationButtons
