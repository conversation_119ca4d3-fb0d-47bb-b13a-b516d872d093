import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/LoginForm.module.scss'

const FaceTecUnsupportedBrowserMessage = ({
  setRenderunsupBrowserModal,
}: {
  setRenderunsupBrowserModal: (value: boolean) => void
}) => {
  const t = useTranslate()
  return (
    <p
      className={style['login-form__facetec-browser-error-text']}
      data-testid={UI_TEST_ID.faceScanBrowserErrorText}
    >
      {t('LOGIN_FORM.FACETEC_UNSUPPORTED_BROWSER')}
      <a
        className={style['login-form__facetec-browser-error-link']}
        // biome-ignore lint/a11y/useValidAnchor: <TODO: Should use button here if possible>
        onClick={() => {
          setRenderunsupBrowserModal(true)
        }}
        data-testid={UI_TEST_ID.faceScanBrowserErrorLink}
      >
        {t('LOGIN_FORM.FACETEC_UNSUPPORTED_BROWSER_LINK')}
      </a>
    </p>
  )
}

export default FaceTecUnsupportedBrowserMessage
