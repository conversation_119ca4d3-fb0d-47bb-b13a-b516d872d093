import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { AuthMachineContext } from '../types/AuthMachineTypes.type'
import { sessionTerminationMessage } from '../utils/consts'

/**
 * Renders a success modal if the session is expired, a session
 * is expired if `context.session_expire_message` is not `null`
 */
const ExpiredSessionModal = ({
  sessionExpired,
  dismissExpiredSessionModal,
}: {
  sessionExpired: AuthMachineContext['sessionExpired']
  dismissExpiredSessionModal: () => void
}) => {
  const t = useTranslate()

  return (
    <ConfirmationModal
      isOpen
      content={t(
        sessionTerminationMessage[
          sessionExpired?.notifyUiWith ?? 'normalExpiredNotification'
        ]
      )}
      icon={ASSET.infoamber}
    >
      <Button onClick={dismissExpiredSessionModal}>
        {t('COMMON.CONTINUE_BUTTON')}
      </Button>
    </ConfirmationModal>
  )
}

export default ExpiredSessionModal
