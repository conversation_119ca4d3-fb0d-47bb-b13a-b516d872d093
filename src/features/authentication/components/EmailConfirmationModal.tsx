import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import TimerButton from '../../../common/components/TimerButton'
import { ANIMATION } from '../../../common/constants/Animations'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { EmailConfirmationModalProps } from '../types/EmailConfirmationModal.type'
import { AUTH_CONSTANTS } from '../utils/consts'
import { millisecondsToSeconds } from '../utils/UtilsFunctions'

/**
 * Renders a success modal if the email was successfully sent,
 * `emailSuccessfullySent` function does the check if the app is in the
 * successfully sent email state
 */
const EmailConfirmationModal = ({
  email,
  state,
  modalTextContent,
  onDismiss,
  onResend,
  isOpen,
}: EmailConfirmationModalProps) => {
  const t = useTranslate()

  return (
    <ConfirmationModal
      isOpen={isOpen}
      title="SUCCESS.HEADERTEXT.MODAL"
      content={modalTextContent}
      contentValues={{ email }}
      animatedIcon={ANIMATION.checkmark}
    >
      <Button
        onClick={onDismiss}
        trackActivity={{
          trackId: 'register_dismiss',
        }}
      >
        {t('SUCCESS.MODAL.DISMISS.BUTTON')}
      </Button>
      <TimerButton
        variant="alternative"
        onClick={onResend}
        seconds={millisecondsToSeconds(
          AUTH_CONSTANTS.RESEND_EMAIL_TIMER_MILLISECONDS
        )}
        loading={state === 'SENDING_NEW_TAB_EMAIL'}
        disabled={state !== 'READY_RESEND_EMAIL'}
        trackActivity={{
          trackId: 'register_resend_email',
        }}
      >
        {t('ACCOUNT.EMAIL_SEND_AGAIN_BUTTON')}
      </TimerButton>
    </ConfirmationModal>
  )
}

export default EmailConfirmationModal
