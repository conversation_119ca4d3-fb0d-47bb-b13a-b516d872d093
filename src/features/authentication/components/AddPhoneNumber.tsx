import { useState } from 'react'
import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ASSET } from '../../../common/constants/Assets'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { hasStrongAuth } from '../../../common/utils/UtilFunctions'
import { PERSONAL_DETAILS } from '../../../routes/Route'
import { useAccountService } from '../hooks/useAccountService'
import { AddPhoneNumberProps } from '../types/AddPhoneNumber.types'
import DiscardChangesModal from './DiscardChangesModal'
import PhoneInput from './PhoneInput'

const AddPhoneNumber = ({
  className,
  saveEditedDetails,
  userIsTyping,
  navigateToBiometrics,
}: AddPhoneNumberProps) => {
  const t = useTranslate()
  const navigate = useCustomNavigation()
  const {
    context: { user_details, permissions },
  } = useAccountService()

  const [enrollModalOpen, setEnrollModalOpen] = useState(false)
  const [showDiscardModal, setShowDiscardModal] = useState(false)

  /**
   * If the user has added their phone number and they are not in
   * edit mode they won't be able to navigate to the edit phone page
   */
  const addPhoneNumber = () => {
    if (userIsTyping) {
      setShowDiscardModal(true)
    }

    if (user_details?.phone_number) {
      //If phone number is edited once then check to display warning modal if
      //user has write permissions
      if (hasStrongAuth(permissions)) {
        navigate(PERSONAL_DETAILS.PHONE_NUMBER)
        return
      }
      setEnrollModalOpen(true)
      setShowDiscardModal(false)
      return
    }

    navigate(!userIsTyping ? PERSONAL_DETAILS.PHONE_NUMBER : '')
  }

  return (
    <>
      <DiscardChangesModal
        isOpen={showDiscardModal}
        onClose={() => setShowDiscardModal(false)}
        onSave={() =>
          saveEditedDetails(() => {
            setShowDiscardModal(false)
            navigate(PERSONAL_DETAILS.PHONE_NUMBER)
          })
        }
      />
      <ConfirmationModal
        isOpen={enrollModalOpen}
        icon={ASSET.yellowShield}
        title={t('FUND_PENSION.WARNING_MODAL_TITLE')}
        content={t('ONBOARDING.MODAL_ERROR_LOGIN_MAGIC_LINK')}
      >
        <Button onClick={navigateToBiometrics}>
          {t('PROMPT_BUTTON.ENROLL_FACE')}
        </Button>
        <Button variant="alternative" onClick={() => setEnrollModalOpen(false)}>
          {t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
        </Button>
      </ConfirmationModal>
      <div className={className} onClick={addPhoneNumber}>
        <div
          style={{
            // Prevents the dropdown from deploying
            // when user clicks to change their phone number
            pointerEvents: 'none',
          }}
        >
          <PhoneInput
            value={user_details?.phone_number}
            label={t('PHONE_NUMBER_INPUT_LABEL')}
            optional
          />
        </div>
      </div>
    </>
  )
}

export default AddPhoneNumber
