import CommonCard from '../../../common/components/card/CommonCard'
import { ASSET } from '../../../common/constants/Assets'
import style from '../style/RejectedDocument.module.scss'
import { RejectedDocumentProps } from '../types/RejectedDocument.types'

/**
 * Renders an explanation why a document has been rejected, and a specific
 * reason for the rejection
 */
const RejectedDocument = ({
  genericExplanation,
  rejectionReason,
  rejectionExplanation,
}: RejectedDocumentProps) => {
  return (
    <section className={style['rejected-document']}>
      <p className={style['rejected-document__gen-explanation']}>
        {genericExplanation}
      </p>
      <CommonCard
        icon={ASSET.deleteAccount}
        iconSize="large"
        title={rejectionReason as string}
        subtitle={rejectionExplanation}
        variant="gray-dirty"
      />
    </section>
  )
}

export default RejectedDocument
