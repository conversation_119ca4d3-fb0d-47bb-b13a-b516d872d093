import { t } from 'i18next'
import CountryDropdown from '../../../common/components/CountryDropdown'
import Dropdown from '../../../common/components/Dropdown'
import TextInput from '../../../common/components/TextInput'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { inputRestrictionRegex } from '../../../common/constants/Regex'
import { AddressFieldsProps } from '../types/AddressFields.types'
import { countriesWithStates } from '../utils/consts'

/**
 * Fields for user to enter their address, country, street address line 1,
 * street address line 2, city, province/state, and zip code
 */
const AddressFields = ({
  readOnly,
  streetAddress,
  streetAddressTwo,
  city,
  state,
  postalCode,
  onStreetAddressChange,
  onStreetAddressTwoChange,
  onCityChange,
  onStateChange,
  onPostalCodeChange,
  streetAddressLineOneValidated,
  streetAddressLineTwoValidated,
  cityValidated,
  usaStateValidated,
  postalCodeValidated,
  validateStreetAddressLineOne,
  validateStreetAddressLineTwo,
  validateCity,
  country,
  onCountryChange,
}: AddressFieldsProps) => {
  return (
    <>
      <CountryDropdown
        readOnly={readOnly}
        optional
        value={country ?? ''}
        onChange={onCountryChange}
        label={t('INPUT_LABEL.COUNTRY')}
      />
      {countriesWithStates?.[country ?? ''] ? (
        <Dropdown
          options={countriesWithStates[country ?? '']}
          value={state}
          onChange={onStateChange}
          itemKey={{ displayKey: 'name' }}
          searchBy={['name', 'iso_code']}
          label={t('INPUT_LABEL.STATE')}
          placeholder={t('STATE_DROPDOWN_PLACEHOLDER')}
          errorMessage={usaStateValidated}
          readOnly={readOnly}
          dataTestID={UI_TEST_ID.usStateDropdown}
        />
      ) : (
        <TextInput
          value={state as string}
          onChange={onStateChange}
          label={t('INPUT_LABEL.STATE_PROVINCE')}
          placeholder={t('STATE_DROPDOWN_PLACEHOLDER')}
          errorMessage={usaStateValidated}
          dataTestID={UI_TEST_ID.usStateDropdown}
          maxLength={INPUT_LIMIT.GENERIC_MAX}
          restrictionRegex={inputRestrictionRegex.usaAddress}
          readOnly={readOnly}
        />
      )}
      <TextInput
        value={streetAddress}
        onChange={onStreetAddressChange}
        label={t('STREET_ADDRESS_LINE1_LABEL')}
        placeholder={t('STREET_ADDRESS_LINE1_PLACEHOLDER')}
        errorMessage={streetAddressLineOneValidated}
        dataTestID={UI_TEST_ID.streetAddressLine1Input}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.usaAddress}
        readOnly={readOnly}
        validatorFunction={validateStreetAddressLineOne}
      />
      <TextInput
        value={streetAddressTwo}
        onChange={onStreetAddressTwoChange}
        label={t('STREET.ADDRESS.LINE2.LABEL')}
        placeholder={t('STREET_ADDRESS_LINE2_PLACEHOLDER')}
        errorMessage={streetAddressLineTwoValidated}
        dataTestID={UI_TEST_ID.streetAddressLine2Input}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.usaAddress}
        optional
        readOnly={readOnly}
        validatorFunction={validateStreetAddressLineTwo}
      />
      <TextInput
        value={city}
        onChange={onCityChange}
        label={t('INPUT_LABEL.CITY')}
        placeholder={t('CITY_INPUT_PLACEHOLDER')}
        errorMessage={cityValidated}
        dataTestID={UI_TEST_ID.cityInput}
        maxLength={INPUT_LIMIT.GENERIC_MAX}
        restrictionRegex={inputRestrictionRegex.usaCity}
        readOnly={readOnly}
        validatorFunction={validateCity}
      />

      <TextInput
        value={postalCode}
        onChange={onPostalCodeChange}
        label={t('ZIP_CODE_LABEL')}
        placeholder={t('ZIP_CODE_PLACEHOLDER')}
        errorMessage={postalCodeValidated}
        dataTestID={UI_TEST_ID.zipCodeInput}
        inputMode="numeric"
        readOnly={readOnly}
      />
    </>
  )
}

export default AddressFields
