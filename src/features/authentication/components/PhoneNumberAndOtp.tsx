import { PhoneNumberAndOtpProps } from '../types/PhoneNumberVerification.type'
import PhoneInput from './PhoneInput'
import SmsVerification from './SmsVerification'

/**
 * Renders a phone number input, if the phone number is submitted for
 * verification then the SMS OTP fields are rendered instead
 */
const PhoneNumberAndOtp = ({
  onFailedPhoneVerification,
  onPhoneNumberVerified,
  onChange,
  unverifiedPhoneNum,
  phoneNumberValidated,
  validatePhoneNumber,
  smsSent,
}: PhoneNumberAndOtpProps) => {
  if (smsSent) {
    return (
      <SmsVerification
        onPhoneNumberVerified={(verifiedPhoneNumber) => {
          onPhoneNumberVerified(verifiedPhoneNumber)
        }}
        onFailedPhoneVerification={() => onFailedPhoneVerification?.()}
        unverifiedPhoneNumber={unverifiedPhoneNum}
      />
    )
  }

  return (
    <PhoneInput
      validatorFunction={validatePhoneNumber}
      errorMessage={phoneNumberValidated}
      onChange={(unverifiedPhone) => {
        onChange?.(unverifiedPhone)
      }}
      value={unverifiedPhoneNum}
    />
  )
}

export default PhoneNumberAndOtp
