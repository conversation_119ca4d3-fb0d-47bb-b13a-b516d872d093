import Checkbox from '../../../common/components/Checkbox'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useLegalMachine } from '../../agreements/hooks/useLegalMachine'
import style from '../style/Register.module.scss'
import { SignUpCheckboxesProps } from '../types/SignUpCheckboxes.types'

/**
 * SignUpCheckboxes renders checkboxes for accepting the Terms and Conditions
 * and opting-in for email updates
 */
const SignUpCheckboxes = ({
  termsAndConditions,
  setTermsAndConditions,
  emailUpdates,
  setEmailUpdates,
  setTermsVersion,
  setRenderTermsAndConditions,
}: SignUpCheckboxesProps) => {
  const t = useTranslate()
  const { legalContext } = useLegalMachine()

  const handleTermsAndConditionsChange = () => {
    setTermsVersion(legalContext?.agreement?.['TermsAndConditions']?.version)
    setTermsAndConditions(!termsAndConditions)
  }
  return (
    <section className={style['register__checkboxes']}>
      <Checkbox
        checked={termsAndConditions}
        onChange={handleTermsAndConditionsChange}
        label={t('ONBOARDING.CHECKBOX_TEXT')}
        onClickLabel={() => {
          setRenderTermsAndConditions(true)
        }}
        labelClassName={style['register__terms']}
        dataTestIDLabel={UI_TEST_ID.termsLabel}
        trackActivity={{ trackId: 'register_tac' }}
      />
      <Checkbox
        checked={emailUpdates}
        onChange={setEmailUpdates}
        label={t('EMAIL_UPDATES_LABEL')}
        optional
        trackActivity={{ trackId: 'register_email_news' }}
      />
    </section>
  )
}
export default SignUpCheckboxes
