import { Trans } from 'react-i18next'
import Icon from '../../../common/components/Icon'
import style from '../style/ScanInstructions.module.scss'
import { ScanInstructionContentProps } from '../types/ScanInstructions.type'

/** Renders a single instruction for the face scan modal. */
const ScanInstructionRequirements = ({
  fileName,
  title,
  text,
  subtext,
}: ScanInstructionContentProps) => (
  <div className={style['scanInstructions__title-block']}>
    <div className={style['scanInstructions__title-container']}>
      <Icon fileName={fileName} />
      <p>
        <Trans i18nKey={title} />
      </p>
    </div>
    <p className={style['scanInstructions__text']}>
      <Trans i18nKey={text} />
    </p>
    {subtext && (
      <p className={style['scanInstructions__subtext']}>
        <Trans i18nKey={subtext} />
      </p>
    )}
  </div>
)

export default ScanInstructionRequirements
