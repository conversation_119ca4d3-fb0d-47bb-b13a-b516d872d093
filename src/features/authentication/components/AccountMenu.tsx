import Divider from '../../../common/components/Divider'
import MenuCard from '../../../common/components/MenuCard'
import MenuCardItem from '../../../common/components/MenuCardItem'
import MobileAppBar from '../../../common/components/MobileAppBar'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU } from '../../../routes/Route'
import { isDisabled } from '../../DisabledLaunchFeatures'
import { useAccountService } from '../hooks/useAccountService'
import style from '../style/AccountMenu.module.scss'
import { MenuItemsFunction } from '../types/AccountMenu.types'
import { UserDetails } from '../types/AuthMachineTypes.type'
import { kycAlertCount } from '../utils/UtilsFunctions'
import AccountSummary from './AccountSummary'
import LogoutButton from './LogoutButtonAndConfirmModal'

const menuItems: MenuItemsFunction = (user_details?: UserDetails) => [
  {
    title: 'ACCOUNT.MENU_GROUP_TITLE_PROFILE',
    items: [
      {
        to: ACCOUNT_MENU.PERSONAL_DETAILS,
        mainText: 'PERSONAL_DETAILS.FORM_TITLE',
        icon: ASSET.iconaccountmenuperonaldetail,
        alertCount: isDisabled
          ? 0
          : kycAlertCount(user_details)?.personalDetails,
      },
      {
        isDivider: true,
      },

      {
        to: ACCOUNT_MENU.ADDRESS_VERIFICATION,
        mainText: 'ACCOUNT.MENU_ITEM_RESIDENCY_VERIFICATION',
        icon: ASSET.iconaccountmenuhomeaddre,
        alertCount: isDisabled
          ? 0
          : kycAlertCount(user_details)?.proofOfResidency,
        writeProtected: user_details?.kyc_status?.L2?.passed_level,
        disabled: isDisabled,
      },
    ],
  },
  {
    title: 'ACCOUNT.MENU_FUNDING_AND_PAYOUTS_TITLE',
    items: [
      {
        to: ACCOUNT_MENU.FUND_PLAN_MENU,
        mainText: 'BANKING.PAGE_TITLE_FUND_YOUR_PLAN',
        icon: ASSET.iconaccountmenufundyourpenionUS,
        dataTestID: UI_TEST_ID.menuItemFunds,
        disabled: isDisabled,
      },
      { isDivider: true },
      {
        to: ACCOUNT_MENU.PAYOUTS_MENU,
        mainText: 'ACCOUNT.MENU_ITEM_PAYOUT_SETUP',
        icon: ASSET.iconaccountmenupayout,
        dataTestID: UI_TEST_ID.menuItemIncome,
        disabled: isDisabled,
      },
    ],
  },
  {
    title: 'ACCOUNT.MENU_REWARDS_GROUP_TITLE',
    items: [
      {
        to: ACCOUNT_MENU.REWARDS,
        mainText: 'REWARDS.INVITE_FRIENDS_PAGE_TITLE',
        icon: ASSET.iconaccountmenuiniteyourfriend,
      },
    ],
  },
  {
    title: 'ACCOUNT.MENU_SETTINGS_SECURITY_TITLE',
    items: [
      {
        to: ACCOUNT_MENU.VIEW_AGREEMENTS,
        mainText: 'ACCOUNT.MENU_ITEM_TERMS_OF_CONDITIONS',
        icon: ASSET.iconaccountcopy,
      },
      { isDivider: true },
      {
        to: ACCOUNT_MENU.SETTINGS,
        mainText: 'ACCOUNT.MENU_SETTINGS',
        icon: ASSET.settingsIcon,
        dataTestID: UI_TEST_ID.menuItemSettings,
      },
    ],
  },
]

/**
 * Renders menu specific to the "Account" section of the app
 */
const AccountMenu = ({ className }: { className?: string }) => {
  //Hooks
  const {
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()

  return (
    <main className={className}>
      <AccountSummary pageTitle={t('ROOT_HEADER_ACCOUNT')} />
      <section className={style.accountMenu}>
        {menuItems(user_details).map((menuGroup, index) => (
          <MenuCard key={index} title={t(menuGroup.title)}>
            {menuGroup.items.map((item, itemIndex) => {
              if ('isDivider' in item) {
                return (
                  <Divider
                    key={`divider-${itemIndex}`}
                    className={style['accountMenu__divider']}
                  />
                )
              }
              return (
                <MenuCardItem
                  key={item.to}
                  to={item.to}
                  mainText={t(item.mainText)}
                  icon={item.icon}
                  alertCount={item.alertCount}
                  writeProtected={item.writeProtected}
                  dataTestID={item.dataTestID}
                  disabled={item?.disabled}
                />
              )
            })}
          </MenuCard>
        ))}

        <LogoutButton
          variant="secondary"
          className={style['accountMenu__auth-button']}
          dataTestID={UI_TEST_ID.logoutButtonDesktop}
        />
      </section>

      {window.matchMedia('(max-width: 900px)').matches && (
        // Do not render on desktop at all
        // causes tests to be flaky with double selectors
        <MobileAppBar
          //Displays a red dot as a notification on an menu bar if the user has
          //not completed KYC
          completedKyc={user_details?.kyc_status?.L2?.passed_level ?? false}
        />
      )}
    </main>
  )
}

export default AccountMenu
