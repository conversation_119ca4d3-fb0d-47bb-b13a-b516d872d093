import { Trans } from 'react-i18next'
import CommonCard from '../../../common/components/card/CommonCard'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/VerificationFeedback.module.scss'
import { UserDetails } from '../types/AuthMachineTypes.type'

const VerificationFeedback = ({
  rejectionReason,
  verificationStatus,
}: {
  rejectionReason?: UserDetails['id_rejection_reason']
  verificationStatus?: UserDetails['id_review_status']
}) => {
  const t = useTranslate()
  const isRejected = verificationStatus === 'rejected'

  const rejectionKeys = {
    possibly_altered: {
      title: 'ID_VERIFICATION.REJECTION.POSSIBLY_ALTERED_ID.TITLE',
      subtitle: 'ID_VERIFICATION.REJECTION.POSSIBLY_ALTERED_ID.SUBTITLE',
    },
    image_not_clear: {
      title: 'ID_VERIFICATION.REJECTION.IMAGE_NOT_CLEAR.TITLE',
      subtitle: 'ID_VERIFICATION.REJECTION.IMAGE_NOT_CLEAR.SUBTITLE',
    },
    user_confirmed_fields_mismatch: {
      title: 'ID_VERIFICATION.REJECTION.FIELDS_MISMATCH.TITLE',
      subtitle: 'ID_VERIFICATION.REJECTION.FIELDS_MISMATCH.SUBTITLE',
    },
  }

  return (
    <div className={style.verificationFeedback}>
      <h2 className={style['verificationFeedback__title']}>
        <Trans
          i18nKey={
            isRejected
              ? 'ID_VERIFICATION.REJECTED.TITLE'
              : 'ID_VERIFICATION.NOT_REVIEWED.TITLE'
          }
        />
      </h2>
      <p className={style['verificationFeedback__subtitle']}>
        <Trans
          i18nKey={
            isRejected
              ? 'ID_VERIFICATION.REJECTED.SUBTITLE'
              : 'ID_VERIFICATION.NOT_REVIEWED.SUBTITLE'
          }
        />
      </p>

      {isRejected && rejectionReason && (
        <CommonCard
          title={t(rejectionKeys[rejectionReason].title)}
          subtitle={t(rejectionKeys[rejectionReason].subtitle)}
          icon={ASSET.idDenial}
          iconSize="x-large"
          className={style['verificationFeedback__card']}
          variant="blue-faint"
        />
      )}
    </div>
  )
}

export default VerificationFeedback
