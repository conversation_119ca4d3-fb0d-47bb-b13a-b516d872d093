import NavigationButtons from '../../../common/components/NavigationButtons'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useRegistration } from '../hooks/useRegistration'
import style from '../style/Register.module.scss'
import { SignUpNavigationButtonsProps } from '../types/SignUpNavigationButtons.types'

/**
 * Renders two navigation buttons, one for going back and one for submitting the form.
 * The go back button is only rendered if `backButtonAction` is provided.
 * The submit button is only enabled if all the required fields are filled in.
 * The component is used in the registration modal.
 */
const ModalSignUpNavigationButtons = ({
  forecastUserData,
  registerButtonLabel,
  forecastPageRegisterModal,
  backButtonAction,
  validatedRegisterInfo,
  handleRegistration,
}: SignUpNavigationButtonsProps) => {
  const { currentState } = useRegistration({ forecastUserData })

  return (
    <NavigationButtons
      hideBackButton={backButtonAction ? false : forecastPageRegisterModal}
      onClickFirst={() => backButtonAction?.()}
      disabledSecond={!validatedRegisterInfo}
      onClickSecond={handleRegistration}
      secondButtonLoading={
        currentState === 'REGISTERING_USER' ||
        currentState === 'SENDING_NEW_TAB_EMAIL'
      }
      secondButtonLabel={registerButtonLabel}
      className={
        style[`register__modal-button${backButtonAction ? '--mt' : ''}`]
      }
      backButtonWhite={Boolean(backButtonAction)}
      showOnMobile={Boolean(backButtonAction)}
      trackActivityBackButton={{
        trackId: 'register_back',
      }}
      trackActivityActionButton={{
        trackId: 'register_submit_signup',
      }}
      disabledFixedOnMobile
      dataTestIDSecondBtn={UI_TEST_ID.registerButton}
    />
  )
}

export default ModalSignUpNavigationButtons
