import { useEffect, useState } from 'react'
import { Trans } from 'react-i18next'
import Icon from '../../../common/components/Icon'
import Modal from '../../../common/components/Modal'
import { useBrowserPermission } from '../../../common/hooks/useBrowserPermission'
import { checkForBrowserPermissionStatus } from '../../../common/utils/UtilFunctions'
import style from '../style/PermissionModal.module.scss'
import { PermissionModalProps } from '../types/PermissionModal.type'

/**
 * Handles permission request and displays an explainer modal to what browser
 * permission he user needs to give access to
 */
const PermissionModal = ({
  title,
  content,
  icon,
  onPermissionGranted,
  onPermissionDenied,
  permission,
  askOnMount,
}: PermissionModalProps) => {
  const [modalIsOpen, setModalIsOpen] = useState(false)

  useEffect(() => {
    checkForBrowserPermissionStatus('camera' as PermissionName)
      .then((browserPermission) => {
        if (browserPermission.state === 'prompt') {
          setModalIsOpen(true)
        }
      })
      .catch((error) => {
        // TODO: Can't handle this, needs to be additional modal
        // To tell the user how to unblock their browser permission
        // https://3.basecamp.com/5235135/buckets/25328289/chats/4433098432@7115099427
        console.error(error)
      })
  }, [])

  useBrowserPermission({
    permission: permission,
    askOnMount: askOnMount,
    onSuccess: () => {
      setModalIsOpen(false)
      onPermissionGranted()
    },
    onFailure: (error) => {
      setModalIsOpen(false)
      onPermissionDenied(error)
    },
  })

  return (
    <Modal
      customStyle={style}
      backdrop
      isOpen={modalIsOpen}
      className={style.permissionModal}
    >
      <Icon fileName={icon} className={style['permissionModal__icon']} />

      <div className={style['permissionModal__text-wrapper']}>
        {title && (
          <p className={style['permissionModal__title']}>
            <Trans i18nKey={title} values={title} />
          </p>
        )}
        {content && (
          <p className={style['permissionModal__content']}>
            <Trans i18nKey={content} values={content} />
          </p>
        )}
      </div>
    </Modal>
  )
}

export default PermissionModal
