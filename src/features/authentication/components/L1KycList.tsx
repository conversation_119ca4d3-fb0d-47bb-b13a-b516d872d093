import { useState } from 'react'
import CommonCard from '../../../common/components/card/CommonCard'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { idVerificationAlertStatus } from '../../../common/utils/UtilFunctions'
import { PERSONAL_DETAILS } from '../../../routes/Route'
import { isDisabled } from '../../DisabledLaunchFeatures'
import { useAccountService } from '../hooks/useAccountService'
import FaceScan from '../pages/FaceScan'
import { idVerificationStatusText } from '../utils/UtilsFunctions'

const idVerificationEnabledStatuses = ['rejected']

/**
 * Renders a list of CTA cards for L1 KYC, and shows the user their L1 KYC
 * Status
 */
const L1KycList = () => {
  const t = useTranslate()
  const { context } = useAccountService()
  const {
    kyc_status: {
      L1: {
        requirements: { face_scanned, phone_verified },
      },
    },
  } = context.user_details || { kyc_status: { L1: { requirements: {} } } }

  const [faceScan, setFaceScan] = useState<boolean>(false)
  const showFaceScan = () => setFaceScan(true)
  const shouldIdvEnabled = idVerificationEnabledStatuses.includes(
    context.user_details?.id_review_status ?? 'rejected'
  )

  return (
    <>
      {faceScan && (
        <FaceScan
          scanType="match-id"
          asModal
          onClickExitScan={() => setFaceScan(false)}
        />
      )}

      <CommonCard
        title={t('KYC.FACE_ENROLLED')}
        onClick={face_scanned ? undefined : showFaceScan}
        disabled={isDisabled}
        cardInfoProps={{
          cardArrowProps: { arrowInvisible: face_scanned },
          cardAlertProps: {
            alert: face_scanned ? 'completed' : 'warn',
          },
        }}
        variant="gray-dirty"
      />
      <CommonCard
        title={idVerificationStatusText(context.user_details?.id_review_status)}
        onClick={shouldIdvEnabled ? showFaceScan : undefined}
        disabled={isDisabled}
        cardInfoProps={{
          cardArrowProps: { arrowInvisible: !shouldIdvEnabled },
          cardAlertProps: {
            alert: idVerificationAlertStatus(
              context.user_details?.id_review_status
            ),
          },
        }}
        variant="gray-dirty"
      />
      <CommonCard
        title={t('PHONE_NUMBER_INPUT_LABEL')}
        cardInfoProps={{
          cardAlertProps: { alert: phone_verified ? 'completed' : 'warn' },
          cardArrowProps: { arrowInvisible: phone_verified },
        }}
        variant="gray-dirty"
        href={phone_verified ? undefined : PERSONAL_DETAILS.PHONE_NUMBER}
      />
    </>
  )
}

export default L1KycList
