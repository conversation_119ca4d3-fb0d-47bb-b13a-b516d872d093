import { useRef, useState } from 'react'
import { TESTING_IDS } from '../../../../cypress/support/ui-component-ids'
import TextError from '../../../common/components/TextError'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ErrorStorage } from '../../CommonState.type'
import { usePinConfirmation } from '../hooks/usePinConfirmation'
import style from '../style/PinSetup.module.scss'
import { PinConfirmationProps } from '../types/Pin.types'
import { AUTH_CONSTANTS } from '../utils/consts'
import { validatePin } from '../utils/UtilsFunctions'
import PinInput from './PinInput'
import PinTitle from './PinTitle'

/**
 * PinConfirmation renders two pin input fields, one for the initial PIN and one for confirmation.
 * It takes care of the focus management and the error handling.
 */
const PinConfirmation = ({
  autoFocus = true,
  headerTitle,
  confirmPinLabel,
  initialPinLabel,
  pinLength = AUTH_CONSTANTS.PIN_INPUT_FIELDS,
  externalInputRefs,
  focusOnSuccess = true,
  handleSubmit,
  errorCallback,
}: PinConfirmationProps) => {
  const t = useTranslate()
  const [initialPin, setInitialPin] = useState<Array<string>>([])
  const [confirmPin, setConfirmPin] = useState<Array<string>>([])
  const [error, setError] = useState<ErrorStorage>()

  const initialInputsRef = useRef<Array<HTMLInputElement>>([])
  const confirmInputsRef = useRef<Array<HTMLInputElement>>([])

  const refs = externalInputRefs?.current?.[0] ?? initialInputsRef?.current?.[0]

  const { onPinsMatching } = usePinConfirmation({
    initialPin,
    onError: (error) => {
      setError(error)

      errorCallback?.()
      setInitialPin([])
      setConfirmPin([])

      refs?.focus()
    },
    onSubmit: {
      handleSubmit,
      successCallback: () => {
        setInitialPin([])
        setConfirmPin([])
        if (focusOnSuccess) {
          refs?.focus()
        }
      },
    },
  })

  const handleInitialPinChange = (pin: Array<string>) => {
    setError(undefined)
    setInitialPin(pin)

    const pinValid = validatePin(pin, pinLength)
    if (pinValid) {
      confirmInputsRef?.current[0]?.focus()
    }
  }

  const handleConfirmPinChange = (pin: Array<string>) => {
    setConfirmPin(pin)

    const pinValid = validatePin(pin, pinLength)
    if (pinValid) {
      onPinsMatching(pin)
    }
  }

  return (
    <section className={style['pin-setup']}>
      {headerTitle && (
        <PinTitle
          headerTitle={t(headerTitle)}
          className={style[`pin-setup-page__header`]}
        />
      )}

      <PinInput
        label={initialPinLabel}
        pinLength={pinLength}
        values={initialPin}
        onChange={handleInitialPinChange}
        errorMessage={error?.translatedError}
        inputRefs={externalInputRefs ?? initialInputsRef}
        autoFocus={autoFocus}
      />
      <PinInput
        label={confirmPinLabel}
        pinLength={pinLength}
        values={confirmPin}
        onChange={handleConfirmPinChange}
        errorMessage={error?.translatedError}
        autoFocus={false}
        inputRefs={confirmInputsRef}
      >
        {error && (
          <TextError
            dataTestID={TESTING_IDS.pinConfirmationError}
            position="relative"
            errorText={error?.translatedError}
          />
        )}
      </PinInput>
    </section>
  )
}

export default PinConfirmation
