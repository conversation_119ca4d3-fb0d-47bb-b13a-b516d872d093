import Dropdown from '../../../common/components/Dropdown'
import countriesLocale from '../../../common/constants/countries-locales.json'
import {
  getCountryInformation,
  getCountrySvgIcon,
} from '../../../common/utils/UtilFunctions'

/**
 * Uses `countries-locales.json` to render international dial codes with flag icons. The value is
 * the alpha3 code of the country and label is the dial code
 */
const DialCodeDropdown = ({
  value,
  onChange,
  className,
  readOnly,
  label,
}: {
  value: string
  onChange: (value?: string) => void
  className?: string
  readOnly?: boolean
  label?: string
}) => {
  const handleOnChange = (value: unknown) => {
    onChange(getCountryInformation('alpha3', value as string)?.dial_code)
  }

  return (
    <Dropdown
      label={label}
      options={countriesLocale.map((country) => ({
        ...country,
        icon: getCountrySvgIcon(country.alpha2),
      }))}
      // the value is dial code, we need to translate that dial code to a
      // country code alpha3
      value={getCountryInformation('dial_code', value)?.alpha3}
      onChange={handleOnChange}
      itemKey={{ displayKey: 'dial_code', valueOnChange: 'alpha3' }}
      searchBy={['name', 'alpha3', 'dial_code']}
      className={className}
      readOnly={readOnly}
    />
  )
}

export default DialCodeDropdown
