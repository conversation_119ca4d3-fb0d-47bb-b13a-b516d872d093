import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import style from '../style/Register.module.scss'
import { RegisterFormProps } from '../types/RegisterForm.types'
import EmailConfirmationModal from './EmailConfirmationModal'

/**
 * A top-level component for rendering the registration form.
 *
 * This component is responsible for rendering the email confirmation
 * modal and error boundary and suspense for the registration form.
 *
 * It also uses the useRegistration hook to manage the state of the
 * registration process and send the verification email.
 */
const RegisterForm = ({
  currentState,
  resendEmail,
  modalTextContent,
  dismissModal,
  sendEmailAgain,
  children,
}: RegisterFormProps) => {
  const isRegisteredAndSentEmail = () =>
    currentState === 'SENT_NEW_TAB_EMAIL' ||
    currentState === 'READY_RESEND_EMAIL' ||
    currentState === 'SENDING_NEW_TAB_EMAIL'

  return (
    // &Important!
    // In order for the class to be present in the style object
    // there needs to be at least one css value, otherwise the
    // style object won't contain that class
    <main className={style.register}>
      {isRegisteredAndSentEmail() && (
        <EmailConfirmationModal
          email={resendEmail}
          state={currentState}
          modalTextContent={modalTextContent}
          isOpen
          onDismiss={dismissModal}
          onResend={sendEmailAgain}
        />
      )}
      <ErrorBoundaryAndSuspense>{children}</ErrorBoundaryAndSuspense>
    </main>
  )
}

export default RegisterForm
