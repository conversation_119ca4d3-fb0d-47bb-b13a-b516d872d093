import CommonCard from '../../../common/components/card/CommonCard'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { isDisabled } from '../../DisabledLaunchFeatures'
import style from '../style/PersonalDetails.module.scss'
import { IDVerificationCardProps } from '../types/IDVerificationCard.types'
import { idVerificationStatusText } from '../utils/UtilsFunctions'

const IDVerificationCard = ({ status, onClick }: IDVerificationCardProps) => {
  const t = useTranslate()

  return (
    <div className={style['personalDetails__id-verify']}>
      <CommonCard
        title={t('ID_VERIFY_TITLE')}
        subtitle={idVerificationStatusText(status)}
        icon={ASSET.iconaccountiderify}
        iconSize="large"
        variant="gray-dirty"
        onClick={status !== 'approved' ? onClick : undefined}
        disabled={isDisabled}
        cardInfoProps={{
          showArrow: !!onClick,
          cardAlertProps: {
            alert: status ?? 'warn',
          },
        }}
      />
    </div>
  )
}

export default IDVerificationCard
