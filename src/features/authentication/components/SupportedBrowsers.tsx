import BrowsersIcons from '../../../common/components/BrowsersIcons'
import Icon from '../../../common/components/Icon'
import StoreIcons from '../../../common/components/StoreIcons'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/SupportedBrowsers.module.scss'

/**
 * This component renders a modal that provides information about supported browsers
 * for FaceTec authentication. It includes a title, a description, and icons for
 * supported browsers and app stores.
 */
const SupportedBrowsers = ({
  setRenderunsupBrowserModal,
}: {
  setRenderunsupBrowserModal: (value: boolean) => void
}) => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()

  return (
    <div className={style['supported-browsers__container']}>
      <Icon
        onClick={() => setRenderunsupBrowserModal(false)}
        className={style['supported-browsers__back-btn']}
        fileName={ASSET.chevronBlueLeft}
      />
      <Icon
        fileName={ASSET.logoTontineTrust}
        className={style['supported-browsers__logo']}
      />

      <h1 className={style['supported-browsers__title']}>
        {t('LOGIN_FORM.FACETEC_UNSUPPORTED_BROWSER_TITLE')}
      </h1>
      <p className={style['supported-browsers__text']}>
        {t('LOGIN_FORM.FACETEC_UNSUPPORTED_BROWSER_QR_TEXT')}
      </p>
      {isMobileOrTablet ? (
        <StoreIcons />
      ) : (
        <div className={style['supported-browsers__qr-mockup']} />
      )}

      <p className={style['supported-browsers__text']}>
        {t('LOGIN_FORM.FACETEC_UNSUPPORTED_BROWSER_RECOMMENDED_TEXT')}
      </p>
      <BrowsersIcons />
    </div>
  )
}

export default SupportedBrowsers
