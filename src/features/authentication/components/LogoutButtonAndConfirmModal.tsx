import { useState } from 'react'
import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../hooks/useAccountService'
import { LogoutButtonProps } from '../types/AuthButton.types'

/**
 * A button component that opens a confirmation modal when clicked, then logs out the user
 * when the confirm button is clicked. The modal is closed when the cancel button is clicked.
 */
const LogoutButtonAndConfirmModal = ({
  variant,
  className,
  icon,
  dataTestID = UI_TEST_ID.logoutButtonDesktop,
}: LogoutButtonProps) => {
  const t = useTranslate()
  const { send } = useAccountService()

  const [isModalOpen, setIsModalOpen] = useState(false)

  return (
    <>
      <ConfirmationModal
        isOpen={isModalOpen}
        title={t('LOGOUT_MODAL_TITLE')}
        content={t('LOGOUT_MODAL_CONTENT')}
      >
        <Button
          dataTestID={UI_TEST_ID.modalLogoutButton}
          onClick={() => send({ type: 'DELETE_AUTH_TOKEN' })}
        >
          {t('LOGOUT_MODAL.CONFIRM_BUTTON')}
        </Button>

        <Button onClick={() => setIsModalOpen(false)} variant="alternative">
          {t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
        </Button>
      </ConfirmationModal>
      <Button
        variant={variant}
        onClick={() => setIsModalOpen(true)}
        className={className}
        icon={icon}
        dataTestID={dataTestID}
      >
        {t('BUTTON_LABEL.LOGOUT')}
      </Button>
    </>
  )
}

export default LogoutButtonAndConfirmModal
