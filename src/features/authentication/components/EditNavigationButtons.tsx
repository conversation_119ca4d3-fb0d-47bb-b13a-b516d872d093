import NavigationButtons from '../../../common/components/NavigationButtons'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/PersonalDetails.module.scss'
import { EditNavigationButtonsProps } from '../types/EditNavigationButtons.types'

const EditNavigationButtons = ({
  loading,
  inputValidation: { firstNameValidated, lastNameValidated },
  userIsTyping,
  onSave,
}: EditNavigationButtonsProps) => {
  const t = useTranslate()

  const fieldsValid = [firstNameValidated, lastNameValidated].every(
    (field) => field === undefined || field.valid
  )

  return (
    <NavigationButtons
      hideBackButton
      firstButtonLabel={t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
      secondButtonLabel={t('SAVE_BUTTON_LABEL')}
      secondButtonLoading={loading}
      textOnLoading={t('LOADING_TEXT')}
      dataTestIDSecondBtn={UI_TEST_ID.editDetailsButton}
      disabledSecond={!(fieldsValid && userIsTyping)}
      onClickSecond={onSave}
      className={style['personalDetails__nav-btns']}
      disabledFixedOnMobile
    />
  )
}

export default EditNavigationButtons
