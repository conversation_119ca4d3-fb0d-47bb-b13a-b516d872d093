import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { DiscardChangesModalProps } from '../types/DiscardChangesModal.types'

const DiscardChangesModal = ({
  isOpen,
  onClose,
  onSave,
}: DiscardChangesModalProps) => {
  const t = useTranslate()

  return (
    <ConfirmationModal
      isOpen={isOpen}
      icon={ASSET.yellowQuestionMark}
      title={t('DISCARD_MODAL.MODAL_DISCARD_TITLE')}
      content={t('PROMPT_CONTENT.EDIT_PERSONAL_DETAILS')}
    >
      <Button onClick={onSave}>{t('SAVE_BUTTON_LABEL')}</Button>
      <Button variant="alternative" onClick={onClose}>
        {t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
      </Button>
    </ConfirmationModal>
  )
}

export default DiscardChangesModal
