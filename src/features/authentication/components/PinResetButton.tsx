import Button from '../../../common/components/Button'
import { ANIMATION } from '../../../common/constants/Animations'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ButtonProps } from '../../../common/types/Button.types'
import { usePinResetEmail } from '../hooks/usePinResetEmail'
import style from '../style/PinSetupPage.module.scss'

/**
 * A button to send a pin reset link to the user's email. The button is disabled
 * while the reset link is being sent.
 */
const PinResetButton = ({
  className,
  textOnLoading,
  ...buttonProps
}: ButtonProps) => {
  const t = useTranslate()

  const { sendPinResetLinkViaEmail, isLoading } = usePinResetEmail()
  return (
    <Button
      {...buttonProps}
      loadingAnimation={ANIMATION.loadingLightBlueDots}
      onClick={sendPinResetLinkViaEmail}
      className={className ?? style['pin-setup-page__forgot-pin-button']}
      loading={isLoading}
      textOnLoading={textOnLoading ?? ''}
      dataTestID={UI_TEST_ID.forgotPinButton}
      variant="text-only"
    >
      {t('FORGOT_PIN_BTN_LABEL')}
    </Button>
  )
}

export default PinResetButton
