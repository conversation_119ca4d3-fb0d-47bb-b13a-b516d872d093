import ExtendedContentCard from '../../../common/components/card/ExtendedContentCard'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { formatFileSize } from '../../../common/utils/UtilFunctions'
import ProgressBar from '../../agreements/components/ProgressBar'
import style from '../style/UploadedFilesCard.module.scss'
import { UploadedFilesCardProps } from '../types/UploadedFilesCard.types'

/**
 * Renders an array of uploaded files  with file name and size, as well as a
 * upload progress bar
 */
const UploadedFilesCard = ({
  files,
  onRemove,
  progressMax,
  progressValue,
  hideProgressBar,
  children,
}: UploadedFilesCardProps) => {
  const t = useTranslate()
  if (!files || files.length === 0) return children
  const isUploading = progressValue >= 1 && progressValue < 100

  return files.map((file) => (
    <ExtendedContentCard
      key={file.size}
      title={file.name}
      icon={ASSET.fileIcon}
      iconSize="large"
      autoExpand
      variant="gray-dirty"
      alert="cancelled"
      showArrow={false}
      onClick={onRemove}
      subtitle={isUploading ? t('LOADING_TEXT') : formatFileSize(file.size)}
    >
      <div className={style['uploaded-files-card__container']}>
        {!hideProgressBar && (
          <ProgressBar
            max={progressMax}
            value={progressValue}
            expanded
            variant="blue"
          />
        )}
      </div>
    </ExtendedContentCard>
  ))
}

export default UploadedFilesCard
