import TextError from '../../../common/components/TextError'
import { useTranslate } from '../../../common/hooks/useTranslate'
import pinTitleStyle from '../style/PinSetup.module.scss'
import style from '../style/PinSetupPage.module.scss'
import { PinChangeProps } from '../types/Pin.types'
import PinInput from './PinInput'
import PinResetButton from './PinResetButton'
import PinTitle from './PinTitle'

/**
 * PinChange is a component that is used to handle the PIN change workflow.
 * It includes a PIN input field with a label and an error message if the PIN is invalid.
 * If the user forgets their PIN, they can send a reset email to themselves.
 * The component also handles the loading state while sending the email.
 */
const PinChange = ({
  label,
  pinLength,
  pin,
  error,
  pinChangeRefs,
  onChange,
}: PinChangeProps) => {
  const t = useTranslate()

  return (
    <article className={`${style['pin-setup-page__container']}`}>
      <PinTitle
        headerTitle={t('PIN_CHANGE_AUTHORIZE')}
        className={pinTitleStyle[`pin-setup-page__header`]}
      />
      <PinInput
        label={label}
        pinLength={pinLength}
        values={pin}
        onChange={(values) => onChange(values)}
        errorMessage={error?.translatedError}
        inputRefs={pinChangeRefs}
      >
        {error && (
          <TextError errorText={error?.translatedError} position="relative" />
        )}
      </PinInput>
      <PinResetButton />
    </article>
  )
}

export default PinChange
