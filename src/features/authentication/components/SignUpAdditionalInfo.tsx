import CountryDropdown from '../../../common/components/CountryDropdown'
import RangeSlider from '../../../common/components/RangeSlider'
import SelectSex from '../../../common/components/SelectSex'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { generateRange } from '../../../common/utils/UtilFunctions'
import style from '../style/Register.module.scss'
import { SignUpAdditionalInfoProps } from '../types/SignUpAdditionalInfo.types'
import { AUTH_CONSTANTS } from '../utils/consts'

/**
 * AdditionalUserInfo component renders a form that asks for a user's
 * additional information such as sex, age and country.
 */
const SignUpAdditionalInfo = ({
  sex,
  setSex,
  currentAge,
  setCurrentAge,
  country,
  handleCountry,
}: SignUpAdditionalInfoProps) => {
  const t = useTranslate()

  const {
    supportedCountry: { tontinatorParams: supportedCountryParams },
  } = useSupportedCountries({
    alpha3CountryCode: country,
  })

  return (
    <>
      <SelectSex
        sex={sex ?? 'Male'}
        setSex={setSex}
        label={t('SEX_FORM.SELECT_SEX_LABEL')}
      />
      <CountryDropdown
        value={country ?? ''}
        onChange={handleCountry}
        label={t('INPUT_LABEL.COUNTRY')}
        className={style['register__country-dropdown']}
      />
      <div className={style['register__age-slider']}>
        <RangeSlider
          value={currentAge ?? 0}
          onChange={setCurrentAge}
          steps={generateRange(
            AUTH_CONSTANTS.CURRENT_AGE_MIN,
            supportedCountryParams?.maxCurrentAge?.age ??
              AUTH_CONSTANTS.CURRENT_AGE_MIN
          )}
          label={t('INPUT_LABEL.CURRENT_AGE')}
        />
      </div>
    </>
  )
}

export default SignUpAdditionalInfo
