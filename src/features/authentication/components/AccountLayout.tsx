import { ReactNode } from 'react'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import style from '../style/AccountLayout.module.scss'
import AccountMenu from './AccountMenu'

/**
 * Account section layout for all the account pages, the account
 * menu is rendered on the left side and the pages are on the right side,
 * passing in `noMenu` does not render the account menu
 */
const AccountLayout = ({ children }: { children: ReactNode }) => {
  return (
    <main className={style['account-layout']}>
      <section className={style[`account-layout__container`]}>
        <AccountMenu className={style[`account-layout__left`]} />
        <section className={style['account-layout__right']}>
          <ErrorBoundaryAndSuspense hideNavButton>
            {children}
          </ErrorBoundaryAndSuspense>
        </section>
      </section>
    </main>
  )
}

export default AccountLayout
