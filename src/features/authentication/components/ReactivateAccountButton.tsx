import { toast } from 'react-toastify'
import Button from '../../../common/components/Button'
import ToastMessage from '../../../common/components/ToastMessage'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../hooks/useAccountService'
import type { ReactivateAccountButtonProps } from './ReactivateAccountButton.types'

const ReactivateAccountButton = ({
  reactivateAccountButtonLabel,
  onAccountReactivated,
  dataTestID,
}: ReactivateAccountButtonProps) => {
  //Hooks
  const t = useTranslate()
  const { send } = useAccountService()

  const onSuccessReactivatingAccount = () => {
    //Callback when the account is reactivated
    onAccountReactivated ? onAccountReactivated(true) : null

    //Display success toast message
    toast.success(
      <ToastMessage title={t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')} />
    )
  }

  const onFailureReactivatingAccount = () => {
    toast.error(
      <ToastMessage title={t('CLOSE_ACCOUNT.FAILED_REOPENING_ACCOUNT_TITLE')} />
    )
  }

  /**
   * Sends an event to the auth machine to reactivate the account
   */
  const reactivateAccount = () => {
    send({
      type: 'CANCEL_CLOSING_ACCOUNT',
      payload: {
        successCallback: onSuccessReactivatingAccount,
        failureCallback: onFailureReactivatingAccount,
      },
    })
  }

  return (
    <Button
      variant="alternative"
      onClick={reactivateAccount}
      dataTestID={dataTestID}
    >
      {reactivateAccountButtonLabel}
    </Button>
  )
}

export default ReactivateAccountButton
