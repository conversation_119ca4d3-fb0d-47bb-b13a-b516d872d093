import Layout from '../../../common/components/Layout'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import RegisterForm from '../components/RegisterForm'
import SignUpFields from '../components/SignUpFields'
import { useRegistration } from '../hooks/useRegistration'
import { StateType } from '../types/AuthMachineTypes.type'
import { RegisterPageProps } from '../types/RegisterPage.types'

/**
 * Component for rendering the registration form in a full site layout and
 * is used when users land directly on the registration page
 */
const RegisterPage = ({ forecastUserData }: RegisterPageProps) => {
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()
  const {
    currentState,
    resendEmail,
    modalTextContent,
    dismissModal,
    sendEmail<PERSON>gain,
    signUpError,
    handleRegister,
  } = useRegistration({ forecastUserData })

  return (
    <RegisterForm
      currentState={currentState as StateType}
      resendEmail={resendEmail}
      sendEmailAgain={sendEmailAgain}
      modalTextContent={modalTextContent}
      dismissModal={dismissModal}
    >
      <Layout
        pageTitle={t('REGISTER_FORM.NO_MODAL_HEADER_TITLE')}
        headerTitle={
          isMobileOrTablet ? '' : t('REGISTER_FORM.NO_MODAL_HEADER_TITLE')
        }
        navigateTo={PUBLIC.GO_TO_PREV_PAGE}
        layoutVariant="sun-bg"
        containerWidth="medium"
        headerTextColor="blue"
        headerVariant="spaced"
        containerHeight="auto"
        containerMt="nomt"
        hideDividerHeader
      >
        <SignUpFields
          forecastUserData={forecastUserData}
          registerButtonLabel={t('BUTTON_LABEL.SIGN_UP')}
          handleRegister={handleRegister}
          signUpError={signUpError}
        />
      </Layout>
    </RegisterForm>
  )
}

export default RegisterPage
