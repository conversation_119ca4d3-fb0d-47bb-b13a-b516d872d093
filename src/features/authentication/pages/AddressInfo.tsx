import { Trans } from 'react-i18next'
import CommonCard from '../../../common/components/card/CommonCard'
import InputGroup from '../../../common/components/InputGroup'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PRIVATE } from '../../../routes/Route'
import AddressFields from '../components/AddressFields'
import RejectedDocument from '../components/RejectedDocument'
import { useAccountService } from '../hooks/useAccountService'
import { useAddressInfo } from '../hooks/useAddressInfo'
import idVerifyStyle from '../style/PersonalDetails.module.scss'
import { flowIsFinishedOnStatues } from '../utils/consts'
import { deniedAddressReasonType, statusToKey } from '../utils/UtilsFunctions'

/**
 * Renders input fields for address verification, if an address is rejected then
 * renders the reason for the rejection
 */
const AddressInfo = ({ setPage }: { setPage: () => void }) => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()

  const {
    context: { user_details },
  } = useAccountService()
  const addressVerificationStatus = user_details?.user_address?.status

  const isFlowFinished = flowIsFinishedOnStatues.includes(
    addressVerificationStatus ?? ''
  )

  const {
    addressValidators,
    userHasTypedData,
    userCountry,
    setCountry,
    userStreetAddress,
    setUserStreetAddress,
    userStreetAddressTwo,
    setUserStreetAddressTwo,
    userCity,
    setUserCity,
    userState,
    setUserState,
    userPostalCode,
    setUserPostalCode,
    submitInfo,
  } = useAddressInfo()

  const { key, alert } = statusToKey(addressVerificationStatus)

  return (
    <Layout
      navigateTo={isFlowFinished ? PRIVATE.ACCOUNT : undefined}
      onClickAction={isFlowFinished ? undefined : () => setPage()}
      pageTitle={t('ACCOUNT.MENU_ITEM_RESIDENCY_VERIFICATION')}
      containerHeight={isMobileOrTablet ? 'lh' : 'sh'}
      containerMt="mt-20"
      card={
        <div className={idVerifyStyle['personalDetails__id-verify']}>
          <CommonCard
            title={t('ADDRESS_VERIFICATION.CARD_TITLE')}
            subtitle={t(key)}
            icon={ASSET.iconaccountaddreUS}
            iconSize="large"
            variant="gray-dirty"
            cardInfoProps={{
              showArrow: !isFlowFinished,
              cardAlertProps: {
                alert,
              },
            }}
            onClick={isFlowFinished ? undefined : () => setPage()}
          />
        </div>
      }
      bottomSection={
        !isFlowFinished ? (
          <NavigationButtons
            onClickSecond={submitInfo}
            secondButtonLabel={
              addressVerificationStatus === 'DENIED'
                ? t('COMMON.TRY_AGAIN_BUTTON')
                : t('CHOOSE_CURRENCY.CONFIRM_BUTTON')
            }
            onClickFirst={setPage}
            disabledSecond={
              !(addressValidators?.allAddressFieldsValid && userHasTypedData) ||
              isFlowFinished
            }
          />
        ) : null
      }
    >
      {addressVerificationStatus === 'DENIED' ? (
        <RejectedDocument
          genericExplanation={
            <Trans i18nKey={'GENERIC_REJECTION_RESIDENCY_DOC'} />
          }
          rejectionReason={
            <Trans
              i18nKey={'ADDRESS_VERIFICATION.DENIED_REASON'}
              values={{
                reasonType: t(
                  deniedAddressReasonType(
                    user_details?.user_address?.user_submitted_address
                      ?.reason_type ?? 'OTHER_REASON'
                  )
                ),
              }}
            />
          }
          rejectionExplanation={
            user_details?.user_address?.user_submitted_address?.reason
          }
        />
      ) : (
        <InputGroup>
          <AddressFields
            {...addressValidators}
            onStreetAddressChange={setUserStreetAddress}
            onStreetAddressTwoChange={setUserStreetAddressTwo}
            onCityChange={setUserCity}
            onStateChange={setUserState}
            onPostalCodeChange={setUserPostalCode}
            onCountryChange={setCountry}
            streetAddress={userStreetAddress}
            streetAddressTwo={userStreetAddressTwo}
            city={userCity}
            state={userState ?? ''}
            postalCode={userPostalCode ?? ''}
            country={userCountry}
            readOnly={isFlowFinished}
          />
        </InputGroup>
      )}
    </Layout>
  )
}

export default AddressInfo
