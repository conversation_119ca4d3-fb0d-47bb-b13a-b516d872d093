import { useTranslate } from '../../../common/hooks/useTranslate'
import { KycProtected } from '../../../routes/Route.type'
import L1KycList from '../components/L1KycList'
import L2KycList from '../components/L2KycList'
import style from '../style/MissingInformation.module.scss'

/**
 * Renders missing KYC information
 */
const MissingInformation = ({ requestKyc }: { requestKyc: KycProtected }) => {
  const t = useTranslate()

  return (
    <section className={style.missingInformation}>
      <div className={style['missingInformation__container']}>
        <p className={style['missingInformation__explainer']}>
          {t('MISSING_DETAILS.EXPLAINER_TEXT')}
        </p>
      </div>

      {(requestKyc.l1 || requestKyc.l2) && <L1KycList />}
      {requestKyc.l2 && <L2KycList />}
    </section>
  )
}

export default MissingInformation
