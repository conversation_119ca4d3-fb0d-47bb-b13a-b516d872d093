import { lazy, useState } from 'react'
import ErrorTextCountdown from '../../../common/components/ErrorTextCountdown'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import TimerButton from '../../../common/components/TimerButton'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { ACCOUNT_MENU } from '../../../routes/Route'
import { useUpdatePhoneController } from '../hooks/useUpdatePhoneController'
import { useUserInfoValidation } from '../hooks/useUserInfoValidation'

const PhoneNumberAndOtp = lazy(() => import('../components/PhoneNumberAndOtp'))

/**
 * Renders the phone number verification flow
 */
const UpdatePhoneNumber = () => {
  const navigate = useCustomNavigation()
  const {
    t,
    addPhoneNumber,
    setUnverifiedPhoneNumber,
    onSuccessfulVerification,
    isLoading,
    apiError,
    setApiError,
    unverifiedPhoneNumber,
  } = useUpdatePhoneController()
  const [resendSms, setResendSms] = useState(true)
  const [smsSent, setSmsSent] = useState(false)

  const { phoneNumberValidated, validatePhoneNumber } = useUserInfoValidation()

  return (
    <Layout
      pageTitle={t('PHONE_NUMBER_INPUT_LABEL')}
      navigateTo={ACCOUNT_MENU.PERSONAL_DETAILS}
      containerWidth="small"
      containerHeight="sh"
      bottomSection={
        <NavigationButtons
          onClickSecond={() =>
            addPhoneNumber({
              onSuccess: () => setSmsSent(true),
            })
          }
          secondButtonLoading={isLoading}
          hideActionButton={smsSent}
          disabledSecond={Boolean(apiError || !phoneNumberValidated?.valid)}
          onClickFirst={() => {
            if (smsSent) {
              setUnverifiedPhoneNumber(undefined)
              // default state
              setResendSms(true)
              setSmsSent(false)
              validatePhoneNumber(undefined)
              return
            }
            navigate(ACCOUNT_MENU.PERSONAL_DETAILS)
          }}
          secondButtonLabel={t('COMMON.CONTINUE_BUTTON')}
          customButton={
            smsSent ? (
              <TimerButton
                disabled={resendSms}
                minutes={1}
                variant="alternative"
                onClick={() => {
                  addPhoneNumber({
                    resend: true,
                    onSuccess: () => {
                      setSmsSent(true)
                      setResendSms(true)
                    },
                  })
                }}
                onCountdownFinished={() => setResendSms(false)}
              >
                {t('MOBILE_RESEND')}
              </TimerButton>
            ) : null
          }
        />
      }
    >
      <PhoneNumberAndOtp
        onPhoneNumberVerified={onSuccessfulVerification}
        unverifiedPhoneNum={unverifiedPhoneNumber}
        onChange={setUnverifiedPhoneNumber}
        phoneNumberValidated={phoneNumberValidated}
        validatePhoneNumber={validatePhoneNumber}
        smsSent={smsSent}
      />
      {apiError && (
        <ErrorTextCountdown
          i18nKey={apiError?.translatedError}
          onCountDownFinished={() => setApiError(undefined)}
          secondsToCountFrom={apiError?.data?.expires_in_seconds}
        />
      )}
    </Layout>
  )
}

export default UpdatePhoneNumber
