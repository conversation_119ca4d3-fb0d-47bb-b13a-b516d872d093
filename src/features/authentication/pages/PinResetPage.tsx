import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import Layout from '../../../common/components/Layout'
import { ANIMATION } from '../../../common/constants/Animations'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import PinConfirmation from '../components/PinConfirmation'
import { usePinReset } from '../hooks/usePinReset'
import style from '../style/PinSetupPage.module.scss'

/**
 * Renders a pin reset page with error or success modal depending on the state
 * of the pin verification
 */
const PinResetPage = () => {
  const t = useTranslate()
  const { error, shouldRenderSuccessModal, handleResetPin, onFailedPinReset } =
    usePinReset()

  return (
    <Layout pageTitle={t('PIN_RESET.PAGE_TITLE')} hideDividerHeader>
      <ConfirmationModal
        isOpen={Boolean(error)}
        icon={ASSET.infoamber}
        title={t('PIN_RESET.LINK_EXPIRED_TITLE')}
        content={t('PIN_RESET.LINK_EXPIRED_CONTENT')}
      />
      <ConfirmationModal
        isOpen={shouldRenderSuccessModal}
        animatedIcon={ANIMATION.checkmark}
        title={t('PIN_RESET.SUCCESS_MODAL_TITLE')}
        content={t('PIN_RESET.SUCCESS_MODAL_CONTENT')}
      >
        <Button onClick={() => window?.close()}>
          {t('THANK_FORM.DISMISS_BUTTON')}
        </Button>
      </ConfirmationModal>

      <article className={style['pin-setup-page']}>
        <PinConfirmation
          headerTitle={t('PIN_RESET.HEADER_TITLE')}
          autoFocus={false}
          handleSubmit={handleResetPin}
          errorCallback={onFailedPinReset}
        />
      </article>
    </Layout>
  )
}

export default PinResetPage
