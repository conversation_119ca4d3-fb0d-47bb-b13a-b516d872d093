import { useState } from 'react'
import { useAccountService } from '../hooks/useAccountService'
import { flowIsFinishedOnStatues } from '../utils/consts'
import AddressInfo from './AddressInfo'
import AddressUpload from './AddressUpload'

/**
 * Handles the address verification flow, renders the address upload page or
 * address info page depending on the user's address verification status
 */
const AddressVerificationFlow = () => {
  const { context } = useAccountService()

  const shouldRenderAddressInfo = [
    ...flowIsFinishedOnStatues,
    'DENIED',
  ].includes(context?.user_details?.user_address?.status ?? '')

  const [renderPage, setRenderPage] = useState({
    addressUpload: !shouldRenderAddressInfo,
    addressInfo: shouldRenderAddressInfo,
  })

  if (renderPage.addressUpload) {
    return (
      <AddressUpload
        setPage={() =>
          setRenderPage({ addressUpload: false, addressInfo: true })
        }
      />
    )
  }

  return (
    <AddressInfo
      setPage={() => setRenderPage({ addressUpload: true, addressInfo: false })}
    />
  )
}

export default AddressVerificationFlow
