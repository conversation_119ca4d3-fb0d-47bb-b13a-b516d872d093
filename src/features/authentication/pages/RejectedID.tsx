import { useState } from 'react'
import Layout from '../../../common/components/Layout'
import ListItems from '../../../common/components/ListItems'
import NavigationButtons from '../../../common/components/NavigationButtons'
import PageContent from '../../../common/components/PageContent'
import { ASSET } from '../../../common/constants/Assets'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU } from '../../../routes/Route'
import { isDisabled } from '../../DisabledLaunchFeatures'
import { useAccountService } from '../hooks/useAccountService'
import { idRejectionReason } from '../utils/UtilsFunctions'
import FaceScan from './FaceScan'

/**
 * Renders reasons why an ID has been rejected and prompts the user
 * to try again
 * @deprecated In favor of `<RejectedDocument />` when https://github.com/TontineTrust/mytontine-webapp/issues/1677,
 * starts this component will be deleted
 */
const RejectedID = () => {
  const navigate = useCustomNavigation()
  const {
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()

  const [startFaceScan, setStartFaceScan] = useState(false)

  return (
    <Layout
      containerWidth="small"
      pageTitle={t('PERSONAL_DETAILS.FORM_TITLE')}
      navigateTo={ACCOUNT_MENU.PERSONAL_DETAILS}
      bottomSection={
        <NavigationButtons
          onClickFirst={() => navigate(ACCOUNT_MENU.PERSONAL_DETAILS)}
          secondButtonLabel={t('COMMON.TRY_AGAIN')}
          disabledSecond={isDisabled}
          onClickSecond={() => setStartFaceScan(true)}
        />
      }
    >
      {startFaceScan && (
        <FaceScan
          scanType="match-id"
          asModal
          onSuccessfulScan={() => {
            setStartFaceScan(false)
            navigate(ACCOUNT_MENU.PERSONAL_DETAILS)
          }}
          onClickExitScan={() => setStartFaceScan(false)}
        />
      )}
      <PageContent
        icon={ASSET.iconaccountcloecirclemall}
        locizeKey={'REJECTION_EXPLANATION'}
        mainContent={
          <ListItems
            arrayOfItems={idRejectionReason(
              (user_details?.id_rejection_reason as unknown as Array<string>) ??
                []
            )}
          />
        }
      />
    </Layout>
  )
}

export default RejectedID
