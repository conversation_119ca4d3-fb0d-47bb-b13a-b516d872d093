import { toast } from 'react-toastify'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { ANIMATION } from '../../../common/constants/Animations'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU, PUBLIC } from '../../../routes/Route'
import PinChange from '../components/PinChange'
import PinConfirmation from '../components/PinConfirmation'
import { useAccountService } from '../hooks/useAccountService'
import { usePinChange } from '../hooks/usePinChange'
import style from '../style/PinSetupPage.module.scss'
import { AUTH_CONSTANTS } from '../utils/consts'

/**
 * PinSetupPage
 *
 * This page is used to set up a PIN for the first time, or to change an existing PIN.
 * The page is a simple form with a title and a single input field for the PIN.
 */
const PinSetupPage = () => {
  const t = useTranslate()
  const navigate = useCustomNavigation()

  const {
    send,
    context: { user_details },
    currentState,
  } = useAccountService()

  // Loading state for Modal
  const isLoading =
    currentState === 'CHANGING_CURRENT_PIN' ||
    currentState === 'CREATING_NEW_PIN'

  const pinLength = AUTH_CONSTANTS.PIN_INPUT_FIELDS

  const {
    error,
    oldPinRefs,
    newPinRefs,
    oldPin,
    handleSetOldPin,
    handleChangePin,
  } = usePinChange(pinLength)

  const handleCreatePin = (pin?: string) => {
    send({
      type: 'CREATE_NEW_PIN',
      payload: {
        pin,
      },
    })

    navigate(ACCOUNT_MENU.SETTINGS, {
      replace: true,
    })
    toast.success(t('PIN_SETUP_SUCCESS_MESSAGE'))
  }

  return (
    <Layout
      containerWidth="small"
      pageTitle={
        user_details?.pin_set
          ? t('AUTH.PAGE_TITLE_CHANGE_PIN')
          : t('PIN_SETUP_TITLE')
      }
      navigateTo={PUBLIC.GO_TO_PREV_PAGE}
      bottomSection={
        <NavigationButtons
          hideActionButton
          onClickFirst={() => navigate(PUBLIC.GO_TO_PREV_PAGE)}
        />
      }
    >
      <ErrorBoundaryAndSuspense>
        <section className={style['pin-setup-page']}>
          {user_details?.pin_set && (
            <PinChange
              label={t('KYC.INPUT_LABEL_CONFIRM_OLD_PIN')}
              pinLength={pinLength}
              pin={oldPin}
              onChange={handleSetOldPin}
              error={error}
              pinChangeRefs={oldPinRefs}
            />
          )}

          <PinConfirmation
            headerTitle={
              user_details?.pin_set
                ? t('PIN_CHANGE.HEADER_TITLE')
                : t('PIN_SETUP.HEADER_TITLE')
            }
            initialPinLabel={t('PIN_PROMPT.PIN_INPUT_LABEL')}
            confirmPinLabel={t('AUTH.INPUT_LABEL_CONFIRM_PIN')}
            pinLength={pinLength}
            handleSubmit={
              user_details?.pin_set ? handleChangePin : handleCreatePin
            }
            externalInputRefs={newPinRefs}
            focusOnSuccess={false}
          />
          <ConfirmationModal
            animatedIcon={ANIMATION.loadingLightBlueDots}
            isOpen={isLoading}
            title={t('PIN_SUBMITTING_MESSAGE')}
          />
        </section>
      </ErrorBoundaryAndSuspense>
    </Layout>
  )
}

export default PinSetupPage
