import { useState } from 'react'
import { Trans } from 'react-i18next'
import FileUpload from '../../../common/components/FileUpload'
import Icon from '../../../common/components/Icon'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import TextError from '../../../common/components/TextError'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { formatFileSize } from '../../../common/utils/UtilFunctions'
import { PRIVATE } from '../../../routes/Route'
import UploadedFilesCard from '../components/UploadedFilesCard'
import { useAccountService } from '../hooks/useAccountService'
import style from '../style/AddressUpload.module.scss'
import {
  allowedFileTypesForAddressVerification,
  FILE_SIZE_LIMIT_MB,
} from '../utils/consts'
import { isAttachedFileValid } from '../utils/UtilsFunctions'

/**
 * Allows the user to submit their residency verification document
 */
const AddressUpload = ({ setPage }: { setPage: () => void }) => {
  const t = useTranslate()
  const { send, context, currentState } = useAccountService()

  const uploadStatus = context?.user_details?.user_address?.status

  const [uploadedFiles, setUploadedFiles] = useState<Array<File>>(
    context?.addressDocument ? [context.addressDocument] : []
  )
  const [error, setError] = useState<string | undefined>()
  const [uploadProgress, setUploadProgress] = useState(
    uploadStatus === 'UPLOADED' ? 100 : 0
  )

  const handleFilesSelected = (files: Array<File>) => {
    const file = files[0]
    const { message, valid } = isAttachedFileValid({
      file,
      acceptedFileTypes: allowedFileTypesForAddressVerification,
      sizeLimit: FILE_SIZE_LIMIT_MB,
    })

    if (valid) {
      send({
        type: 'UPLOAD_ADDRESS_DOCUMENT',
        payload: {
          addressDocument: file,
          uploadProgressCallback: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / (progressEvent?.total ?? 0)
            )
            setUploadProgress(percentCompleted)
          },
          failureCallback: (error) => setError(error?.translatedError),
        },
      })
    }

    setUploadedFiles(files)
    setError(message)
  }

  return (
    <Layout
      navigateTo={PRIVATE.ACCOUNT}
      pageTitle={t('ACCOUNT.MENU_ITEM_RESIDENCY_VERIFICATION')}
      containerMt="nomt"
      containerHeight="lh"
      bottomSection={
        <NavigationButtons
          hideBackButton
          secondButtonLabel={t('COMMON.CONTINUE_BUTTON')}
          onClickSecond={setPage}
          disabledSecond={
            Boolean(error) ||
            currentState === 'UPLOADING_ADDRESS_DOCUMENT' ||
            uploadedFiles.length === 0
          }
        />
      }
    >
      <p className={style['address-upload__note']}>
        <Trans i18nKey={'ADDRESS_UPLOAD_INSTRUCTIONS'} />
      </p>
      <FileUpload
        onFilesSelected={handleFilesSelected}
        accept={allowedFileTypesForAddressVerification.join(', ')}
        multiple
      >
        <Icon fileName={ASSET.uploadFiles} />
        <Trans i18nKey={'ADDRESS_VERIFICATION_UPLOAD_MSG'} />
      </FileUpload>
      <p className={style['address-upload__warning']}>
        <Trans
          i18nKey={'ADDRESS_UPLOAD_WARNING_MSG'}
          values={{
            fileSize: formatFileSize(FILE_SIZE_LIMIT_MB),
          }}
        />
      </p>
      <p className={style['address-upload__uploaded-files']}>
        {t('UPLOADED_FILES')}
      </p>
      <UploadedFilesCard
        files={uploadedFiles}
        progressMax={100}
        progressValue={uploadProgress}
        onRemove={() => {
          send({ type: 'REMOVE_UPLOADED_DOCUMENT' })
          setUploadedFiles([])
          setError(undefined)
        }}
        hideProgressBar={Boolean(error)}
      >
        <p className={style['address-upload__no-files-txt']}>
          {t('UPLOAD_FILES_PROMPT')}
        </p>
      </UploadedFilesCard>
      <TextError errorText={error} className={style['address-upload__error']} />
    </Layout>
  )
}

export default AddressUpload
