import { lazy } from 'react'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import AccountMenu from '../components/AccountMenu'

const PersonalDetails = lazy(() => import('./PersonalDetails'))

/**
 * Renders only account menu on mobile or personal details only on desktop on
 * the same route /account
 */
const Account = () => {
  const { isMobileOrTablet } = useDeviceScreen()

  if (isMobileOrTablet) {
    return <AccountMenu />
  }

  return <PersonalDetails />
}

export default Account
