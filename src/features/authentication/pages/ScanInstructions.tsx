import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import ScanInstructionRequirements from '../components/ScanInstructionRequirements'
import style from '../style/ScanInstructions.module.scss'
import { ScanInstructionsProps } from '../types/ScanInstructions.type'

const scanTypeHeader = {
  'enroll-face': {
    title: 'FACE_SCAN.ENROLL_TITLE',
    subtitle: 'FACE_SCAN.ENROLL_SUBTITLE',
  },
  'auth-scan': {
    title: 'FACE_SCAN.LOGIN_TITLE',
    subtitle: 'FACE_SCAN.LOGIN_SUBTITLE',
  },
  'match-id': {
    title: 'FACE_SCAN.AUTH_TITLE',
    subtitle: 'FACE_SCAN.AUTH_SUBTITLE',
  },
}

const scanTypeInstructions = {
  default: {
    fileName: ASSET.cameraIcon,
    title: 'FACE_SCAN.CAMERA_REQ_TITLE',
    text: 'FACE_SCAN.CAMERA_REQ_TEXT',
  },
  'enroll-face': {
    fileName: ASSET.faceScanIcon,
    title: 'FACE_SCAN.ENROLL_AFTER_REQ_TITLE',
    text: 'FACE_SCAN.ENROLL_AFTER_REQ_TEXT',
  },
  'auth-scan': {
    fileName: ASSET.faceScanIcon,
    title: 'FACE_SCAN.LOGIN_AFTER_REQ_TITLE',
    text: 'FACE_SCAN.LOGIN_AFTER_REQ_TEXT',
  },
  'match-id': {
    fileName: ASSET.faceScanIcon,
    title: 'FACE_SCAN.AUTH_AFTER_REQ_TITLE',
    text: 'FACE_SCAN.AUTH_AFTER_REQ_TEXT',
  },
}

/**
 * Scanning instructions for face biometrics, if there is a scanning error, the
 * component renders in error state with instructions to help the user correct
 * their scan error
 */
const ScanInstructions = ({
  scanningErrorMessage,
  children,
  scanType,
}: ScanInstructionsProps) => {
  const t = useTranslate()
  return (
    <section className={style.scanInstructions}>
      <article className={style['scanInstructions__main-container']}>
        {!scanningErrorMessage && (
          <>
            <h2 className={style['scanInstructions__title']}>
              {t(scanTypeHeader[scanType].title)}
            </h2>

            <p className={style['scanInstructions__explainer']}>
              {t(scanTypeHeader[scanType].subtitle)}
            </p>
          </>
        )}

        {scanningErrorMessage && (
          <>
            <Icon
              fileName={ASSET.infoamber}
              className={style['scanInstructions__error-icon']}
            />
            <h1 className={style['scanInstructions__title']}>
              {t(scanningErrorMessage)}
            </h1>
          </>
        )}
        <div className={style['scanInstructions__requirements-container']}>
          {children}
          <ScanInstructionRequirements {...scanTypeInstructions.default} />
          {scanType && scanTypeInstructions[scanType] && (
            <ScanInstructionRequirements {...scanTypeInstructions[scanType]} />
          )}
        </div>
      </article>
    </section>
  )
}

export default ScanInstructions
