import { ReactNode, useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import Layout from '../../../common/components/Layout'
import MultiSelection from '../../../common/components/MultiSelection'
import PageContent from '../../../common/components/PageContent'
import ToastMessage from '../../../common/components/ToastMessage'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import type { SelectionItem } from '../../../common/types/MultiSelection.types'
import { formatDate } from '../../../common/utils/UtilFunctions'
import { ACCOUNT_MENU } from '../../../routes/Route'
import CloseAccountButtons from '../components/CloseAccountButtons'
import { useAccountService } from '../hooks/useAccountService'
import { useSubmitPin } from '../hooks/useSubmitPin'
import type {
  BeforeClosingAccountFeedback,
  FeedbackOption,
  PinAuthorizationCallbacks,
  UseSubmitPinReturn,
} from '../types/CloseAccountTypes'
import PinAuthorization from './PinAuthorization'

const CloseAccount = () => {
  // Hooks
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  const feedbackOptions: Array<FeedbackOption> = [
    {
      id: 1,
      title: t('ACCOUNT.DELETE_ACCOUNT_CHECKBOX_1'),
    },
    {
      id: 2,
      title: t('ACCOUNT.DELETE_ACCOUNT_CHECKBOX_2'),
    },
    {
      id: 3,
      title: t('ACCOUNT.DELETE_ACCOUNT_FEEDBACK_CHECKBOX_3'),
    },
    {
      id: 5,
      title: t('ACCOUNT.DELETE_ACCOUNT_FEEDBACK_CHECKBOX'),
    },
  ]

  // States
  const [confirmClosing, setConfirmClosing] = useState<boolean>(false)
  const [beforeClosingAccountFeedback, setBeforeClosingAccountFeedback] =
    useState<BeforeClosingAccountFeedback>({
      activeSelection: undefined,
      data: feedbackOptions,
    })
  const [pinAuthorization, setPinAuthorization] = useState<boolean>(false)
  const [closeAccountFeedback, setCloseAccountFeedback] = useState<
    Array<string>
  >([])
  const [closeAccountAdditionalFeedback, setCloseAccountAdditionalFeedback] =
    useState<string>('')
  const [reactivatedAccount, setReactivatedAccount] = useState<boolean>(false)
  const [error, setError] = useState<string>('')

  const renderTextAreaForOtherFeedback = (): boolean => {
    if (
      !user_details?.closure_scheduled_time &&
      !reactivatedAccount &&
      confirmClosing
    ) {
      return closeAccountFeedback?.includes(feedbackOptions[3].title)
    }
    return false
  }

  const mainTextContent = (): string => {
    if (user_details?.closure_scheduled_time) {
      return 'CLOSE_ACCOUNT.CLOSED_ACCOUNT_CONTENT'
    }

    if (reactivatedAccount) {
      return 'CLOSE_ACCOUNT.REACTIVATED_ACCOUNT_CONTENT'
    }

    return confirmClosing
      ? 'DELETE_ACCOUNT.FEEDBACK'
      : 'DELETE_ACCOUNT.CONFIRM_INFO'
  }

  const addCloseAccountFeedback = (feedback: SelectionItem): void => {
    if (closeAccountFeedback.includes(feedback?.title as string)) {
      setCloseAccountFeedback((prevState) =>
        prevState.filter((currentReason) => currentReason !== feedback?.title)
      )
    } else {
      setCloseAccountFeedback((prevState) => [
        ...prevState,
        feedback?.title as string,
      ])
    }
  }

  const mainPageContent = (): ReactNode | undefined => {
    if (
      confirmClosing &&
      !user_details?.closure_scheduled_time &&
      !reactivatedAccount
    ) {
      return (
        <MultiSelection
          setMultiSelectionData={(data) =>
            setBeforeClosingAccountFeedback((prev) => ({ ...prev, ...data }))
          }
          multiSelectionData={beforeClosingAccountFeedback}
          onSelection={addCloseAccountFeedback}
          allowMultipleItems
        />
      )
    }
    return undefined
  }

  const showIcon = (): string => {
    if (user_details?.closure_scheduled_time) {
      return ASSET.iconaccountmenucontributionhitory
    }

    if (reactivatedAccount) {
      return ASSET.iconaccountcelebratin
    }

    return confirmClosing ? ASSET.iconnotlikelyemojiurey : ASSET.infoamber
  }

  const onClickCloseAccount = (): void => {
    if (confirmClosing) {
      setPinAuthorization(true)
    } else {
      setConfirmClosing(true)
    }
  }

  const onSuccessfulAccountScheduling: PinAuthorizationCallbacks['onSuccessfulAccountScheduling'] =
    () => {
      setPinAuthorization(false)

      toast.success(
        <ToastMessage title={t('CLOSE_ACCOUNT.SCHEDULED_SUCCESS_CONTENT')} />
      )
    }

  const onFailedAccountScheduling = (): void => {
    setError('Failed to schedule account closure')
    toast.error(<ToastMessage title="Failed to schedule account closure" />)
  }

  useEffect(() => {
    if (!confirmClosing) {
      setCloseAccountFeedback([])
    }
  }, [confirmClosing])

  const { setPin, pin, handleSubmitPin }: UseSubmitPinReturn = useSubmitPin({
    authMachineEvent: 'CLOSE_USER_ACCOUNT',
    successCallback: onSuccessfulAccountScheduling,
    failureCallback: onFailedAccountScheduling,
  })

  if (pinAuthorization) {
    return (
      <PinAuthorization
        onClickAction={() => setPinAuthorization(false)}
        pageTitle={t('ACCOUNT.PAGE_TITLE_DELETE_ACCOUNT')}
        navigateTo={ACCOUNT_MENU.CLOSE_ACCOUNT}
        errorMessage={error}
        onSuccessfulPinSubmit={onSuccessfulAccountScheduling}
        onFailedPinSubmit={onFailedAccountScheduling}
        hideMobileHeader={false}
        actionConfirmText={t('ACCOUNT.CLOSING_ACCOUNT_TEXT')}
        loadingType={'CLOSING_USER_ACCOUNT'}
        modalTitle={'PIN_SUBMITTING_MESSAGE'}
        setPin={(pin) => {
          setPin(pin)
          handleSubmitPin({
            pin: (pin as Array<string>)?.join(''),
            payload: {
              closureFeedback: closeAccountFeedback.join(','),
            },
          })
        }}
        pin={pin}
      />
    )
  }

  return (
    <Layout
      containerWidth="small"
      onClickAction={
        isMobileOrTablet
          ? undefined
          : () => {
              setConfirmClosing(false)
            }
      }
      pageTitle={t('ACCOUNT.PAGE_TITLE_DELETE_ACCOUNT')}
      navigateTo={ACCOUNT_MENU.SETTINGS}
      bottomSection={
        <CloseAccountButtons
          confirmClosing={confirmClosing}
          reactivatedAccount={reactivatedAccount}
          setConfirmClosing={setConfirmClosing}
          scheduledClosingTime={user_details?.closure_scheduled_time}
          setReactivatedAccount={setReactivatedAccount}
          onClickCloseAccount={onClickCloseAccount}
        />
      }
    >
      <PageContent
        icon={showIcon()}
        locizeKey={mainTextContent()}
        mainContent={mainPageContent()}
        textArea={
          renderTextAreaForOtherFeedback() ? <div>Text Area</div> : undefined
        }
        setTextAreaFeedback={() => setCloseAccountAdditionalFeedback('')}
        values={{
          scheduledTimeForClosing: formatDate(
            user_details?.closure_scheduled_time,
            'HH:mm:ss, MMM D YYYY'
          ),
        }}
        value={closeAccountAdditionalFeedback}
      />
    </Layout>
  )
}

export default CloseAccount
