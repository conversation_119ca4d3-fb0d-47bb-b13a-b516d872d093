import { useTranslate } from '../../../common/hooks/useTranslate'
import RegisterForm from '../components/RegisterForm'
import RegisterTitle from '../components/RegisterTitle'
import SignUpFields from '../components/SignUpFields'
import { useRegistration } from '../hooks/useRegistration'
import { StateType } from '../types/AuthMachineTypes.type'
import { RegisterFormModalProps } from '../types/RegisterFormModal.types'

/**
 * Component for rendering the registration form modal.
 */
const RegisterFormModal = ({
  forecastUserData,
  forecastPageRegisterModal,
  backButtonAction,
  hideSmallTitle,
}: RegisterFormModalProps) => {
  const t = useTranslate()
  const {
    currentState,
    resendEmail,
    modalTextContent,
    dismissModal,
    sendEmailAgain,
    signUpError,
    handleRegister,
  } = useRegistration({ forecastUserData })

  return (
    <RegisterForm
      currentState={currentState as StateType}
      resendEmail={resendEmail}
      sendEmailAgain={sendEmailAgain}
      modalTextContent={modalTextContent}
      dismissModal={dismissModal}
    >
      {/* Only appears if these fields are used in the register modal */}
      {!hideSmallTitle && <RegisterTitle />}
      <SignUpFields
        forecastUserData={forecastUserData}
        forecastPageRegisterModal={forecastPageRegisterModal}
        backButtonAction={backButtonAction}
        registerButtonLabel={t('ONBOARDING.SEND_EMAIL_TO_SEE_RESULTS_BUTTON')}
        hideAdditionalFields={Boolean(forecastUserData)}
        hideDivider
        signUpError={signUpError}
        handleRegister={handleRegister}
      />
    </RegisterForm>
  )
}

export default RegisterFormModal
