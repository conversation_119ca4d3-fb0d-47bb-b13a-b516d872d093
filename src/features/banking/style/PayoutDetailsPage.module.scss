@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define payoutDetailsPage */
.payoutDetailsPage {
  &__dividers {
    max-width: 53.6875rem;
  }

  &__user-address {
    margin-bottom: 3.125rem;
  }

  &__bottom-options {
    @include mixins.flex-layout;
    @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-ml
    );
    text-align: center;
  }

  &__delete-acc {
    @include mixins.no-user-select;
    @include mixins.font-style(
      $color: colors.$red,
      $font-weight: variables.$font-semibold
    );
    margin-left: 0.625rem;
    cursor: pointer;
  }

  &__clear-fields {
    @include mixins.no-user-select;
    @include mixins.font-style(
      $color: colors.$blue,
      $font-weight: variables.$font-semibold
    );
    margin-right: 0.625rem;
    cursor: pointer;
  }

  &__disclaimer {
    @include mixins.font-style(
      $color: colors.$gray-medium,
      $font-size: variables.$font-size-sm
    );
    text-align: center;
    margin-bottom: 0.9375rem;
  }

  //Only on mobile
  @media only screen and (max-width: variables.$mobile-devices) {
    &__user-bank {
      margin-top: 0;
    }
    &__user-address {
      margin-bottom: 0;
    }

    &__delete-acc {
      @include mixins.font-style(
        $color: colors.$red,
        $font-size: variables.$font-size-sm,
        $font-weight: variables.$font-semibold
      );
    }

    &__clear-fields {
      @include mixins.font-style(
        $color: colors.$blue,
        $font-size: variables.$font-size-sm,
        $font-weight: variables.$font-semibold
      );
    }

    &__bottom-options {
      @include mixins.font-style($font-size: variables.$font-size-xs);
      margin: 0 0.9375rem;
    }

    &__disclaimer {
      @include mixins.font-style($font-size: variables.$font-size-xs);
    }
  }
}
