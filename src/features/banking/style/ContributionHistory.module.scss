@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define contributionHistory */
.contributionHistory {
  &__range-input {
    @include mixins.flex-layout(row, space-between, null);
    gap: 0.625rem;
    max-width: variables.$user-input-width;
  }

  &__no-contributions {
    @include mixins.flex-layout(column);
  }

  &__message {
    @include mixins.font-style($font-size: variables.$font-size-m);
    text-align: center;
    margin: 0.9375rem 0;
  }

  &__image {
    margin: 5rem 0;
  }

  //Mobile devices only
  @media only screen and (max-width: variables.$mobile-devices) {
    &__range-input {
      width: 100%;
      padding: 0 variables.$mobile-spacing;
    }
  }
}
