@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

/** @define extended-transaction */
.extended-transaction {
  @include mixins.flex-layout(column, center, null);
  @include mixins.no-user-select;
  background-color: colors.$white;
  padding-inline: 1.25rem;
  padding-block: 0rem 0.75rem;
  transition: 0.3s ease-out;

  &--blue-faint {
    background-color: colors.$blue-faint;
  }

  &--gray-dirty {
    background-color: colors.$gray-faint;
  }

  &__container,
  &__id-container {
    @include mixins.flex-layout(row, space-between, center);
    @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-sm
    );
  }

  &__id {
    max-width: 80px;
    text-overflow: ellipsis;
    overflow: clip;
    white-space: nowrap;

    &:hover {
      max-width: 300px;
    }
  }
}
