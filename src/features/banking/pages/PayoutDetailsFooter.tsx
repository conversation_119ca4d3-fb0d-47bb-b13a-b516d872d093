import ClickableText from '../../../common/components/ClickableText'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/PayoutDetailsPage.module.scss'
import { PayoutDetailsFooterProps } from '../types/Banking.types'

/**
 * Renders different clickable text depending on permissions for the
 * PayoutDetails page.
 */
const PayoutDetailsFooter = ({
  onReadOnlyTextClick,
  renderDeleteButton,
  clearAllEditableFields,
  permissions,
  onClickableWritePermissionsText,
}: PayoutDetailsFooterProps) => {
  const t = useTranslate()

  const writePermissionsContent = (
    <>
      <ClickableText
        text={t('CLEAR_ALL_FIELDS')}
        onClick={clearAllEditableFields}
        clickableText={t('CLEAR_ALL_FIELDS')}
        className={style[`payoutDetailsPage__clear-fields`]}
        dataTestID={UI_TEST_ID?.clearAllInputFields}
      />

      {renderDeleteButton && (
        <>
          <div>{t('BUTTON_SEPARATOR')}</div>
          <ClickableText
            text={t('DELETE_BANK_ACC_TEXT')}
            onClick={onClickableWritePermissionsText}
            clickableText={t('DELETE_BANK_ACC_TEXT')}
            className={style[`payoutDetailsPage__delete-acc`]}
            dataTestID={UI_TEST_ID?.deleteBankAccountButton}
          />
        </>
      )}
    </>
  )

  const readOnlyPermissionsContent = (
    <ClickableText
      text={t('PAYOUT_ACC_READ_PERMISSION')}
      onClick={onReadOnlyTextClick}
      clickableText={t('PAYOUT_ACC_READ_PERMISSION')}
      dataTestID={UI_TEST_ID?.startFaceScanFromPayout}
    />
  )

  return (
    <footer className={style[`payoutDetailsPage__bottom-options`]}>
      {permissions === 'write'
        ? writePermissionsContent
        : readOnlyPermissionsContent}
    </footer>
  )
}

export default PayoutDetailsFooter
