import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import SecondaryMenu from '../../../common/pages/SecondaryMenu'
import { MenuCardConfig } from '../../../common/types/SecondaryMenu.types'
import { PAYOUT, PRIVATE } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'

const menuCards: Array<MenuCardConfig> = [
  {
    title: 'BANKING.PAYOUT_ACC_COUNTAINER_TITLE',
    variant: 'alternative',
    items: [
      {
        mainText: 'BANKING.CARD_WHERE_TO_SEND_YOUR_INCOME_LABEL',
        to: PAYOUT.SETUP,
        icon: ASSET.iconaccountaddbank,
        cardVariant: 'gray-dirty',
        dataTestID: UI_TEST_ID.incomeSubMenuPayoutDetails,
      },
    ],
  },
  {
    title: 'BANKING.CONTAINER_SET_PAYOUT_START_MONTH',
    variant: 'alternative',
    comingSoon: true,
    items: [
      {
        mainText: 'PAYOUT_SETTINGS.PAGE_TITLE',
        icon: ASSET.iconaccountaddbank,
        cardVariant: 'gray-dirty',
        disabled: true,
        writeProtected: true,
      },
    ],
  },
]

/**
 * Displays all the available options for the user to manage their payout settings
 * including bank account setup and payout schedule configuration.
 */
const PayoutMenu = () => {
  const {
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()

  return (
    <SecondaryMenu
      navigateTo={PRIVATE.ACCOUNT}
      pageTitle={t('ACCOUNT.MENU_ITEM_PAYOUT_SETUP')}
      menuCards={menuCards.map((card) => ({
        ...card,
        title: t(card.title),
        items: card.items.map((item) => ({
          ...item,
          mainText: t(item.mainText),
          writeProtected: item.writeProtected || !user_details?.payout_account,
        })),
      }))}
    />
  )
}

export default PayoutMenu
