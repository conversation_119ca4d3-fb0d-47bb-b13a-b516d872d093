import { useState } from 'react'
import Button from '../../../common/components/Button'
import CountryDropdown from '../../../common/components/CountryDropdown'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import InputGroup from '../../../common/components/InputGroup'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import SelectValue from '../../../common/components/SelectValue'
import TextInput from '../../../common/components/TextInput'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { inputRestrictionRegex } from '../../../common/constants/Regex'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU } from '../../../routes/Route'
import FaceScan from '../../authentication/pages/FaceScan'
import { usePayoutDetails } from '../hooks/usePayouDetails'
import { useValidateInputFields } from '../hooks/useValidateInputFields'
import style from '../style/PayoutDetailsPage.module.scss'
import { BANKING_CONSTANTS, DEFAULT_VALUES } from '../utils/consts'
import PayoutDetailsFooter from './PayoutDetailsFooter'

/**
 * Payout bank account details for the USA banking system
 */
const USAPayoutDetails = () => {
  const t = useTranslate()
  const [isModalOpen, setIsModalOpen] = useState(false)

  const {
    validateBankAccountNumber,
    bankAccountValidated,
    validateACHNumber,
    achRoutingNumberValidated,
    allFieldsValid,
    invalidateAllFields,
  } = useValidateInputFields()

  const {
    firstName,
    lastName,
    accountType,
    bankAccountNumber,
    routingNumber,
    transferType,
    setAccountType,
    setBankAccountNumber,
    setRoutingNumber,
    onClickNext,
    isLoading,
    navigateBackToMenu,
    userHasTypedData,
    onClickDeleteBankAccount,
    clearAndInvalidateEditableFields,
    isLoadingDelete,
    renderDeleteButton,
    readOnly,
    permissions,
    startFaceScan,
    setStartFaceScan,
    user_details,
  } = usePayoutDetails(invalidateAllFields)

  return (
    <Layout
      navigateTo={ACCOUNT_MENU.PAYOUTS_MENU}
      className={style.payoutDetailsPage}
      dividersClass={style[`payoutDetailsPage__dividers`]}
      pageTitle={t('BANKING.PAYOUT_ACCOUNT_PAGE_TITLE')}
      bottomSection={
        <NavigationButtons
          onClickFirst={navigateBackToMenu}
          secondButtonLabel={t('PAYOUT.NEXT_BUTTON_LABEL')}
          onClickSecond={onClickNext}
          disabledSecond={!(allFieldsValid && userHasTypedData)}
          secondButtonLoading={isLoading}
          dataTestIDFirstBtn={UI_TEST_ID.backPayoutDetailsButton}
          dataTestIDSecondBtn={UI_TEST_ID.submitPayoutDetailsButton}
        />
      }
    >
      <ErrorBoundaryAndSuspense>
        <ConfirmationModal
          isOpen={isModalOpen}
          icon={ASSET.iconaccountinfotrianlemall}
          title={t('CONFIRM_DEL_BANK_ACC.TITLE')}
          content={t('CONFIRM_DEL_BANK_ACC.CONTENT')}
        >
          <Button
            loading={isLoadingDelete}
            textOnLoading={t('LOADING_TEXT')}
            variant={'danger'}
            dataTestID={UI_TEST_ID?.confirmDeleteBankAccountButton}
            onClick={() =>
              onClickDeleteBankAccount(() => setIsModalOpen(false))
            }
          >
            {t('CONFIRM_DEL_BANK_ACC.BUTTON_LABEL')}
          </Button>

          <Button onClick={() => setIsModalOpen(false)} variant="alternative">
            {t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
          </Button>
        </ConfirmationModal>
        <InputGroup className={style[`payoutDetailsPage__user-bank`]}>
          <TextInput
            readOnly
            value={`${firstName} ${lastName}`}
            label={t('USA_BANK_ACCOUNT_HOLDER_LABEL')}
            dataTestID={UI_TEST_ID.verifiedFullName}
          />
          <TextInput
            readOnly
            value={transferType.toUpperCase()}
            label={t('USA_BANK_TYPE_TRANSFER')}
          />

          <CountryDropdown
            value={DEFAULT_VALUES.country}
            readOnly
            label={t('COUNTRY.OF.BANK.ACC.LABEL')}
          />

          <SelectValue
            value={accountType}
            setValue={setAccountType}
            label={t('TRUST.ACC.TYPE.LABEL')}
            optionsToSelect={['Checking', 'Savings']}
            buttonLabels={['Checking', 'Savings']}
            optional={Boolean(readOnly)}
          />

          <TextInput
            value={bankAccountNumber}
            onChange={setBankAccountNumber}
            label={t('DOMESTIC_ACCOUNT_NUM_LABEL')}
            validatorFunction={validateBankAccountNumber}
            errorMessage={bankAccountValidated}
            placeholder={t('USA_BANK_ACCOUNT_NUM_PLACEHOLDER')}
            dataTestID={UI_TEST_ID.usBankAccountNumberInput}
            maxLength={BANKING_CONSTANTS.USA_BANK_ACCOUNT_MAX}
            inputMode="numeric"
            restrictionRegex={inputRestrictionRegex.onlyDigits}
            readOnly={readOnly}
          />

          <TextInput
            value={routingNumber}
            onChange={setRoutingNumber}
            label={t('DOMESTIC_BRANCH_LABEL')}
            validatorFunction={validateACHNumber}
            errorMessage={achRoutingNumberValidated}
            placeholder={t('USA_ROUTING_NUMBER_PLACEHOLDER')}
            dataTestID={UI_TEST_ID.usACHRoutingNumberInput}
            maxLength={BANKING_CONSTANTS.USA_ACH_ROUTING_NUM_MAX}
            inputMode="numeric"
            readOnly={readOnly}
          />
        </InputGroup>

        {startFaceScan && (
          <FaceScan
            onSuccessfulScan={() => setStartFaceScan(false)}
            onClickExitScan={() => setStartFaceScan(false)}
            scanType={
              user_details?.face_enrolled && permissions === 'read'
                ? 'auth-scan'
                : 'match-id'
            }
            asModal
          />
        )}

        <PayoutDetailsFooter
          clearAllEditableFields={clearAndInvalidateEditableFields}
          renderDeleteButton={renderDeleteButton}
          onClickableWritePermissionsText={() => setIsModalOpen(true)}
          onReadOnlyTextClick={() => setStartFaceScan(true)}
          permissions={permissions}
        />

        <div className={style[`payoutDetailsPage__disclaimer`]}>
          {t('PAYOUT_ACCOUNT.BOTTOM_DISCLAIMER')}
        </div>
      </ErrorBoundaryAndSuspense>
    </Layout>
  )
}

export default USAPayoutDetails
