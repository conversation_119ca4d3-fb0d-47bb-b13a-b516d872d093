import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import SecondaryMenu from '../../../common/pages/SecondaryMenu'
import { MenuCardConfig } from '../../../common/types/SecondaryMenu.types'
import { FUND_PENSION, PRIVATE } from '../../../routes/Route'

const menuCards: Array<MenuCardConfig> = [
  {
    title: 'BANKING.CARD_TITLE_BANK_DETAILS',
    variant: 'alternative',
    items: [
      {
        mainText: 'BANKING.FUND_PLAN_CARD_LABEL_CONTRIBUTION_ACCOUNT',
        to: FUND_PENSION.INVESTMENT_ACCOUNT,
        icon: ASSET.iconaccountaddbank,
        writeProtected: true,
        cardVariant: 'gray-dirty',
      },
    ],
  },
  {
    title: 'BANKING.FUND_PLAN_INPUT_LABEL_FUTURE_FEATURE',
    variant: 'alternative',
    comingSoon: true,
    items: [
      {
        mainText: 'BANKING.CARD_DIRECT_DEBIT_LABEL',
        to: 'NONE',
        icon: ASSET.iconaccountfundbank,
        cardVariant: 'gray-dirty',
        disabled: true,
      },
    ],
  },
  {
    title: 'FUND_PENSION.OPEN_BANKING_MENU_TEXT',
    variant: 'alternative',
    comingSoon: true,
    items: [
      {
        mainText: 'FUND_PENSION.OPEN_BANKING_MENU_TEXT',
        to: 'NONE',
        icon: ASSET.iconaccountfundbank,
        cardVariant: 'gray-dirty',
        disabled: true,
      },
    ],
  },
  {
    title: 'TRANSFER_AND_CONTRIBUTIONS.LEGEND_ITEM',
    variant: 'alternative',
    items: [
      {
        to: FUND_PENSION.CONTRIBUTION_HISTORY,
        mainText: 'BANKING.PAGE_TITLE_CONTRIBUTION_HISTORY',
        icon: ASSET.iconaccountmenucontributionhitory,
        dataTestID: UI_TEST_ID.subMenuItemFundsTrans,
        cardVariant: 'gray-dirty',
      },
    ],
  },
]

/**
 * Displays all the available options for the user to choose how
 * they will fund their pension. KYC check is also being done here in order to
 * display a check list to the user that they need to do before proceeding
 * further with any of the fund options.
 */
const FundPensionMenu = () => {
  //Hooks
  const t = useTranslate()

  return (
    <SecondaryMenu
      navigateTo={PRIVATE.ACCOUNT}
      pageTitle={t('BANKING.PAGE_TITLE_FUND_YOUR_PLAN')}
      menuCards={menuCards.map((card) => ({
        ...card,
        title: t(card.title),
        items: card.items.map((item) => ({
          ...item,
          mainText: t(item.mainText),
        })),
      }))}
    />
  )
}

export default FundPensionMenu
