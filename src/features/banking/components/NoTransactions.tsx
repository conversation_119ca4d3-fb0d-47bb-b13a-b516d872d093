import { ReactNode } from 'react'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import style from '../style/ContributionHistory.module.scss'

/**
 * Renders an icon with text notifying the user that they don't
 * have any transactions. If `contributionButtonLabel` is passed in a CTA button
 * render that takes the user to the "Fund your pension" page.
 */
const NoTransactions = ({ children }: { children?: ReactNode }) => {
  return (
    <div className={style['contributionHistory__no-contributions']}>
      <Icon
        fileName={ASSET.iconaccountnocontribution}
        className={style['contributionHistory__image']}
      />

      <div className={style['contributionHistory__message']}>{children}</div>
    </div>
  )
}

export default NoTransactions
