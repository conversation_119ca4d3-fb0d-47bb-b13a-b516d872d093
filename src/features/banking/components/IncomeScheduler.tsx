import DateSlider from '../../../common/components/DateSlider'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useIncomeScheduler } from '../hooks/useIncomeScheduler'
import type { IncomeSchedulerProps } from './IncomeScheduler.types'

const IncomeScheduler = ({
  retirementData,
  setRetirementData,
  anonUserDetails,
}: IncomeSchedulerProps) => {
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  const { sliderSteps, ageThreshold, scheduleRetirement } =
    // need the initial state just to init the hook because it uses legacy data
    // format
    useIncomeScheduler(retirementData)

  retirementData = {
    // only used for init and it is overridden by fresh retirement data from the
    // state
    ...scheduleRetirement,
    // only use fresh data from the state
    ...retirementData,
  }

  return (
    <DateSlider
      ageThresholds={{
        maxRetirementAge: {
          age: ageThreshold?.maxRetirementAge?.age ?? 0,
          month: ageThreshold?.maxRetirementAge?.month ?? 0,
        },
        maxRetirementAgeYearMonth: {
          year:
            (
              ageThreshold?.maxRetirementAgeYearMonth as {
                year?: number
                month?: number
              }
            )?.year ?? 0,
          month:
            (
              ageThreshold?.maxRetirementAgeYearMonth as {
                year?: number
                month?: number
              }
            )?.month ?? 0,
        },
        minRetirementAge: {
          age: ageThreshold?.minRetirementAge?.age ?? 0,
          month: ageThreshold?.minRetirementAge?.month ?? 0,
        },
        minRetirementAgeYearMonth: {
          year:
            (
              ageThreshold?.minRetirementAgeYearMonth as {
                year?: number
                month?: number
              }
            )?.year ?? 0,
          month:
            (
              ageThreshold?.minRetirementAgeYearMonth as {
                year?: number
                month?: number
              }
            )?.month ?? 0,
        },
      }}
      userDetails={
        anonUserDetails ??
        user_details ?? {
          // only used for blurred forecast
          date_of_birth: `${retirementData?.year}-${retirementData?.month}-01`,
        }
      }
      label={t('INCOME_SLIDER_LABEL')}
      sliderSteps={sliderSteps}
      monthHeadLabel={t('MONTH_HEAD_LABEL')}
      yearHeadLabel={t('YEAR_HEAD_LABEL')}
      value={retirementData}
      yearsOldOnRetirementDateLabel={t('BUBBLE_YEAR_VALUE_LABEL')}
      yearsOldOnRetirementDate={retirementData?.yearsOld}
      monthsOldOnRetirementDateLabel={t('BUBBLE_MONTH_VALUE_LABEL')}
      monthsOldOnRetirementDate={retirementData?.monthsOld}
      caption={t('RETIRE_USER_INCOME_CAPTION')}
      setValue={(value) => {
        if (value) {
          // Create a new object with the required properties
          const newFormData = {
            ...retirementData,
            retirementAge: value.retirementAge,
            year: value.year,
            month: value.month,
            yearsOld: value.yearsOld,
            monthsOld: value.monthsOld,
          }

          // Call the original setRetirementData function with the new object
          setRetirementData(newFormData as unknown as IncomeForecastParams)
        }
      }}
    />
  )
}

export default IncomeScheduler
