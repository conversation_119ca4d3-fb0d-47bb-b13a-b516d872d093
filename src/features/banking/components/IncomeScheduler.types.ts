import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'

type RetirementData = {
  year?: number
  month?: number
  yearsOld?: number
  monthsOld?: number
  retirementAge?: {
    age?: number
    month?: number
  }
}

type AnonUserDetails = {
  date_of_birth?: string
}

type IncomeSchedulerProps = {
  retirementData: RetirementData
  setRetirementData: (formData: IncomeForecastParams) => void
  anonUserDetails?: AnonUserDetails
}

export type { IncomeSchedulerProps, RetirementData, AnonUserDetails }
