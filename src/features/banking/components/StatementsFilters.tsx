import Dropdown from '../../../common/components/Dropdown'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/StatementsFilters.module.scss'
import { StatementsFiltersProps } from '../types/StatementsFilters.types'
import { statementTypes } from '../utils/consts'

/**
 * Dropdown filters used for filtering statement types and statuses
 */
const StatementsFilters = <T extends (typeof statementTypes)[number]>({
  statementType,
  onStatementTypeChange,
  statementOptions,
  status,
  onStatusChange,
  statusOptions,
  statementTypeLabel,
  statusLabel,
}: StatementsFiltersProps<T>) => {
  const t = useTranslate()

  // Translate the statement options for display
  const translatedStatementOptions = statementOptions.map((option) => ({
    ...option,
    label: t(option.label),
  }))

  // Translate the current statement type for display
  const translatedStatementType = {
    ...statementType,
    label: t(statementType.label),
  }
  return (
    <section className={style['statements-filters']}>
      <Dropdown
        className={style['statements-filters__type']}
        optional
        label={statementTypeLabel}
        searchBy={['label']}
        itemKey={{
          displayKey: 'label',
        }}
        value={translatedStatementType}
        options={translatedStatementOptions}
        onChange={(option) => {
          // Find the original option that corresponds to the translated option
          const originalOption = statementOptions.find(
            (orig) => orig.value === option.value
          )
          if (originalOption) {
            onStatementTypeChange(originalOption)
          }
        }}
      />
      {statusOptions && statusOptions?.length > 0 && (
        <Dropdown
          optional
          label={statusLabel}
          searchBy={['label']}
          itemKey={{
            displayKey: 'label',
          }}
          value={status}
          options={statusOptions}
          onChange={(option) => onStatusChange?.(option as T)}
        />
      )}
    </section>
  )
}

export default StatementsFilters
