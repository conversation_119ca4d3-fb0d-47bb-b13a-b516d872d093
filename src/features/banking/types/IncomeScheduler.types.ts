import type { TontinatorUIParams } from '../../../common/types/SupportedCountries.types'
// Type definitions for useIncomeScheduler hook
import type { AgeMonth } from '../../CommonState.type'

// Input parameter type for useIncomeScheduler hook
export type RetirementData = {
  contributionAge?: AgeMonth
  retirementAge?: {
    age?: number
    month?: number
  }
  year?: number
  month?: number
  yearsOld?: number
  monthsOld?: number
}

// State type for scheduleRetirement
export type ScheduleRetirement = {
  contributionAge?: AgeMonth
  retirementAge?: {
    age?: number
    month?: number
  }
  year?: number
  month?: number
  yearsOld?: number
  monthsOld?: number
}

// Age threshold type (alias for TontinatorUIParams)
export type AgeThreshold = TontinatorUIParams

// Return type for useIncomeScheduler hook
export type UseIncomeSchedulerReturn = {
  sliderSteps: number[]
  setSliderSteps: (steps: number[]) => void
  scheduleRetirement: ScheduleRetirement
  setScheduleRetirement: (retirement: ScheduleRetirement) => void
  isLoading: boolean
  ageThreshold?: AgeThreshold
}

// Return type for calculateYearForRetirementAge function
export type CalculateYearForRetirementAgeReturn =
  | {
      yearsOldOnRetirement: number
      monthsOldOnRetirement: number
      yearsOldNow: number
      monthsOldNow: number
      retirementYear: number
      retirementMonth: number
    }
  | undefined
