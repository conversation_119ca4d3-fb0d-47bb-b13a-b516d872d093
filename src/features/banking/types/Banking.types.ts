import { AxiosResponse } from 'axios'
import { CardVariantType } from '../../../common/types/Card.types'
import { AccountPermissions } from '../../authentication/types/AuthMachineTypes.type'

type ExtendedContentProps = {
  date?: string
  time?: string
  secondLineData?: string
  firstLineLabel?: string
  secondLineLabel?: string
  variant?: CardVariantType
}

type PayoutDetailsFooterProps = {
  onReadOnlyTextClick: () => void
  renderDeleteButton: boolean
  clearAllEditableFields: () => void
  permissions?: AccountPermissions
  onClickableWritePermissionsText: () => void
}

type SettledPromise = PromiseSettledResult<AxiosResponse<unknown, unknown>>

export type { ExtendedContentProps, PayoutDetailsFooterProps, SettledPromise }
