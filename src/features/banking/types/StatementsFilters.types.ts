interface StatusFilters<T> {
  status: T
  onStatusChange: (value: T) => void
  statusOptions: Array<T>
  statementTypeLabel: string
  statusLabel: string
}

// partial for now since the status filters will be done in
// another PR
interface StatementsFiltersProps<T> extends Partial<StatusFilters<T>> {
  statementType: T
  onStatementTypeChange: (value: T) => void
  statementOptions: Array<T>
}

export type { StatementsFiltersProps }
