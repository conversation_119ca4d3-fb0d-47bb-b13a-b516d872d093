import { InvestmentDetails } from '../../authentication/types/AuthMachineTypes.type'
import { AgeMonth, ErrorStorage, YearMonth } from '../../CommonState.type'
import { BankingInfo } from './BankTypes.type'

type ParsedProductRules = {
  minRetirementAge: AgeMonth
  minRetirementAgeYearMonth: YearMonth
  maxRetirementAge: AgeMonth
  maxRetirementAgeYearMonth: YearMonth
  minCurrentAge: AgeMonth
  minCurrentAgeYearMonth: YearMonth
  maxCurrentAge: AgeMonth
  maxCurrentAgeYearMonth: YearMonth
}

type Threshold = {
  age_month: AgeMonth
  year_month: YearMonth
}

type ProductRules = {
  payout_min: Threshold
  payout_max: Threshold
  contribution_min: Threshold
  contribution_max: Threshold
}

type TontineProducts = 'TontineIRA' | 'TontineTrustFund'

type TontineProductRules = {
  [key in TontineProducts]?: ProductRules
}

type ForecastRules = {
  [key: string]: {
    supported_investments: InvestmentDetails
    products: TontineProductRules
    countries: Array<string>
    currency: 'USD' | 'EUR ' | 'JPY'
  }
}

/**
 * Context of the bank machine with out of the box data, like a `navigator`
 * function to navigate the user and `toast` for sending toast messages with `actions`
 */
type BankMachineContext = {
  bankingInfoError?: ErrorStorage
  bankingInfo: BankingInfo
  returns?: { forecastRules: ForecastRules }
  tontineProduct: TontineProducts
}

type BankPayload = {
  abortController?: AbortController
  finallyCallback?: () => void
  successCallback?: (data?: unknown) => void
  failureCallback?: (error?: ErrorStorage) => void
  currencyParamForRate?: string
  product?: TontineProducts
}

/**
 * Supported events by the bank machine
 */
type Events = {
  type: 'FETCH_BANKING_INFO' | 'GET_RETURNS' | 'UPDATE_PRODUCT'
  output?: BankingInfo & { invAccOpen?: boolean }
  payload?: BankingInfo & BankPayload & { invAccOpen?: boolean }
}

type BankMachineEvent = Events
/**
 * All states that the bank machine can be in
 */
type States = {
  FETCHING_BANK_INFO: 'FETCHING_BANK_INFO'
  GETTING_RETURNS: 'GETTING_RETURNS'
  UPDATE_PRODUCT: 'UPDATE_PRODUCT'
}

export type {
  AgeMonth,
  BankingInfo,
  BankMachineContext,
  BankMachineEvent,
  Events,
  ForecastRules,
  ParsedProductRules,
  ProductRules,
  States,
  Threshold,
  TontineProductRules,
  TontineProducts,
  YearMonth,
}
