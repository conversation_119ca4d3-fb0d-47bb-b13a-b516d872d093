import dayjs from 'dayjs'
import { InvestmentDetails } from '../authentication/types/AuthMachineTypes.type'
import { Events, ForecastRules } from './types/BankMachineTypes.type'
import { HistoryTransaction } from './types/BankTypes.type'
import { BANKING_CONSTANTS } from './utils/consts'

/**
 * Updates the returns in the context with data from the event
 */
const updateReturns = {
  returns: ({ event }: { event: Events }) => {
    if (
      event.output &&
      typeof event.output === 'object' &&
      'forecastRules' in event.output
    ) {
      return event.output as {
        returns: InvestmentDetails
        forecastRules: ForecastRules
      }
    }
    return undefined
  },
}

/**
 * Clears the banking fetch error from the context
 */
const wipeBankFetchError = {
  bankingInfoError: () => {
    return undefined
  },
}

/**
 * Changes the tontine product in the context
 */
const changeProduct = {
  tontineProduct: ({ event }: { event: Events }) => {
    return event.payload?.product ?? 'TontineTrustFund'
  },
}

/**
 * Stores banking information in the context
 */
const storeBankingInfo = {
  bankingInfo: ({ event }: { event: Events }) => {
    const payinHistory = event.output?.nominalBalance
      ?.filter((balance: HistoryTransaction) => {
        return balance.transaction?.[`type'`] === BANKING_CONSTANTS.contribution
      })
      .sort((a: HistoryTransaction, b: HistoryTransaction) =>
        dayjs(b.transaction?.time).diff(dayjs(a.transaction?.time))
      ) as HistoryTransaction[]

    const payoutHistory = event.output?.nominalBalance
      ?.filter((balance: HistoryTransaction) => {
        return balance.transaction?.[`type'`] === BANKING_CONSTANTS.payout
      })
      .sort((a: HistoryTransaction, b: HistoryTransaction) =>
        dayjs(b.transaction?.time).diff(dayjs(a.transaction?.time))
      ) as HistoryTransaction[]

    const all = [...payinHistory, ...payoutHistory].sort((a, b) =>
      dayjs(b.transaction?.time).diff(dayjs(a.transaction?.time))
    )

    return {
      ...event.output,
      payinHistory,
      payoutHistory,
      all,
    }
  },
}

export { changeProduct, storeBankingInfo, updateReturns, wipeBankFetchError }
