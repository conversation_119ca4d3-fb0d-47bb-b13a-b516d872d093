import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import ToastMessage from '../../../common/components/ToastMessage'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU, PUBLIC } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import {
  ACHRoutingNumber,
  BankAccountNumber,
  BankAccountType,
  PayoutAccountDetails,
  TransferType,
} from '../../authentication/types/AuthMachineTypes.type'
import { DEFAULT_VALUES } from '../utils/consts'

/**
 * Payout details handling
 */
export const usePayoutDetails = (invalidateAllFields: () => void) => {
  const navigate = useCustomNavigation()
  const t = useTranslate()

  const {
    states,
    currentState: currentBankState,
    send,
    context: { user_details, permissions },
  } = useAccountService()

  const shouldRedactInfo = permissions === 'read'
  const redactedInfo = Array.from({ length: 9 }).join('*')

  const [startFaceScan, setStartFaceScan] = useState(false)
  const [userHasTypedData, setUserHasTypedData] = useState(false)

  ////////////Bank account details///////////
  const [accountType, setAccountType] = useState<BankAccountType>(
    user_details?.payout_account?.account?.checkingSavings ??
      (DEFAULT_VALUES.accountType as BankAccountType)
  )
  // Sensitive info
  const [bankAccountNumber, _setBankAccountNumber] = useState<
    BankAccountNumber | undefined
  >(
    shouldRedactInfo
      ? redactedInfo
      : user_details?.payout_account?.account?.accountNumber
  )
  const [routingNumber, _setRoutingNumber] = useState<
    ACHRoutingNumber | undefined
  >(
    shouldRedactInfo ? redactedInfo : user_details?.payout_account?.account?.ach
  )

  useEffect(() => {
    // No more redaction needed when user has write permissions
    if (permissions === 'write') {
      _setBankAccountNumber(
        user_details?.payout_account?.account?.accountNumber
      )
      _setRoutingNumber(user_details?.payout_account?.account?.ach)
    }
  }, [permissions])

  //For V1 MVP we only support ACH transfers
  const [transferType, setTransferType] = useState<TransferType>(
    DEFAULT_VALUES.transferType as TransferType
  )

  const navigateBackToMenu = () => navigate(PUBLIC.GO_TO_PREV_PAGE)

  /**
   * Sets the bank account number with a dispatch function, but not before the
   * digits from the input are cleared of any chars, and max input digits is 17
   */
  const setBankAccountNumber = (bankAccountNumber: string): void => {
    _setBankAccountNumber(bankAccountNumber)
    setUserHasTypedData(true)
  }

  /**
   * Sets the ACH routing number to the dispatch function, but not before the
   * digits from the input are cleared of any chars, and max input digits is 9
   */
  const setRoutingNumber = (routingNumber: string): void => {
    _setRoutingNumber(routingNumber)
    setUserHasTypedData(true)
  }

  /**
   * Sends an event to the banking machine with the data harvested from the
   * input fields
   */
  const onClickNext = (): void => {
    send({
      type: 'UPDATE_PAYOUT_DETAILS',
      payload: {
        payoutDetails: {
          account: {
            tag: transferType,
            ach: routingNumber,
            accountNumber: bankAccountNumber,
            checkingSavings: accountType,
          },
        } as PayoutAccountDetails,

        successCallback: () => {
          toast.success(
            <ToastMessage
              title={t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')}
            />
          )
          navigate(ACCOUNT_MENU.PAYOUTS_MENU)
        },

        failureCallback: (error) => toast.error(error?.translatedError),
      },
    })
  }

  /**
   * Clears all fields from data
   */
  const clearAndInvalidateEditableFields = (): void => {
    _setBankAccountNumber(undefined)
    _setRoutingNumber(undefined)
    invalidateAllFields?.()
  }

  /**
   * Deletes user's current payout bank account
   */
  const onClickDeleteBankAccount = (successCallback?: () => void): void => {
    send({
      type: 'DELETE_PAYOUT_DETAILS',
      payload: {
        successCallback: () => {
          successCallback?.()

          toast.success(
            <ToastMessage
              title={t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')}
            />
          )
          clearAndInvalidateEditableFields()
          setUserHasTypedData(false)
        },
        failureCallback: (error) => toast.error(error?.translatedError),
      },
    })
  }

  return {
    isLoading: currentBankState === states.UPDATING_PAYOUT_DETAILS,
    isLoadingDelete: currentBankState === states.DELETING_PAYOUT_DETAILS,
    firstName: user_details?.first_name,
    lastName: user_details?.last_name,
    accountType,
    bankAccountNumber,
    routingNumber,
    transferType,
    setAccountType,
    setBankAccountNumber,
    setRoutingNumber,
    setTransferType,
    onClickNext,
    navigateBackToMenu,
    userHasTypedData,
    onClickDeleteBankAccount,
    clearAndInvalidateEditableFields,
    renderDeleteButton: Boolean(
      user_details?.payout_account?.account?.accountNumber
    ),
    readOnly: permissions === 'read',
    permissions,
    startFaceScan,
    setStartFaceScan,
    user_details,
  }
}
