import { useEffect, useState } from 'react'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { generateRange } from '../../../common/utils/UtilFunctions'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { calculateYearForRetirementAge } from '../../dashboard/utils/UtilFunctions'
import {
  AgeThreshold,
  RetirementData,
  ScheduleRetirement,
  UseIncomeSchedulerReturn,
} from '../types/IncomeScheduler.types'

export const useIncomeScheduler = (
  retirementData: RetirementData
): UseIncomeSchedulerReturn => {
  const {
    context: { user_details },
  } = useAccountService()

  const calculateResult =
    retirementData?.contributionAge &&
    retirementData?.retirementAge?.age !== undefined &&
    retirementData?.retirementAge?.month !== undefined
      ? calculateYearForRetirementAge(retirementData.contributionAge, {
          age: retirementData.retirementAge.age,
          month: retirementData.retirementAge.month,
        })
      : undefined

  const { retirementYear, retirementMonth } = calculateResult ?? {
    retirementYear: 0,
    retirementMonth: 0,
  }

  // biome-ignore lint/style/noParameterAssign: <TODO: Solve>
  retirementData = {
    ...retirementData,
    year: retirementYear,
    month: retirementMonth,
    yearsOld: retirementData?.retirementAge?.age,
    monthsOld: retirementData?.retirementAge?.month,
  }

  const {
    detectedCountry: { tontinatorParams },
  } = useLocalization(user_details?.residency)

  //Income scheduling state
  const [scheduleRetirement, setScheduleRetirement] =
    useState<ScheduleRetirement>(retirementData)
  //Generates slider steps, sliders steps are in state so when the user reaches
  //the range limit they can click the increment button so a new step is added
  //to the range. The range start if from age threshold, always. Age threshold
  //contains the minimum date that the user can set the slider to
  const [sliderSteps, setSliderSteps] = useState<number[]>([])

  //Initializes the income scheduler with retirement data or if there is no
  //retirement data uses the age threshold
  useEffect(() => {
    if (tontinatorParams) {
      setScheduleRetirement(
        presetRetirementData(retirementData, tontinatorParams)
      )
      setSliderSteps(presetAgeSlider(tontinatorParams))
    }
  }, [
    user_details?.residency,
    ///////////////////
    user_details?.date_of_birth,
    tontinatorParams?.maxRetirementAge?.age,
    tontinatorParams?.maxRetirementAge?.month,
    tontinatorParams?.minRetirementAge?.age,
    tontinatorParams?.minRetirementAge?.month,
  ])

  return {
    sliderSteps,
    setSliderSteps,
    scheduleRetirement,
    setScheduleRetirement,
    isLoading: false,
    ageThreshold: tontinatorParams,
  }
}

//Presets the income scheduler data by checking if the user has already
//set their retirement month/year, if they have not then the age threshold
//data is used to preset the income scheduler
const presetRetirementData = (
  retirementData: RetirementData,
  ageThreshold: AgeThreshold
): ScheduleRetirement => {
  //Parses the age threshold values to number format
  const minRetirementAgeYearMonth = ageThreshold.minRetirementAgeYearMonth
  const minRetirementAge = ageThreshold.minRetirementAge

  if (!minRetirementAgeYearMonth || !minRetirementAge) {
    return retirementData
  }

  const { year, month } = minRetirementAgeYearMonth
  const { age: yearsOld, month: monthsOld } = minRetirementAge

  //Prioritize using the retirement data passed in from props, the reason being
  //because those values can be pension plan values
  //Retirement data MONTH and YEAR are in number format
  if (
    retirementData?.year &&
    retirementData.year > 0 &&
    retirementData?.month !== undefined &&
    retirementData.month >= 0
  ) {
    return {
      ...retirementData,
      //If no values are provided then use age threshold values
      month: Math.max(retirementData?.month, month),
      year: Math.max(retirementData?.year, year),
      monthsOld: Math.max(retirementData?.monthsOld ?? 0, monthsOld),
      yearsOld: Math.max(retirementData?.yearsOld ?? 0, yearsOld),
    }
  }

  //Just returns the age thresholds
  return {
    ...retirementData,
    month,
    year,
    yearsOld,
    monthsOld,
  }
}

const presetAgeSlider = (ageThreshold: AgeThreshold): Array<number> => {
  //Sets age slider year to minimum from threshold age
  const minRetirementAgeYearMonth = ageThreshold.minRetirementAgeYearMonth
  const maxRetirementAgeYearMonth = ageThreshold.maxRetirementAgeYearMonth

  if (!minRetirementAgeYearMonth || !maxRetirementAgeYearMonth) {
    return []
  }

  const { year: minStartYear } = minRetirementAgeYearMonth
  const { year: maxStartYear } = maxRetirementAgeYearMonth

  return generateRange(minStartYear, maxStartYear)
}
