import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useValidateUSABankAccountDetails } from './useValidateUSABankAccountDetails'

/**
 * Provides validator functions for all the fields present in this page
 */
export const useValidateInputFields = () => {
  const {
    context: { user_details },
  } = useAccountService()

  const {
    validateBankAccountNumber,
    bankAccountValidated,
    validateACHNumber,
    achRoutingNumberValidated,
    allFieldsValid: bankFieldsValid,
  } = useValidateUSABankAccountDetails({
    initialBankAccountNumberValidated: {
      valid: Boolean(user_details?.payout_account?.account?.accountNumber),
    },
    initialACHRoutingNumberValidated: {
      valid: <PERSON><PERSON><PERSON>(user_details?.payout_account?.account?.ach),
    },
  })

  const invalidateAllFields = () => {
    validateBankAccountNumber(undefined)
    validateACHNumber(undefined)
  }

  return {
    validateBankAccountNumber,
    bankAccountValidated,
    validateACHNumber,
    achRoutingNumberValidated,
    allFieldsValid: bankFieldsValid,
    invalidateAllFields,
  }
}
