import { useState } from 'react'
import { ValidationData } from '../../../common/types/CommonTypes.types'
import { validateInputWithError } from '../../../common/utils/UtilFunctions'
import {
  isValidACHRoutingNumber,
  isValidBankAccountNumber,
} from '../utils/BankingValidators'

/**
 * Validates USA bank account details, mainly the bank account number and the
 * ACH have format validation, other fields are just checked if they are empty
 * or not
 */
export const useValidateUSABankAccountDetails = ({
  initialBankAccountNumberValidated,
  initialACHRoutingNumberValidated,
}: {
  initialBankAccountNumberValidated?: ValidationData
  initialACHRoutingNumberValidated?: ValidationData
} = {}) => {
  const [bankAccountValidated, setBankAccountValidated] = useState<
    ValidationData | undefined
  >(initialBankAccountNumberValidated)

  const [achRoutingNumberValidated, setACHRoutingNumberValidated] = useState<
    ValidationData | undefined
  >(initialACHRoutingNumberValidated)

  /**
   * Validates an USA bank account number
   */
  const validateBankAccountNumber = (bankAccountNumber?: string) =>
    validateInputWithError({
      input: bankAccountNumber,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_BANK_ACCOUNT_NUMBER',
      validateFormat: (bankAccountNumber: string) =>
        !isValidBankAccountNumber(bankAccountNumber),
      invalidInputErrorI18nKey: 'ERROR_INVALID_BANK_ACCOUNT_NUMBER',
      setStateAction: setBankAccountValidated,
    })

  /**
   * Validates an ACH transfer routing number
   */
  const validateACHNumber = (achRoutingNumber?: string) =>
    validateInputWithError({
      input: achRoutingNumber,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_ACH',
      validateFormat: (achRoutingNumber: string) =>
        !isValidACHRoutingNumber(achRoutingNumber),
      invalidInputErrorI18nKey: 'ERROR_INVALID_ACH',
      setStateAction: setACHRoutingNumberValidated,
    })

  return {
    validateBankAccountNumber,
    bankAccountValidated,
    validateACHNumber,
    achRoutingNumberValidated,
    allFieldsValid:
      bankAccountValidated?.valid && achRoutingNumberValidated?.valid,
  }
}
