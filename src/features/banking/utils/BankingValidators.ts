import { BANKING_CONSTANTS } from './consts'

/**
 * Validates an ACH routing number length if it is below 9 digits and does a
 * checksum validation as well
 */
const isValidACHRoutingNumber = (routingNumber: string): boolean => {
  if (routingNumber.length !== BANKING_CONSTANTS.USA_ACH_ROUTING_NUM_MAX) {
    return false
  }

  const weights = [3, 7, 1, 3, 7, 1, 3, 7, 1]
  let sum = 0

  for (let i = 0; i < BANKING_CONSTANTS.USA_ACH_ROUTING_NUM_MAX; i++) {
    sum += weights[i] * Number.parseInt(routingNumber[i], 10)
  }

  return sum % 10 === 0
}

/**
 * Validates if USA bank account number contains digits, if the digit count is
 * below 5 or above 17
 */
const isValidBankAccountNumber = (bankAccountNumber: string): boolean =>
  /^\d{5,17}$/.test(bankAccountNumber)

export { isValidACHRoutingNumber, isValidBankAccountNumber }
