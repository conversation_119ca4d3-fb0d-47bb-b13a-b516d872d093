/**
 * Banking-related constants
 */

const BANKING_CONSTANTS = {
  // Component behavior config
  USA_BANK_ACCOUNT_MAX: 17,
  USA_ACH_ROUTING_NUM_MAX: 9,

  // Transaction types
  contribution: 'Contribution',
  payout: 'Payout',
} as const

const DEFAULT_VALUES = {
  country: 'USA',
  accountType: 'Checking',
  transferType: 'Domestic',
} as const

const statementTypes = [
  {
    value: 'Contribution',
    label: 'PAYIN_HISTORY',
  },
  {
    value: 'Payout',
    label: 'BANKING.PAYOUT_HISTORY_GROUP_TITLE',
  },
  {
    value: 'All',
    label: 'ALL_STATEMENTS',
  },
] as const

export { BANKING_CONSTANTS, DEFAULT_VALUES, statementTypes }
