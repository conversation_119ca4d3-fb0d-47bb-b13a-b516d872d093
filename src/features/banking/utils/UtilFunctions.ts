import dayjs from 'dayjs'
import { HistoryTransaction } from '../types/BankTypes.type'

/**
 * First occurrence of a transaction
 */
const transactionStartAndEnd = (transactions: Array<HistoryTransaction>) => {
  if (!transactions?.length) return {}

  const mostRecentTransactionDate = dayjs(transactions?.[0].transaction?.time)
  const oldestTransactionDate = dayjs(
    transactions?.[transactions?.length - 1].transaction?.time
  )

  return {
    start: oldestTransactionDate.startOf('month').format('YYYY-MM-DD'),
    end: mostRecentTransactionDate.endOf('month').format('YYYY-MM-DD'),
  }
}

const combinedFilters = ({
  filterType,
  array,
  filterBy,
}: {
  filterType: "type'"
  array: Array<HistoryTransaction>
  filterBy: string
}) => {
  if (filterBy === 'All') {
    return array
  }
  return array?.filter((item) => item?.transaction?.[filterType] === filterBy)
}

export { transactionStartAndEnd, combinedFilters }
