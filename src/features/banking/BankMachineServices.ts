import axios from 'axios'
import { API } from '../../common/api/API'
import { axiosConfig } from '../../common/api/RequestConfig'
import { API_STATUS } from '../../common/constants/ApiErrors'
import { writeToConsoleAndIssueAlert } from '../../common/utils/UtilFunctions'
import { getAuthToken } from '../authentication/utils/AuthFunctions'
import { SettledPromise } from './types/Banking.types'
import {
  BankMachineContext,
  BankMachineEvent,
  ForecastRules,
} from './types/BankMachineTypes.type'
import {
  BankingInfo,
  HistoryTransaction,
  NextPayoutItem,
} from './types/BankTypes.type'

/**
 * Hack until the cookie update is done or removed
 */
const withCredentials = false

/**
 * Check if promise status if fulfilled and returns the data, if the promise
 * is rejected then returns undefined
 */
const getData = (statusObject: SettledPromise) =>
  statusObject.status === 'fulfilled' ? statusObject.value.data : []

/**
 * Gets the the Fed rate and data
 */
const getReturns = async (_: BankMachineContext, event: BankMachineEvent) => {
  try {
    const { data, status } = (await axios.get(
      API.forecastRules,
      axiosConfig({
        signal: event?.payload?.abortController?.signal,
        withCredentials,
        authToken: getAuthToken(),
      })
    )) as unknown as {
      data: ForecastRules
      status: number
    }

    const modifiedData = {
      forecastRules: data,
      invAccOpen: event?.payload?.invAccOpen,
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(modifiedData)
      return modifiedData
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Fetches all possible banking info. Currently multiple APIs are called in
 * parallel using `Promise.allSettled` until this call is merged into one API from the backend.
 */
const readUserBankingInfo = async (
  _: BankMachineContext,
  event: BankMachineEvent
): Promise<BankingInfo> => {
  /**
   * Binds all promise response into a return object that is used by the
   * bank machine context
   */
  const bindResponsesToObject = (
    nominalBalance: SettledPromise,
    nextPayout: SettledPromise
  ) => ({
    nominalBalance: getData(nominalBalance) as Array<HistoryTransaction>,
    payoutHistory: getData(nominalBalance) as Array<HistoryTransaction>,
    payinHistory: getData(nominalBalance) as Array<HistoryTransaction>,
    nextPayout: getData(nextPayout) as Array<NextPayoutItem> | null | undefined,
  })

  try {
    const controller = event?.payload?.abortController
    //The array destructing follows the order of the API calls in `allSettled`!
    const [
      nominalBalance,
      nextPayout,
      //All settled promise does not care if promise has been accepted or
      //rejected, as long as it has been fulfilled it will continue execution.
      //We want to use it like this in order to ensure for now if one API call
      //fails, not to stop execution for all other API calls
    ] = await Promise.allSettled([
      axios.get(
        API.readUserNominalBalance,
        axiosConfig({
          signal: controller?.signal,
          withCredentials,
          authToken: getAuthToken(),
        })
      ),
      axios.get(
        API.readNextPayout,
        axiosConfig({
          signal: controller?.signal,
          withCredentials,
          authToken: getAuthToken(),
        })
      ),
    ])

    return bindResponsesToObject(nominalBalance, nextPayout)
  } catch (error) {
    // to skips spam alerts until other APIs are done in order to prevent client
    // errors
    writeToConsoleAndIssueAlert({
      error,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return {} as BankingInfo
}

export { getReturns, readUserBankingInfo }
