import { getAuthToken } from '../authentication/utils/AuthFunctions'
import { Events } from './types/BankMachineTypes.type'

const isAuthenticated = () => Boolean(getAuthToken())

/**
 * Checks if the user has an investment account open before fetching their
 * balance history
 */
const isInvestmentAccountOpen = ({ event }: { event: Events }): boolean => {
  const isAuthed = Boolean(getAuthToken())
  const hasInvestmentAccount = Boolean(
    event.output?.invAccOpen ?? event.payload?.invAccOpen
  )

  return isAuthed && hasInvestmentAccount
}

export { isAuthenticated, isInvestmentAccountOpen }
