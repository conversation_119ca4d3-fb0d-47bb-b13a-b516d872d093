import { useState } from 'react'
import Button from '../../../common/components/Button'
import Divider from '../../../common/components/Divider'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import style from '../style/AccessibilityControls.module.scss'
import { AccessibilityControlsProps } from '../types/Accessibility.types'
import AccessibilityOptions from './AccessibilityOptions'

/**
 * Allows the user to modify the font and theme of the app
 */
const AccessibilityControls = ({
  variant,
  position,
}: AccessibilityControlsProps) => {
  const [open, setOpen] = useState(false)

  return (
    <section
      className={`
        ${style['accessibility-controls']}
        ${style[`accessibility-controls--${position}`]}
    `}
    >
      <div
        className={`
         ${style[`accessibility-controls__options-container`]}
         ${style[`accessibility-controls--${variant}`]}
         ${style[`accessibility-controls__options${open ? '--open' : '--closed'}`]}
         `}
      >
        <Button
          variant={variant}
          onClick={() => setOpen(false)}
          trackActivity={{
            trackId: 'accessibility_close',
          }}
        >
          <Icon
            fileName={ASSET.icononboardinwhitearrowback}
            className={style['accessibility-controls__close-chevron']}
          />
        </Button>
        <Divider className={style['accessibility-controls__divider']} />
        <AccessibilityOptions variant={variant} />
        <Divider className={style['accessibility-controls__divider']} />
      </div>

      <Button
        variant={variant}
        onClick={() => setOpen((prev) => !prev)}
        className={
          style[
            `accessibility-controls__options-button${open ? '--open' : '--closed'}`
          ]
        }
        trackActivity={{
          trackId: open
            ? 'accessibility_open_menu'
            : 'accessibility_close_menu',
        }}
      >
        <Icon
          fileName={ASSET.accessibilityLogo}
          className={style['accessibility-controls__logo']}
        />
      </Button>
    </section>
  )
}

export default AccessibilityControls
