import Button from '../../../common/components/Button'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { useAccessibility } from '../hooks/useAccessibility'
import style from '../style/AccessibilityOptions.module.scss'
import { AccessibilityOptionsType } from '../types/Accessibility.types'

const FONT_SIZES = {
  megaBig: 5,
  default: 0,
}

/**
 * Accessibility option buttons to adjust font size, change theme and other
 * accessibility options. The last selected option is saved on user's browser
 */
const AccessibilityOptions = ({ variant }: AccessibilityOptionsType) => {
  const { settings, updateFontBy } = useAccessibility()

  return (
    <section
      className={`${style[`accessibility-options${variant ? `--${variant}` : ''}`]}`}
    >
      <Button
        variant="gray-dark"
        onClick={() => updateFontBy(FONT_SIZES.default)}
        trackActivity={{
          trackId: 'accessibility_font_size_default',
          value: FONT_SIZES.default,
        }}
      >
        <Icon
          fileName={
            settings.fontIncreaseBy === FONT_SIZES.default
              ? ASSET.accTextMinusSelected
              : ASSET.accTextMinus
          }
          className={style[`accessibility-options__icon`]}
        />
      </Button>
      <Button
        variant="gray-dark"
        onClick={() => updateFontBy(FONT_SIZES.megaBig)}
        trackActivity={{
          trackId: 'accessibility_font_size_increase',
          value: FONT_SIZES.megaBig,
        }}
      >
        <Icon
          fileName={
            settings.fontIncreaseBy === FONT_SIZES.megaBig
              ? ASSET.accTextPlusSelected
              : ASSET.accTextPlus
          }
          className={style[`accessibility-options__icon`]}
        />
      </Button>
    </section>
  )
}

export default AccessibilityOptions
