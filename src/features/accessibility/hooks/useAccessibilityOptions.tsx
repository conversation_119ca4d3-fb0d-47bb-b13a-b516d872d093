import { useState } from 'react'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { AccessibilitySettings } from '../../../common/types/CommonTypes.types'

/**
 * Sets a CSS variable to the passed in `pixels` value
 */
const setCssFontVar = (pixels: number) =>
  document.documentElement.style.setProperty('--font-modifier', `${pixels}px`)

const defaultAccessibilitySettings: AccessibilitySettings = {
  fontIncreaseBy: 0,
}

/**
 * Sets the accessibility settings and stores them in browser storage
 */
export const useAccessibilityOptions = () => {
  const { addValueToStorage, storedValue } = useBrowserStorage({
    key: 'accessibilitySettings',
  })

  const initFromBrowserStorage = () => {
    const retrievedSettings = storedValue as AccessibilitySettings
    if (retrievedSettings) {
      setCssFontVar(retrievedSettings.fontIncreaseBy)
      return retrievedSettings
    }
    return defaultAccessibilitySettings
  }

  const [settings, setSettings] = useState<AccessibilitySettings>(
    initFromBrowserStorage()
  )

  /**
   * Updates the settings state with a new fontIncreaseBy value and set's the
   * css variable for font to the passed in `pixels` value
   */
  const updateFontBy = (pixels: number) => {
    setSettings((prev) => {
      addValueToStorage({ ...prev, fontIncreaseBy: pixels })
      return { ...prev, fontIncreaseBy: pixels }
    })
    setCssFontVar(pixels)
  }

  return { settings, updateFontBy }
}
