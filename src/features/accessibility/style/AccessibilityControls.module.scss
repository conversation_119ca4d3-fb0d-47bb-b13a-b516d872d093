@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

$width-elements: 3.4375rem;
$chevron-size: 30px;
$logo-size: 35px;

/** @define accessibility-controls */
.accessibility-controls {
  width: $width-elements;

  &--bottom-left {
    position: fixed;
    bottom: 350px;
    left: 20px;
  }

  &__options-container {
    padding: 0.3125rem;
    border-top-left-radius: variables.$rounded;
    border-top-right-radius: variables.$rounded;
  }

  &--gray-dark {
    background-color: colors.$gray-dark;
    width: $width-elements;
  }

  &__options--closed {
    opacity: 0;
    transition: all 0.2s ease-in;
    pointer-events: none;
  }

  &__options--open {
    opacity: 1;
    transition: all 0.2s ease-in;
  }

  &__close-chevron {
    transform: rotate(270deg);
    width: $chevron-size;
    height: $chevron-size;
  }

  &__divider {
    margin: 5px 0;
  }

  &__options-button--open {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    cursor: pointer;
  }

  &__options-button--closed {
    border-top-left-radius: variables.$rounded;
    border-top-right-radius: variables.$rounded;
    cursor: pointer;
  }

  &__logo {
    width: $logo-size;
    height: $logo-size;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    display: none;
  }
}
