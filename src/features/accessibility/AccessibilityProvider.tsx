import { ReactNode } from 'react'
import { AccessibilityContext } from './AccessibilityContext'
import { useAccessibilityOptions } from './hooks/useAccessibilityOptions'

/**
 * Provides accessibility controls to the whole app
 */
const AccessibilityProvider = ({ children }: { children: ReactNode }) => {
  const { settings, updateFontBy } = useAccessibilityOptions()
  return (
    <AccessibilityContext.Provider
      value={{
        settings,
        updateFontBy,
      }}
    >
      {children}
    </AccessibilityContext.Provider>
  )
}

export default AccessibilityProvider
