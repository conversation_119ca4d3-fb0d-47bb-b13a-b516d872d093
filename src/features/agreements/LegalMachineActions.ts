import {
  AgreementData,
  LegalMachineContext,
  LegalMachineEvent,
  SubmissionResponse,
} from './types/LegalMachineTypes.types'

/**
 * Stores agreements in context on initial fetch
 */
const storeAgreements = {
  agreement: ({
    context,
    event,
  }: {
    context: LegalMachineContext
    event: LegalMachineEvent
  }) => {
    return { ...context?.agreement, ...event?.output } as AgreementData
  },
}

/**
 * Updates an agreement when a user agrees by adding a timestamp ISO to the
 * `userSigned` field
 */
const updateAgreement = {
  agreement: ({
    context,
    event,
  }: {
    context: LegalMachineContext
    event: LegalMachineEvent
  }) => {
    if (context?.agreement && event?.output?.agreementType) {
      // Time stamp when the agreement was signed
      // There will be a slight mismatch in mere seconds in case the user
      // refreshes the app and re-fetches the agreement but this is not na issue
      context.agreement[event?.output?.agreementType].userAgreed =
        new Date().toISOString()
    } else {
      console.warn(
        'Did not receive agreement type to update, returning original agreement context'
      )
    }

    return context.agreement
  },
}

const storeDocument = {
  investAccountDocument: ({
    event,
    context,
  }: {
    context: LegalMachineContext
    event: LegalMachineEvent
  }) => {
    return {
      ...context?.investAccountDocument,
      ...event?.output,
    } as SubmissionResponse
  },
}

export { storeAgreements, storeDocument, updateAgreement }
