import { writeToConsoleAndIssueAlert } from '../../../common/utils/UtilFunctions'
import {
  AgreementDataForInvestmentAccount,
  AgreementForInvestmentAccount,
} from '../types/LegalMachineTypes.types'

/**
 * `agreementIndex` is necessary to determine on which page the user is on
 * Parses agreement content and data into one object with different data types
 * to be used in the UI.
 */
const parseAgreementToIDAndData = ({
  agreementData,
  agreementToRenderIndex,
}: {
  agreementData: AgreementDataForInvestmentAccount
  agreementToRenderIndex: number
}) => {
  try {
    let checkboxInitialState = {} as {
      [key in AgreementForInvestmentAccount]: boolean
    }
    let agreementIDs: Array<AgreementForInvestmentAccount> = []

    Object.entries(agreementData).forEach((agreement) => {
      checkboxInitialState = {
        ...checkboxInitialState,
        [agreement[0]]: Boolean(agreement[1]?.userAgreed),
      }

      agreementIDs = [
        ...agreementIDs,
        // Not sure what TS is smoking, it is clearly defined that the agreement
        // type is not a string but of type AgreementForInvestmentAccount, so
        // for some reason need to assert
        agreement[0] as AgreementForInvestmentAccount,
      ]
    })

    const agreementID = agreementIDs[agreementToRenderIndex]
    const agreementContent = agreementData[agreementID]

    return {
      agreementID,
      agreementData: agreementData?.[agreementID],
      agreementContent,
      checkboxInitialState,
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      errorMessage: 'Parser catastrophic failure',
      skipRethrow: true,
    })
  }

  return {}
}

export { parseAgreementToIDAndData }
