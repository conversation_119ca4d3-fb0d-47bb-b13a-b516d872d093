@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define agreementContent */
.agreementContent {
  // FIXME:Need to have at least one css attribute to generate the main class, so
  // tests don't fail
  visibility: visible;
  &__image-container {
    @include mixins.flex-layout;
    margin-bottom: 3.125rem;
  }
  &__image {
    width: 400px;
    height: 300px;
    @media only screen and (max-width: variables.$mobile-devices) {
      width: 250px;
      height: 150px;
    }
  }

  &__content {
    margin-bottom: 1.875rem;
  }

  &__heading {
    @include mixins.font-style(
      $font-size: variables.$font-size-large,
      $font-weight: variables.$font-bold
    );
    margin-bottom: 1.25rem;
    text-align: center;
  }
}
