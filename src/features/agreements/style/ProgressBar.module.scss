@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

@mixin progress-bar(
  $width: 100%,
  $background-color: colors.$white,
  $value-color: colors.$green,
  $border-radius: 10px,
  $border: 2px solid colors.$green,
  $height: 10px,
  $transition: ease 0.5s
) {
  width: $width;
  border: $border;
  background: colors.$white;
  border-radius: $border-radius;
  height: $height;
  transition: $transition;

  //Safari
  &::-webkit-progress-bar {
    //Needs to be 100% for webkit otherwise it will be broken
    width: 100%;
    border-radius: $border-radius;
    background: colors.$white;
    transition: $transition;
  }

  &::-webkit-progress-value {
    width: $width;
    border-radius: $border-radius;
    background: $value-color;
  }

  //Chrome
  &[value]::-webkit-progress-bar {
    //Needs to be 100% for webkit otherwise it will be broken
    width: 100%;
    border-radius: $border-radius;
    background: colors.$white;
    transition: $transition;
  }

  &[value]::-webkit-progress-value {
    width: $width;
    border-radius: $border-radius;
    background: $value-color;
  }

  //Firefox specific values!!
  &[value]::-moz-progress-bar {
    transition: $transition;
    border-radius: $border-radius;
    background: $value-color;
  }
}

.progress-bar {
  @include progress-bar($width: 60px);
  &--expanded {
    @include progress-bar($width: 100%);
  }

  &--blue {
    @include progress-bar(
      $width: 100%,
      $value-color: colors.$blue,
      $border: 2px solid colors.$blue,
      $background-color: colors.$blue
    );
  }
}

.progress-bar--error {
  @include progress-bar(
    $width: 60px,
    $value-color: red,
    $border: 2px solid red,
    $background-color: red
  );
}
