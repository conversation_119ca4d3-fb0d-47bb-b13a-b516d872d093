@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define legalExplainerPage */
.legalExplainerPage {
  &__inner-container {
    @include mixins.flex-layout(column);
  }

  &__top-image {
    width: 345px;
    height: 300px;
  }

  &__title {
    @include mixins.font-style(
      $font-size: 27px,
      $font-weight: variables.$font-semibold
    );
    text-align: center;
    margin-bottom: 1.25rem;
    margin-top: 3.125rem;
  }

  &__content {
    @include mixins.font-style;
    margin-bottom: 1.25rem;
  }

  //Mobile devices scss
  @media only screen and (max-width: variables.$mobile-devices) {
    &__top-img {
      width: 188px;
      height: 165px;
    }
  }
}
