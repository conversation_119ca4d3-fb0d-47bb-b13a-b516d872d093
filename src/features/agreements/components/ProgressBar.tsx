import style from '../style/ProgressBar.module.scss'
import { ProgressBarProps } from '../types/ProgressBar.types'

/**
 * Progress bar
 */
const ProgressBar = ({
  max,
  value,
  onClick,
  className,
  expanded,
  variant,
  ...props
}: ProgressBarProps) => {
  return (
    <progress
      {...props}
      className={`
       ${style[`progress-bar${expanded ? '--expanded' : ''}`]}
       ${style[`progress-bar${variant ? `--${variant}` : ''}`]} 
       ${className ?? ''}
       `}
      max={max}
      value={value}
      onClick={onClick}
    />
  )
}

export default ProgressBar
