type AgreementContentProps = {
  onUserReadAgreement?: (hasRead: boolean) => void
  onAgreed?: ({
    checkboxChecked,
    agreementData,
  }: {
    checkboxChecked: boolean
    agreementData: unknown
  }) => void
  backgroundColor?: 'white'
  agreementData?: unknown
  agreementSigned?: boolean
  agreementImage: string
  agreementContent: string
  agreementHeading?: string
  checkboxLabel?: string
  agreementSigningError?: string
  readOnly?: boolean
}

export type { AgreementContentProps }
