import {
  Agreement,
  AgreementContents,
  AgreementData,
  InvestmentAccountFormType,
  TontineProduct,
} from './types/LegalMachineTypes.types'

/**
 * Converts an array of form types to a string that can be used as GET params
 */
const parsePayloadToParams = (
  formTypes: Array<Agreement | InvestmentAccountFormType>,
  product?: TontineProduct
) => {
  const delimiterForBackendParsing = ';'

  const withProduct = product ? `,${product}` : ''

  const productParams = formTypes.map(
    (InvestmentAccountFormType) => `${InvestmentAccountFormType}${withProduct}`
  )

  return `${productParams.join(delimiterForBackendParsing)}`
}

/**
 * Processes the response data for agreements and parses the response into a
 * flat object
 */
const processAndFlatAgreement = (
  data: Array<[Agreement, AgreementContents]>
): AgreementData | object => {
  let processedResponse: AgreementData | object = {}

  data?.forEach((agreement) => {
    let text = ''
    let image = ''

    agreement[1]?.content.forEach((content) => {
      if (content?.tag === 'Text') {
        text = content?.contents
      }

      if (content?.tag === 'Image') {
        image = content?.contents
      }
    })

    const title = agreement[1].title
    const version = agreement[1].version
    const userAgreed = agreement[1].userAgreed

    processedResponse = {
      ...processedResponse,
      // 0 is the agreement type
      // 1 is the contents
      [agreement[0]]: {
        text,
        image,
        title,
        version,
        userAgreed,
      },
    }
  })

  return processedResponse
}

export { parsePayloadToParams, processAndFlatAgreement }
