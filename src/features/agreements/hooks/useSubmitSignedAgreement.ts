import { useState } from 'react'
import { debounce } from '../../../common/utils/UtilFunctions'
import { DASHBOARD_CONSTANTS } from '../../dashboard/utils/consts'
import {
  AgreementData,
  AgreementForInvestmentAccount,
} from '../types/LegalMachineTypes.types'
import { useLegalMachine } from './useLegalMachine'

/**
 * Submits the signed agreement and updates the state of the checkbox if the
 * agreement signing has failed
 */
export const useSubmitSignedAgreement = ({
  goToNextAgreement,
}: {
  goToNextAgreement?: () => void
}) => {
  const [errorSign, setErrorSign] = useState<
    { [key: string]: string } | undefined
  >()

  const { sendLegalEvent, currentLegalState, states } = useLegalMachine()

  const handleSigning = ({
    agreementType,
    agreementSignedData,
  }: {
    agreementType?: AgreementForInvestmentAccount
    agreementSignedData?: {
      checkboxChecked: boolean
      agreementData: AgreementData
    }
  }) => {
    const debouncedSendLegal = debounce(
      sendLegalEvent,
      DASHBOARD_CONSTANTS.DEBOUNCE_SLIDERS_TIME
    )

    // Reset state error
    setErrorSign(undefined)

    if (agreementSignedData) {
      debouncedSendLegal({
        type: 'SIGN_AGREEMENT',
        payload: {
          agreementType,
          signedAgreementData: {
            checkboxChecked: agreementSignedData?.checkboxChecked,
            signedAgreementContents: agreementSignedData?.agreementData,
          },
          successCallback: () => {
            goToNextAgreement?.()
          },
          failureCallback: () => {
            // Can be modified, for now don't have any design...
            setErrorSign({ [agreementType as string]: agreementType as string })
          },
        },
      })
    }
  }

  return {
    handleSigning,
    isLoading: currentLegalState === states.SIGNING_AGREEMENT,
    errorSign,
  }
}
