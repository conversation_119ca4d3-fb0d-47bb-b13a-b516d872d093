import { Dispatch, SetStateAction } from 'react'
import { COMMON_CONSTANTS } from '../../../common/utils/consts'
import { RenderPageStates } from '../types/CommonLegalTypes.types'
import { useFormPageController } from './useFormPageController'

/**
 * Controls the agreements flow. Saves the agreement page that the user has left from
 */
export const useAgreementsPage = ({
  setShouldRenderPage,
  agreementCount,
}: {
  setShouldRenderPage: Dispatch<SetStateAction<RenderPageStates>>
  agreementCount: number
}) => {
  const { formPageNumber, forwardOnePage, backOnePage } = useFormPageController(
    {
      key: COMMON_CONSTANTS.FORM_AGREEMENTS_KEY,
      pageCount: agreementCount,
    }
  )

  const goToNextAgreement = () => {
    if (formPageNumber + 1 !== agreementCount) {
      forwardOnePage()
    } else {
      setShouldRenderPage({
        investmentForm: true,
      })
    }
  }
  const handleBackButton = () => {
    //If on first form page then the user can go back to the entry page if they
    //wish so
    if (formPageNumber === 0) {
      setShouldRenderPage({ entryPage: true })
    } else {
      backOnePage()
    }
  }

  return {
    handleBackButton,
    goToNextAgreement,
    formPageNumber,
  }
}
