import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { UseFormPageProps } from '../types/useSaveFieldsOnType.types'

/**
 * Controls and persists from page number in local storage
 */
export const useFormPageController = ({ key, pageCount }: UseFormPageProps) => {
  const browserStorage = useBrowserStorage({
    initialValue: 0,
    key,
  })

  const { addValueToStorage, storedValue, removeValueFromStorage } =
    browserStorage as {
      addValueToStorage: (value: number) => void
      storedValue: number
      removeValueFromStorage: (key: string) => void
    }

  /**
   * Checks if the chosen index is withing range, if not returns the max index
   */
  const withinProgressRange = (
    currentIndex: number,
    maxLength: number
  ): number => {
    const maxArrayLength = maxLength - 1

    if (currentIndex >= 0 && currentIndex < maxArrayLength) {
      return currentIndex
    }

    return currentIndex
  }

  /**
   * Checks if the given page number is valid and takes the user to a specific
   * form page
   */
  const gotoPage = (pageNumber: number, maxPages: number) =>
    withinProgressRange(pageNumber, maxPages)

  const setFormPage = (pageNumber: number) => addValueToStorage(pageNumber)

  const forwardOnePage = () => setFormPage(gotoPage(storedValue + 1, pageCount))
  const backOnePage = () => setFormPage(gotoPage(storedValue - 1, pageCount))

  return {
    formPageNumber: storedValue,
    setFormPage,
    removeValueFromStorage,
    forwardOnePage,
    backOnePage,
  }
}
