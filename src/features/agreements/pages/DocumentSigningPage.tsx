import { DocusealForm } from '@docuseal/react'
import { useEffect, useState } from 'react'
import Button from '../../../common/components/Button'
import LottieAnimation from '../../../common/components/LottieAnimation'
import PageLayout from '../../../common/components/PageLayout'
import TextError from '../../../common/components/TextError'
import { ANIMATION } from '../../../common/constants/Animations'
import { useTranslate } from '../../../common/hooks/useTranslate'
import i18n from '../../../config/i18n'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useLegalMachine } from '../hooks/useLegalMachine'

const DocumentSigningPage = () => {
  const { sendLegalEvent, legalContext } = useLegalMachine()
  const {
    context: { user_details },
  } = useAccountService()
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState<string | undefined>()
  const t = useTranslate()

  const fetchDocument = () => {
    sendLegalEvent({
      type: 'FETCH_DOCUMENT_EMBED_URL',
      payload: {
        submissionData: {
          template_id: 'BRA-SHOWCASE',
        },

        failureCallback: (error) => setError(error?.translatedError),
      },
    })
  }

  useEffect(() => {
    fetchDocument()
  }, [])

  return (
    <PageLayout containerHeight="xlh">
      {!isLoaded && !error && (
        <LottieAnimation
          animationName={ANIMATION.loadingLightBlueDots}
          autoplay
          loop
        />
      )}
      {error ? (
        <>
          <TextError errorText={error} />
          <br />
          <br />
          <Button
            onClick={() => {
              setError(undefined)
              setIsLoaded(false)
              fetchDocument()
            }}
          >
            {t('COMMON.TRY_AGAIN_BUTTON')}
          </Button>
        </>
      ) : (
        <DocusealForm
          src={legalContext?.investAccountDocument?.user[0].embedSrc}
          email={user_details?.email}
          language={i18n.language.split('-')?.[0] ?? i18n.language}
          onLoad={() => setIsLoaded(true)}
        />
      )}
    </PageLayout>
  )
}

export default DocumentSigningPage
