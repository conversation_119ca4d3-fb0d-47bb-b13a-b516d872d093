import { useState } from 'react'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useTranslate } from '../../../common/hooks/useTranslate'
import AgreementContent from '../components/AgreementContent'
import { useAgreementsPage } from '../hooks/useAgreementsPage'
import { useLegalMachine } from '../hooks/useLegalMachine'
import { useSubmitSignedAgreement } from '../hooks/useSubmitSignedAgreement'
import { AgreementPageProps } from '../types/AgreementPage.types'
import { AgreementData } from '../types/LegalMachineTypes.types'
import { parseAgreementToIDAndData } from '../utils/UtilFunctions'

/**
 * Renders an agreement pages with the given agreement data.
 * The user can sign an agreement by checking the checkbox.
 */
const AgreementPage = ({
  setShouldRenderPage,
  agreementData,
}: AgreementPageProps) => {
  const t = useTranslate()

  const { currentLegalState, states } = useLegalMachine()

  const { goToNextAgreement, handleBackButton, formPageNumber } =
    useAgreementsPage({
      setShouldRenderPage,
      agreementCount: Object.entries(agreementData).length,
    })

  const { agreementID, agreementContent, checkboxInitialState } =
    parseAgreementToIDAndData({
      agreementData,
      // Renders one agreement on page from the agreements data
      agreementToRenderIndex: formPageNumber,
    })

  const [agreementCheckboxes, setAgreementCheckboxes] =
    useState(checkboxInitialState)

  const { handleSigning, errorSign } = useSubmitSignedAgreement({
    goToNextAgreement,
  })

  return (
    <Layout
      onClickAction={handleBackButton}
      pageTitle={agreementContent?.title ?? ''}
      bottomSection={
        <NavigationButtons
          onClickFirst={handleBackButton}
          secondButtonLabel={t('BUTTON_LABEL.NEXT')}
          onClickSecond={goToNextAgreement}
          // Prevents the user from going to the next page
          // if the previous agreement is not submitted successfully
          disabledSecond={
            currentLegalState !== states.SIGNING_AGREEMENT &&
            !checkboxInitialState?.[agreementID ?? '']
          }
          secondButtonLoading={currentLegalState === states.SIGNING_AGREEMENT}
        />
      }
    >
      <AgreementContent
        backgroundColor="white"
        agreementSigningError={errorSign?.[agreementID ?? '']}
        agreementHeading={agreementContent?.heading ?? ''}
        agreementImage={agreementContent?.image ?? ''}
        agreementContent={agreementContent?.text ?? ''}
        agreementSigned={
          agreementID ? agreementCheckboxes?.[agreementID] : false
        }
        onAgreed={
          // Do not include the handler if the agreement has been signed
          agreementID && agreementCheckboxes?.[agreementID]
            ? undefined
            : (agreementData) => {
                // Makes the API request to sign the
                handleSigning({
                  agreementType: agreementID,
                  agreementSignedData: agreementData as {
                    checkboxChecked: boolean
                    agreementData: AgreementData
                  },
                })

                // Sets the checkbox state to true to visually indicate to the user
                // that the agreement has been signed
                setAgreementCheckboxes((previousState) => {
                  if (previousState) {
                    return {
                      ...previousState,
                      [agreementID ?? '']: agreementData.checkboxChecked,
                    }
                  }
                  // If previous state is somehow undefined, no idea how that can
                  // happen, so we return the default state
                  return previousState
                })
              }
        }
        agreementData={agreementData}
      />
    </Layout>
  )
}

export default AgreementPage
