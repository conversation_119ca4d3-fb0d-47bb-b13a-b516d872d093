import { ScaleLinear } from 'd3'
import { chart } from '../D3Chart'
import { Styling } from '../Styling'
import {
  DrawBreakevenDotsParams,
  InflationAreaParams,
  RenderAnnotationsParams,
} from '../types/D3Chart.types'
import {
  BreakevenAges,
  ForecastPayout,
  ForecastResult,
  LineChartLines,
} from '../types/Visualization.types'
import {
  adjustBreakevenCircleOverlap,
  adjustForInflation,
  alphaStringId,
  annotationValue,
  calculateBreakevenAges,
  dateParser,
} from './D3DataUtils'

/**
 * Trims the forecast results response from selected age, the selected age will
 * be included in the modified array.
 *
 * @note Modified the original array!
 */
const trimDataFrom = (
  multipleLinePayouts: Array<Array<ForecastPayout>>,
  cutFromAge: number
) => {
  multipleLinePayouts.forEach((line: Array<ForecastPayout>) => {
    const index = line.findIndex((item) => {
      return item.age.years === cutFromAge
    })
    if (index !== -1) {
      // We cut from found element + 1
      // So for array 99,100,101
      line.splice(index + 1)
    }
  })
}

/**
 * Returns a new array that serves as a relative point to the area line above
 * the bottom line
 */
const calculateCutAreaForLine = (
  forecastData: Array<ForecastResult>,
  yScale: ScaleLinear<number, number, never>
): Array<number> => {
  if (!forecastData || forecastData?.length < 2) {
    return []
  }
  //Chooses which forecast line should be scaled by
  const index =
    forecastData[0].stats.total_payouts > forecastData[1].stats.total_payouts
      ? 1
      : 0

  return forecastData[index].results.payouts.map((payout: ForecastPayout) =>
    yScale(payout.tontine.amount)
  )
}

/**
 * Cuts off area where the line below the area starts. Area with the lowest
 * payout amount goes at the bottom so it can safely use the whole's chart
 * height. Otherwise the minimum line payouts are used to cut off the chart area
 * for the line above the minimum line.
 *
 * Returns an iterator function that iterates trough the CURRENT line's function
 */
const cutLineAreaUsingRelativeLine = ({
  lineWithLowestTotalPayouts,
  currentLinePayout,
  chartHeight,
  relativeAreaToCutFrom,
  lineWithHighestTotalPayouts,
  currentPayoutLineLength,
}: {
  lineWithLowestTotalPayouts: number
  currentLinePayout: number
  chartHeight: number
  relativeAreaToCutFrom: Array<number>
  lineWithHighestTotalPayouts: number
  currentPayoutLineLength: number
}):
  | number
  | ((_: ForecastPayout, currentLinePayoutIndex: number) => number) => {
  //Bottom line found, can safely extend area using the charts full height
  if (lineWithLowestTotalPayouts === currentLinePayout) {
    return chartHeight
  }

  //THIS RETURNS A ITERATOR FUNCTION!!!!
  if (lineWithHighestTotalPayouts === currentLinePayout) {
    return (_: ForecastPayout, currentLinePayoutIndex: number) => {
      //We need to remove the starting elements from the relative line in order
      //for the current line area to match the relative's line area
      const modifiedRelativeLine: Array<number> = relativeAreaToCutFrom.slice(
        relativeAreaToCutFrom?.length - currentPayoutLineLength,
        relativeAreaToCutFrom?.length
      )

      //Temporary fix, but here we need to add MAX_LINE[min_line]
      if (relativeAreaToCutFrom?.length < currentPayoutLineLength) {
        return chartHeight
      }

      return modifiedRelativeLine[currentLinePayoutIndex]
    }
  }

  return chartHeight
}

/**
 * Lift the annotation values in order to prevent overlapping in two lines have
 * a very small difference in total payouts
 */
const liftAnnotationYValue = (
  lineDifferenceInPercentage: number,
  minThreshold: number,
  maxThreshold: number,
  lineWithHighestTotalPayouts: number,
  currentLinePayout: number
): number => {
  if (
    lineDifferenceInPercentage > minThreshold &&
    lineDifferenceInPercentage <= maxThreshold
  ) {
    if (lineWithHighestTotalPayouts === currentLinePayout) {
      return -25
    }
  }
  return 0
}

/**
 * Calculates the best place to place an annotation between to annotations,
 * taking into account the space between the two annotations and returning the
 * calculated space added from the first's annotation position.
 */
const calcBestPositionToPlaceDepositAnnotationBetweenTwoAnnotations = (
  annotationOne: number,
  annotationTwo: number
) => {
  const stepsBetweenFirstAndSecondAnnotation = annotationTwo - annotationOne

  const middleOfFreeSpace = Math.floor(stepsBetweenFirstAndSecondAnnotation / 2)

  return annotationOne + middleOfFreeSpace
}

/**
 * Returns appropriate dataset to be used in hovering annotations, based on the
 * `toggles` values
 */
const hoveringAnnotationDataset = ({
  lineData,
  inflation,
}: {
  inflation: boolean
  lineData: LineChartLines
}): string => {
  if (inflation) {
    return `${lineData}.amount_inflation_adjusted`
  }

  return `${lineData}.amount`
}

/**
 * Iterates trough the payouts array and breaks from the loop as soon as a 0 is
 * detected. Creates a new array with the last element being a 0 in order for D3
 * to render a line that is crashing down to 0
 */
const modifyResultsForDepositAccount = (
  payouts: Array<ForecastPayout>
): Array<ForecastPayout> => {
  let crashingArray: Array<ForecastPayout> = []

  for (let i = 0; i < payouts.length; i++) {
    if (payouts[i].deposit_account.amount === 0) {
      crashingArray = [...crashingArray, payouts[i]]
      break
    }
    crashingArray = [...crashingArray, payouts[i]]
  }

  return crashingArray
}

/**
 * Generates an array of ages where annotations should be placed on, only the
 * start age is needed, which is the retirement age
 */
const agesToPlaceAnnotationsOn = (retirementAge: number): Array<number> => {
  const maxAge = 100
  //What the percentage increment should annotations be placed on
  const incrementOn = 3
  const ageAnnotations: Array<number> = [retirementAge]

  //Generates an array of age annotations
  while (Math.max(...ageAnnotations) < maxAge) {
    //Calculates where on what age should an annotation be placed on The
    //annotations are placed by 33% percent increment
    const ageToPlaceAnnotationOn = Math.round(
      Math.max(...ageAnnotations) + (maxAge - retirementAge) / incrementOn
    )

    ageAnnotations.push(ageToPlaceAnnotationOn)
  }

  const lastArrayElement = ageAnnotations.length - 1

  //Remove the last element from the array if it's greater than the maxAge
  //Because the last element is 101 in the ageAnnotations, and we don't want
  //place an annotation on 101
  if (ageAnnotations[lastArrayElement] > maxAge) {
    ageAnnotations.pop()
    ageAnnotations.push(maxAge)
  }

  //Removes 99 from the generated array
  return ageAnnotations.filter((number) => number !== maxAge - 1)
}

/**
 * Renders static annotations for tontine lines, annuities and bank deposit
 */
const calculateAndRenderAnnotations = ({
  payouts,
  xScaleDate,
  yScale,
  forecastData,
  formatter,
  toggles,
  tontineLineStyle,
  fixedAnnuityStyle,
  bankDepositsStyle,
  svg,
  liftAnnotationByY = 0,
  chartWidth = 0,
  mainSVGContainerId,
  hideMiddleAnnotations,
}: RenderAnnotationsParams) => {
  /*Brings the annotation closer to the line it is placed on */
  const annotationsYAdjustmentCloserToLine = 5
  const lastAgeOnAxis = 100
  const retirementAge: number = payouts[0]?.age?.years
  const chartWidthThreshold = 600
  const hideMiddleAnnotationsThreshold = 8

  /**
   * If breakeven is toggled then we only place annotations on 1st and last age
   * on the X axis
   */
  let tontineAnnotationOnAge: Array<number> =
    toggles?.breakeven && toggles?.annuityLine && toggles?.depositLine
      ? [retirementAge, lastAgeOnAxis]
      : agesToPlaceAnnotationsOn(retirementAge)

  let depositAccountAnnotationOnAge = [...tontineAnnotationOnAge]

  //Places an annotation on age 95 for annuity
  const annuityAnnotationOnAge = [95]
  const annotationMaxLengthForAdjustment = 6

  if (
    // Distance between the middle callouts is less than the threshold that
    // means they will overlap so we hide them
    tontineAnnotationOnAge[2] - tontineAnnotationOnAge[1] <=
      hideMiddleAnnotationsThreshold &&
    hideMiddleAnnotations
  ) {
    tontineAnnotationOnAge = [retirementAge, lastAgeOnAxis]
  }

  //Tontine lines annotations
  payouts?.forEach((payout: ForecastPayout): void => {
    const currentAge: number = payout.age.years

    //Check is made where to place a tontine payout annotation
    if (tontineAnnotationOnAge.includes(currentAge)) {
      //Removes the element from the array in order to avoid placing duplicate
      //annotations at the same age, because the payout age is a whole number
      //and an array looking like 65,65,65 can appear because months are not
      //taken into account
      const elementAtIndex = tontineAnnotationOnAge.indexOf(currentAge)

      if (elementAtIndex !== -1) {
        tontineAnnotationOnAge.splice(elementAtIndex, 1)
      }

      /**
       * Adjusted yScale
       */
      const yScaleRaw =
        yScale(
          adjustForInflation(
            payout?.tontine?.amount,
            payout?.tontine.amount_inflation_adjusted,
            toggles?.inflation
          )
        ) + annotationsYAdjustmentCloserToLine

      const modifiedYScale =
        // Modifies the yScale only for the last annotation
        currentAge === lastAgeOnAxis ? yScaleRaw : yScaleRaw + liftAnnotationByY

      chart?.addAnnotation({
        chartSvg: svg,
        color: tontineLineStyle?.color ?? '',
        mainSVGContainerId: mainSVGContainerId ?? '',
        xScale: xScaleDate(dateParser(payout?.date)),
        yScale: modifiedYScale,
        text: annotationValue({
          percentage: toggles?.percent,
          value: payout.tontine.amount,
          inflation: toggles?.inflation,
          inflationAmount: payout?.tontine.amount_inflation_adjusted,
          forecastData,
          dataKey: 'tontine',
          formatter,
          payoutOnAnnotation: payout,
        })?.toString(),
        id: `${alphaStringId()}-tontine-ann${mainSVGContainerId}`,
        textColor: tontineLineStyle?.textColor || 'white',
        transitionDuration: Styling.transitionDurationMilliseconds,
        firstAnnotation: retirementAge === currentAge,
        lastAnnotation: currentAge === lastAgeOnAxis,
      })
    }
  })

  //Fixed annuity annotation
  payouts?.forEach((payout: ForecastPayout): void => {
    if (
      annuityAnnotationOnAge.includes(payout?.age?.years) &&
      fixedAnnuityStyle?.color !== Styling.hidden
    ) {
      annuityAnnotationOnAge.pop()

      chart?.addAnnotation({
        chartSvg: svg,
        mainSVGContainerId: mainSVGContainerId ?? '',
        color: fixedAnnuityStyle?.color ?? '',
        xScale: xScaleDate(dateParser(payout?.date)),
        yScale:
          yScale(
            adjustForInflation(
              payout?.annuity?.amount,
              payout?.annuity.amount_inflation_adjusted,
              toggles?.inflation
            )
          ) +
          annotationsYAdjustmentCloserToLine +
          liftAnnotationByY,

        text: annotationValue({
          percentage: toggles?.percent,
          value: payout?.annuity.amount,
          inflation: toggles?.inflation,
          inflationAmount: payout?.annuity.amount_inflation_adjusted,
          forecastData,
          dataKey: 'annuity',
          formatter,
          payoutOnAnnotation: payout,
        })?.toString(),
        id: `${alphaStringId()}-annuity-ann${mainSVGContainerId}`,
        textColor: fixedAnnuityStyle?.textColor,
        transitionDuration: Styling.transitionDurationMilliseconds,
      })
    }
  })

  //Filter out zero values, we don't want to place annotations on 0 values
  const filteredPayouts = payouts?.filter(
    (payout) => payout.deposit_account.amount > 0
  )

  const firstTontineAnnotation = depositAccountAnnotationOnAge?.[0]
  const secondTontineAnnotation = depositAccountAnnotationOnAge?.[1]
  const thirdTontineAnnotation = depositAccountAnnotationOnAge?.[2]
  const fourthTontineAnnotation = depositAccountAnnotationOnAge?.[3]

  /**
   * Detects on what age does the deposit account line crash, meaning that is
   * how long the deposit account line is
   */
  const depositAccountLineMaxLength =
    filteredPayouts[filteredPayouts?.length - 1]?.age?.years

  //First free space between very first TT annotation and last
  let firstBestPosition =
    calcBestPositionToPlaceDepositAnnotationBetweenTwoAnnotations(
      firstTontineAnnotation,
      secondTontineAnnotation
    )

  //Moves the deposit account annotation by +1 in order not to cause overlap
  //with first modified TT annotation
  firstBestPosition = firstBestPosition + 1

  if (firstBestPosition > depositAccountLineMaxLength) {
    firstBestPosition = depositAccountLineMaxLength
  }

  const secondBestPosition =
    calcBestPositionToPlaceDepositAnnotationBetweenTwoAnnotations(
      secondTontineAnnotation,
      thirdTontineAnnotation
    )

  //Last free space between third and fourth TT annotation
  let thirdBestPosition =
    calcBestPositionToPlaceDepositAnnotationBetweenTwoAnnotations(
      thirdTontineAnnotation,
      fourthTontineAnnotation
    )

  //Moves the deposit account annotation by -1 in order not to cause overlap
  //with last modified TT annotation
  thirdBestPosition = thirdBestPosition - 1

  if (
    firstBestPosition > firstTontineAnnotation &&
    firstBestPosition <= secondTontineAnnotation
  ) {
    depositAccountAnnotationOnAge = [firstBestPosition]
  }

  if (
    secondBestPosition > secondTontineAnnotation &&
    secondBestPosition <= thirdTontineAnnotation &&
    secondTontineAnnotation > depositAccountLineMaxLength &&
    thirdTontineAnnotation <= depositAccountLineMaxLength
  ) {
    depositAccountAnnotationOnAge = [secondBestPosition]
  }

  if (
    thirdBestPosition > thirdTontineAnnotation &&
    thirdBestPosition <= depositAccountLineMaxLength &&
    thirdTontineAnnotation > depositAccountLineMaxLength &&
    fourthTontineAnnotation <= depositAccountLineMaxLength
  ) {
    depositAccountAnnotationOnAge = [thirdBestPosition]
  }

  //If the deposit account annotation is placed next to the annuity annotation
  //as an edge case, then push it back 5 years in order to avoid a collision
  if (depositAccountAnnotationOnAge?.[0] === annuityAnnotationOnAge?.[0]) {
    depositAccountAnnotationOnAge[0] = depositAccountAnnotationOnAge[0] - 3
  }

  //Deposit line annotation
  filteredPayouts?.forEach((payout: ForecastPayout): void => {
    if (
      depositAccountAnnotationOnAge.includes(payout?.age?.years) &&
      bankDepositsStyle?.color !== Styling.hidden &&
      //Does not show this annotation if breakeven is toggled
      !toggles.breakeven
    ) {
      //Remove all elements from the array and place the annotation only once
      depositAccountAnnotationOnAge.pop()

      const annotationText = annotationValue({
        percentage: toggles?.percent,
        value: payout.deposit_account.amount,
        inflation: toggles?.inflation,
        inflationAmount: payout.deposit_account.amount_inflation_adjusted,
        forecastData,
        dataKey: 'deposit_account',
        formatter,
        payoutOnAnnotation: payout,
      })

      /**
       * If the text inside the annotation is very long then push the whole
       * annotation at the bottom of the of the line. Otherwise check the
       * chart's width if it is too small to fit the annotation
       */
      const adjustAnnotationToBeUnderTheLineInPx =
        annotationText?.toString()?.length >=
          annotationMaxLengthForAdjustment || chartWidth <= chartWidthThreshold
          ? 50
          : 0

      chart?.addAnnotation({
        chartSvg: svg,
        mainSVGContainerId: mainSVGContainerId ?? '',
        color: bankDepositsStyle?.color ?? '',
        xScale:
          xScaleDate(dateParser(payout?.date)) +
          annotationsYAdjustmentCloserToLine,
        yScale:
          yScale(
            adjustForInflation(
              payout.deposit_account?.amount,
              payout.deposit_account.amount_inflation_adjusted,
              toggles?.inflation
            )
          ) +
          adjustAnnotationToBeUnderTheLineInPx +
          annotationsYAdjustmentCloserToLine,
        text: annotationText?.toString(),
        id: `${alphaStringId()}-deposit-acc-ann${mainSVGContainerId}`,
        textColor: fixedAnnuityStyle?.textColor,
        transitionDuration: Styling.transitionDurationMilliseconds,
        tipOnTop: adjustAnnotationToBeUnderTheLineInPx > 0,
      })
    }
  })
}

/**
 * Draws an inflation area on top of a line chart, when inflation is applied to
 * the lines
 */
const inflationAreaOnTop = ({
  inflation,
  height,
  yScaledData,
}: InflationAreaParams) => (inflation ? yScaledData : height)

/**
 * Adds a breakeven dot to each line of the chart at once. The breakeven dot on
 * the line is calculated by the `calculateBreakevenDot` util function from
 * `D3DataUtils.ts`
 */
const drawBreakevenDots = ({
  svg,
  xScaleDate,
  yScale,
  payouts,
  tontineLineStyle,
  fixedAnnuityStyle,
  bankDepositsStyle,
  showBreakevenDots,
  toggles,
}: DrawBreakevenDotsParams): void => {
  //Searches the payouts breakeven age for each payout type
  const { tontine, annuities, depositAccount }: BreakevenAges =
    calculateBreakevenAges(payouts)

  const { tontineAdjustment, depositAccountAdjustment, annuityAdjustment } =
    adjustBreakevenCircleOverlap(
      tontine.breakevenAge,
      depositAccount.breakevenAge,
      annuities.breakevenAge,
      toggles
    )

  if (tontine?.breakevenAmount > 0) {
    chart.drawCircleOnLine({
      chartSvg: svg,
      xScaledPosition: xScaleDate(tontine.breakevenDate),
      yScaledPosition: yScale(tontine.breakevenAmount) + tontineAdjustment,
      textInCircle: tontine?.breakevenAge?.toString(),
      circleID: `tontine-breakeven-age`,
      stylingOptions: tontineLineStyle ?? {},
      hideDot: showBreakevenDots,
    })
  }

  if (annuities?.breakevenAmount > 0) {
    chart.drawCircleOnLine({
      chartSvg: svg,
      xScaledPosition: xScaleDate(annuities.breakevenDate),
      yScaledPosition: yScale(annuities.breakevenAmount) + annuityAdjustment,
      textInCircle: annuities?.breakevenAge?.toString(),
      circleID: 'annuity-breakeven-age',
      stylingOptions: fixedAnnuityStyle ?? {},
      hideDot: showBreakevenDots || !toggles?.annuityLine,
    })
  }

  if (depositAccount?.breakevenAmount > 0) {
    chart.drawCircleOnLine({
      chartSvg: svg,
      xScaledPosition: xScaleDate(depositAccount.breakevenDate),
      yScaledPosition:
        yScale(depositAccount.breakevenAmount) + depositAccountAdjustment,
      textInCircle: depositAccount?.breakevenAge?.toString(),
      circleID: 'deposit-account-breakeven-age',
      stylingOptions: bankDepositsStyle ?? {},
      hideDot: showBreakevenDots || !toggles?.depositLine,
    })
  }
}

export {
  calculateAndRenderAnnotations,
  calculateCutAreaForLine,
  cutLineAreaUsingRelativeLine,
  drawBreakevenDots,
  hoveringAnnotationDataset,
  inflationAreaOnTop,
  liftAnnotationYValue,
  modifyResultsForDepositAccount,
  trimDataFrom,
}
