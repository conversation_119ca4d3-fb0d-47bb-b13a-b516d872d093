import { Selection, select, selectAll } from 'd3'
import { Styling } from '../Styling'
import {
  ComparisonLineParams,
  FocusCircleParams,
  RectangleWithTextParams,
  TextBoundaryBox,
  UnknownBaseType,
} from '../types/Utils.types'

/**
 * Generates a SVG circle with passed in styling properties
 */
const generateFocusCircle = ({
  chartSvg,
  id,
  styling,
  radius,
  hidden,
}: FocusCircleParams) => {
  if (selectAll(`.focus-${id}`)) {
    selectAll(`.focus-${id}`).remove()
  }

  return chartSvg
    .append('circle')
    .attr('class', `focus-${id}`)
    .attr('r', radius || Styling.focusCircles.radius)
    .style('fill', styling?.backgroundColor || styling?.color)
    .style(
      'stroke',
      //If any color is passed in then use white stroke, otherwise hide stroke
      styling?.backgroundColor || styling?.color
        ? Styling.focusCircles.stroke
        : Styling.hidden
    )
    .style('visibility', hidden ? 'hidden' : 'visible')
}
/** Generates a line from passed in params, x1 x2 y1 y2 are `undefined` by
  default */
const generateComparisonLine = ({
  chartSvg,
  x1,
  x2,
  y1,
  y2,
  styling,
  hidden,
  mainSVGContainerId,
}: ComparisonLineParams) => {
  const hoverLineGroupID = `hover-line-group${mainSVGContainerId}`
  const fillBoxPattern = `${hoverLineGroupID}__fill`

  if (select(`#${hoverLineGroupID}`)) {
    selectAll(`#${hoverLineGroupID}`).remove()
  }

  //Styling
  const {
    fontWeight,
    textColor,
    comparisonLine: { line, comparisonBox },
  } = Styling

  const comparisonGroup = chartSvg.append('g').attr('id', hoverLineGroupID)

  generateImageFill({
    chartSvg: comparisonGroup,
    imagePatternID: fillBoxPattern,
    imageSrc: Styling.image.comparisonBox,
  })

  const comparisonLine = comparisonGroup
    .append('line')
    .attr('id', `hover-line-id${mainSVGContainerId}`)
    .attr('class', `hover-line`)
    .attr('x1', x1)
    .attr('x2', x2)
    .attr('y1', y1)
    .attr('y2', y2)
    .attr('stroke-dasharray', `${styling?.dashArray ?? line.dashArray}`)
    .attr('stroke', `${styling?.stroke ?? line.stroke}`)
    .attr('stroke-width', `${styling?.strokeWidth ?? line.strokeWidth}`)
    .style('visibility', hidden ? 'hidden' : 'visible')

  const comparisonLineComparisonBox = comparisonGroup
    //Might be a custom ellipse
    .append('ellipse')
    .attr('id', `hover-line-id__ellipse${mainSVGContainerId}`)
    .attr('r', 13)
    .attr('rx', 25)
    .attr('ry', 20)
    .attr('cx', x2)
    .attr('cy', y1 + 10)
    .attr('fill', `url(#${fillBoxPattern})`)
    .attr(
      'stroke-width',
      `${styling?.strokeWidth ?? comparisonBox.strokeWidth}`
    )
    .style('visibility', hidden ? 'hidden' : 'visible')

  const comparisonLineComparisonText = comparisonGroup
    .append('text')
    // Position text below the textBackground
    .attr('y', y1 + 15)
    .style('font-weight', fontWeight)
    .style('fill', `${styling?.textColor ?? textColor}`)
    .style('visibility', hidden ? 'hidden' : 'visible')

  return {
    comparisonLine,
    comparisonLineComparisonBox,
    comparisonLineComparisonText,
  }
}

/**
 * Generates a rectangle svg element with text on top of it. The rect element is
 * responsive, meaning it will scale with the length of the text.
 *
 * Additionally a tip is added if `bottomTip` is `true`
 */
const generateRectangleWithText = ({
  chartSvg,
  id,
  text,
  color,
  textColor,
  bottomTip,
  adjustDistanceForFirstAnnotation = 0,
  tipLeftSide,
  hidden,
  mainSVGContainerId,
}: RectangleWithTextParams) => {
  //Text SVG boundary box initialized to 0 to prevent TS errors
  let textBoundaryBox: TextBoundaryBox = {
    x: 0,
    y: 0,
    height: 0,
    width: 0,
  }

  //Padding for the rectangle which acts as a background for the text
  const xPadding = 6
  const yPadding = 0
  //Width and height for the tip at the bottom of the rectangle background
  const tipWidth = 6
  const tipHeight = 6

  //Creates an annotation group
  const annotationGroup = chartSvg
    .append('g')
    .attr('class', `annotation-on-mouse${mainSVGContainerId}`)
    .attr('id', `annotationId-${id}`)
    .style('visibility', hidden ? 'hidden' : 'visible')

  const [rectId, textId] = [`annotation-rect-${id}`, `annotation-text-${id}`]

  //Adds text to the annotation group
  annotationGroup
    .append('text')
    .attr('id', textId)
    .attr('class', 'annotation-text')
    .attr('text-anchor', 'middle')
    .text(text)
    .style('font-weight', '800')
    .style('fill', textColor || 'white')
    .style('top', function (): string | number | boolean | null {
      //Gets the bounding box so if the annotation text is longer than the
      //bounding box
      textBoundaryBox = this.getBBox()
      //Must return something, I have no idea why, TS keeps throwing errors
      return null
    })

  annotationGroup
    .append('rect')
    .attr('id', rectId)
    .attr('class', 'back')
    .attr('rx', 4)
    .attr('x', textBoundaryBox?.x - xPadding)
    .attr('y', textBoundaryBox?.y - yPadding)
    .attr('width', textBoundaryBox?.width + 2 * xPadding)
    .attr('height', textBoundaryBox?.height + 2 * yPadding)
    .style('fill', color)

  /**TIP PLACEMENT CALCULATIONS */
  // Calculate the adjusted X position by subtracting the xPadding from the
  // textBoundaryBox's x value
  const adjustedX = textBoundaryBox?.x - xPadding

  // Calculate the half width of the textBoundaryBox, including the padding on
  // both sides
  const halfWidth = tipLeftSide
    ? textBoundaryBox?.width / xPadding - 10
    : (textBoundaryBox?.width + 2 * xPadding) / 2

  // Calculate the middle X position by adding the adjustedX value to halfWidth
  // and subtracting the adjustDistanceForFirstAnnotation
  const xMid = adjustedX + halfWidth - adjustDistanceForFirstAnnotation

  // Calculate the adjusted Y position by subtracting the yPadding from the
  // textBoundaryBox's y value
  const adjustedY = textBoundaryBox?.y - yPadding

  // Calculate the height of the textBoundaryBox, including the padding on both
  // sides
  const heightWithPadding = textBoundaryBox?.height + 2 * yPadding

  // Calculate the maximum Y position by adding the adjustedY value to the
  // heightWithPadding and subtracting 1
  const yMax = adjustedY + heightWithPadding - 1

  //Adds small annotation tips to the rectangle background
  if (bottomTip) {
    annotationGroup
      .append('polygon')
      .attr(
        'points',
        createTip({ tipLeftSide, xMid, yMax, tipWidth, tipHeight })
      )
      .style('fill', color)
  }

  //Makes sure the text is always rendered on top of rect background
  const textSvg = select(`#${textId}`)
  textSvg?.raise()

  return { annotationGroup, textSvg }
}

/**
 * Adjusts a callout not go out of chart boundaries
 */
const fitCalloutToBoundaries = (text: string, scalingFactor = 10): number => {
  if (text) {
    return text?.length + (text?.length / 3.3) * scalingFactor
  }
  return 0
}

/**
 * Returns a string of points that is used with the `points` attribute to draw
 * an svg shape
 */
const createTip = ({
  tipLeftSide,
  xMid,
  yMax,
  tipWidth,
  tipHeight,
}: {
  tipLeftSide?: boolean
  tipTop?: boolean
  xMid: number
  yMax: number
  tipWidth: number
  tipHeight: number
}): string => {
  const extraPadding: number = tipLeftSide ? 3 : 0
  const x1: number = tipLeftSide ? xMid : xMid - tipWidth
  const x2: number = tipLeftSide ? xMid + tipWidth : xMid + tipWidth
  const y1: number = tipLeftSide ? yMax - 5 : yMax
  const y2: number = yMax + tipHeight + extraPadding

  return `${x1},${y1} ${x2},${y1} ${xMid},${y2}`
}

/**
 * Generates an image pattern that can be used as a fill to certain svg
 * elements. The image pattern fill objectBounding box by default.
 * Returns the generated image id
 */
const generateImageFill = ({
  chartSvg,
  imagePatternID,
  imageSrc,
}: {
  chartSvg: Selection<SVGGElement, unknown, UnknownBaseType, unknown>
  imagePatternID: string
  imageSrc: string
}): string => {
  const defs = chartSvg.append('defs')

  const pattern = defs
    .append('pattern')
    .attr('id', imagePatternID || 'imagePattern')
    .attr('patternUnits', 'objectBoundingBox')
    .attr('patternContentUnits', 'objectBoundingBox')
    .attr('width', 1)
    .attr('height', 1)

  pattern
    .append('image')
    .attr('href', imageSrc)
    .attr('x', 0)
    .attr('y', 0)
    .attr('width', 1)
    .attr('height', 1)

  return imagePatternID
}

export {
  generateFocusCircle,
  generateRectangleWithText,
  generateComparisonLine,
  fitCalloutToBoundaries,
  createTip,
}
