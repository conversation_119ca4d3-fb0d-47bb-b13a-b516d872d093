import LottieAnimation from '../../../common/components/LottieAnimation'
import { useDrawBalanceLineChart } from '../hooks/useDrawBalanceLineChart'
import { BalanceChartProps } from '../types/Visualization.types'

/**
 * Visualizes the nominal balance  and user's contributions data
 */
const BalanceLineChart = ({
  height,
  width,
  redraw,
  contributionsStyleOptions,
  nominalBalanceStyling,
  numOfTicksForX,
  numOfTicksForY,
  axisDistanceFromGraph,
  showLegend,
  mainSVGContainerID,
  xAxisCssClass,
  yAxisCssClass,
  contributionLineKey,
  nominalBalanceLineKey,
  legendData,
  data,
  isLoading,
  containerCssClass,
  formatter,
}: BalanceChartProps) => {
  useDrawBalanceLineChart({
    data,
    height,
    width,
    redraw,
    contributionsStyleOptions,
    nominalBalanceStyling,
    numOfTicksForX,
    numOfTicksForY,
    axisDistanceFromGraph,
    showLegend,
    mainSVGContainerID,
    containerCssClass,
    xAxisCssClass,
    yAxisCssClass,
    contributionLineKey,
    nominalBalanceLineKey,
    legendData,
    isLoading,
    formatter,
  })

  if (!isLoading && redraw && data) {
    return (
      <main className={containerCssClass}>
        <svg id={mainSVGContainerID} />
      </main>
    )
  }

  return (
    <main className={containerCssClass}>
      <LottieAnimation
        style={{
          width: '100%',
          height: '100%',
        }}
        autoplay
        loop
      />
    </main>
  )
}

export default BalanceLineChart
