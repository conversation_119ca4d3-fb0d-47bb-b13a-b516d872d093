import { useState } from 'react'
import { track } from '../../../common/analytics/Analytics'
import { LegendEvent } from '../../../common/analytics/EventData'
import { ObjectIdProperty } from '../../../common/analytics/ObjectId'
import { useTranslate } from '../../../common/hooks/useTranslate'
import {
  InvestmentStrategyId,
  TontinatorParamsMode,
} from '../../../common/types/CommonTypes.types'
import {
  LINE_KEYS,
  legendKeys,
  productToLabel,
} from '../../dashboard/utils/consts'
import { Styling } from '../Styling'

const trackLegend = ({
  legendItemId,
  legendItemValue,
  legendItemLabel,
}: {
  legendItemId: ObjectIdProperty
  legendItemValue: boolean
  legendItemLabel: string
}) => {
  void track({
    event: LegendEvent.toggled,
    properties: {
      object_id: legendItemId,
      object_value: legendItemValue,
      label: legendItemLabel,
    },
  })
}

/**
 * Controls if the lines should be rendered or not
 */
export const useChartLegend = (params: {
  renderTontineLine: boolean
  renderDepositLine: boolean
  renderAnnuityLine: boolean
}) => {
  const t = useTranslate()
  const { renderTontineLine, renderDepositLine, renderAnnuityLine } = params

  const [tontineLine, setTontineLine] = useState<boolean>(renderTontineLine)
  const [depositLine, _setDepositLine] = useState<boolean>(renderDepositLine)
  const [annuityLine, _setAnnuityLine] = useState<boolean>(renderAnnuityLine)

  const setDepositLine = ({
    value,
    label,
  }: {
    value: boolean
    label: string
  }) => {
    _setDepositLine(!value)
    trackLegend({
      legendItemId: 'tontinator_legend_item_bank',
      legendItemValue: !value,
      legendItemLabel: label,
    })
  }
  const setAnnuityLine = ({
    value,
    label,
  }: {
    value: boolean
    label: string
  }) => {
    _setAnnuityLine(!value)
    trackLegend({
      legendItemId: 'tontinator_legend_item_annuity',
      legendItemValue: !value,
      legendItemLabel: label,
    })
  }

  /**
   * Controls visibility and styling of chart lines based on toggle state
   */
  const lineStyleVisibility = ({
    styling,
    isVisible,
    isInflationEnabled,
    lineName,
  }: {
    styling: typeof Styling
    isVisible: boolean
    isInflationEnabled: boolean
    lineName: keyof typeof Styling.tontinatorLineChart
  }) => {
    const { tontinatorLineChart } = styling
    const lineStyle = {
      ...(tontinatorLineChart[
        lineName
      ] as typeof tontinatorLineChart.tontineLine),
    }

    if (isVisible && isInflationEnabled) {
      return {
        ...lineStyle,
        areaOpacity: lineStyle.inflationAreaOpacity,
      }
    }

    if (isVisible) {
      return lineStyle
    }

    return {
      ...lineStyle,
      color: styling.hidden,
    } as typeof lineStyle
  }

  const generateLegend = ({
    returnsForProduct,
    productKey,
    strategyKey,
  }: {
    returnsForProduct: string
    productKey: TontinatorParamsMode
    strategyKey: InvestmentStrategyId
  }) => {
    return [
      {
        id: LINE_KEYS.tontineLineKey,
        color: Styling.tontinatorLineChart.tontineLine.color,
        text: t(legendKeys[productKey][strategyKey].tontine, {
          return: returnsForProduct,
          product: t(productToLabel[productKey]),
        }),
        // We do not want the tontine line to be toggled
        lineToggled: true,
      },
      {
        id: LINE_KEYS.bankDepositKey,
        color: Styling.tontinatorLineChart.bankDepositLine.color,
        text: t(legendKeys[productKey][strategyKey].deposit, {
          return: returnsForProduct,
          product: t(productToLabel[productKey]),
        }),
        lineToggled: depositLine,
        onClick: () =>
          setDepositLine({
            value: depositLine,
            label: t(legendKeys[productKey][strategyKey].deposit),
          }),
      },
      {
        id: LINE_KEYS.fixedAnnuityLineKey,
        color: Styling.tontinatorLineChart.annuityLine.color,
        text: t(legendKeys[productKey][strategyKey].annuity, {
          return: returnsForProduct,
          product: t(productToLabel[productKey]),
        }),
        lineToggled: annuityLine,
        onClick: () =>
          setAnnuityLine({
            value: annuityLine,
            label: t(legendKeys[productKey][strategyKey].annuity),
          }),
      },
    ]
  }

  return {
    tontineLine,
    setTontineLine,
    depositLine,
    setDepositLine,
    annuityLine,
    setAnnuityLine,
    lineStyleVisibility,
    generateLegend,
  }
}
