import { select, selectAll } from 'd3'
import { useEffect } from 'react'
import { differenceInPercentage } from '../../../common/utils/UtilFunctions'
import { useAccessibility } from '../../accessibility/hooks/useAccessibility'
import { chart } from '../D3Chart'
import { Styling } from '../Styling'
import {
  ForecastPayout,
  GraphDimensions,
  StylingOptionsD3,
  TontinatorLineChartProps,
} from '../types/Visualization.types'
import {
  adjustForInflation,
  dateParser,
  findLongestArray,
  generateAgeXAxis,
  maxAmounts,
  minDateRange,
} from '../utils/D3DataUtils'
import {
  calculateAndRenderAnnotations,
  calculateCutAreaForLine,
  cutLineAreaUsingRelativeLine,
  drawBreakevenDots,
  hoveringAnnotationDataset,
  inflationAreaOnTop,
  liftAnnotationYValue,
  modifyResultsForDepositAccount,
  trimDataFrom,
} from '../utils/DrawTontinatorUtils'

const DEFAULT_STYLE: StylingOptionsD3 = {
  color: Styling.hidden,
  stroke: '',
  fill: '',
  areaOpacity: 0,
  dashArray: '',
  showAnnotations: false,
  arrowHead: false,
  areaColor: '',
  inflationAreaOpacity: 0,
  transitionDuration: 0,
}

const ageToTrimDataFrom = 100
const delayCalloutRenderMs = 0

/**
 * Clean up function when the tontinator unmounts
 */
const onUnmountCleanUp = (mainSVGContainerId: string): void => {
  //Removes any leftover annotations in case of a redraw
  selectAll(`.annotation${mainSVGContainerId}`).remove()
  //Cleans up any leftover annotations from re-rendering
  if (select(`.annotation-on-mouse${mainSVGContainerId}`)) {
    selectAll(`.annotation-on-mouse${mainSVGContainerId}`).remove()
  }
}

/**
 * Draws the forecast payouts returned from the forecast payouts API. The hook
 * uses D3 to draw the forecast payouts, To get better data results visually on
 * the graph some data is being modified by the hook using util functions from
 * `D3DataUtils.ts`
 */
export const useDrawTontinator = ({
  width,
  height,
  mainSVGContainerID,
  containerCssClass,
  redraw,
  forecastData,
  xAxisCssClass,
  yAxisCssClass,
  tontineLineKey,
  fixedAnnuityLineKey,
  bankDepositKey,
  tontineLineStyle,
  bankDepositsStyle = DEFAULT_STYLE,
  fixedAnnuityStyle = DEFAULT_STYLE,
  numOfTicksForX,
  numOfTicksForY,
  axisDistanceFromGraph,
  formatter,
  toggles,
  isLoading,
  showFocusCircleOnPath,
  showHoveringMouseAnnotation,
  legendData,
  multipleTontineLineStyles,
  xAxisIntervals = 5,
  onHoverOrTapStart,
  onHoverOrTapLeave,
}: TontinatorLineChartProps) => {
  const { settings } = useAccessibility()

  useEffect(() => {
    let calloutRenderTimeoutId: NodeJS.Timeout
    const drawGraph = (width: number, height: number): void => {
      //////////RESPONSE DATA IS PROCESSED HERE////////////////////////////
      const allLinesTotalPayouts: Array<number> = []

      /**
       * All payout lines destructed to `payout` objects
       */
      const multipleLinePayouts: Array<Array<ForecastPayout>> =
        forecastData.map((forecast) => {
          //Rounds and pushes the total payouts by age 100 to a new array
          allLinesTotalPayouts.push(Math.round(forecast.stats.total_payouts))
          return forecast.results.payouts
        })

      // CAUTION: Modifies the original array!!
      trimDataFrom(multipleLinePayouts, ageToTrimDataFrom)

      //Results is actually payouts
      const results: Array<ForecastPayout> = multipleLinePayouts[0]
      const lineWithLowestTotalPayouts = Math.min(...allLinesTotalPayouts)
      //TODO:THIS WILL ONLY WORK FOR TWO LINES OF THE SAME TYPE PRESENT ON THE CHART
      const lineWithHighestTotalPayouts = Math.max(...allLinesTotalPayouts)
      /////////////////////////////////////////////////////////////////////

      //If style array is passed in default to first element if not just use
      //tontine line style
      tontineLineStyle = multipleTontineLineStyles?.[0] ?? tontineLineStyle

      //Returns the array with the longest length of both payouts array if there
      //are any
      const arrayOfPayoutsWithMostElements: Array<ForecastPayout> =
        findLongestArray(multipleLinePayouts)

      //Age where the forecast payouts array starts from
      const retirementAge =
        arrayOfPayoutsWithMostElements[0].age.total_months / 12

      //Age where the forecast payouts end
      const maxPayoutAgeToDisplay =
        arrayOfPayoutsWithMostElements[
          arrayOfPayoutsWithMostElements.length - 1
        ].age.years

      //Create the graph SVG and give it initial params
      const svg = chart?.drawChartSvg({
        selector: `#${mainSVGContainerID}`,
        height,
        width,
      })

      //Generates a linear xScale of payout ages so it can be used to display
      //ages on the x axis
      const xScale = chart?.generateLinearScaleX({
        //Returns the array with the longest length in order to scale the age
        //axis correctly
        data: arrayOfPayoutsWithMostElements,
        maxRangeValue: width,
        //Scales the age in years per payout month
        // Need to divide by 12 because the data is not in pure age, it is in months
        mapData: (payoutData: ForecastPayout) =>
          payoutData.age.total_months / 12,
      })

      //Generates a linear yScale from each max payout for tontine lines,
      //deposit_account and annuity in order to scale the chart lines within
      //boundaries
      const yScale = chart?.generateScaleY({
        domainMax: maxAmounts(multipleLinePayouts),
        rangeMax: height,
      })

      //Generates a time scale from each payout date by using the minimum start
      //date from multiple tontine lines, in order to avoid multiple lines being
      //cut off by each other the line with the minimum start date is used to
      //extended the range of the X scale so every lines fits.
      const xScaleDate = chart?.generateTimeScaleX({
        //If minDateRange returns undefined for some reason default to results
        //array
        data: minDateRange(multipleLinePayouts) ?? results,
        maxRangeValue: width,
        mapData: (payoutData: ForecastPayout) => dateParser(payoutData.date),
      })

      //Draw X axis using generated linear xScale
      const xAxis = chart?.drawAxisX({
        //Add trim amount if possible
        xScale,
        height,
        numberOfTicks: numOfTicksForX,
        // We do not want to show the decimals in the X axis
        tickFormatter: (tick) => Math.trunc(tick.valueOf()).toString(),
        data: generateAgeXAxis(
          retirementAge,
          maxPayoutAgeToDisplay,
          //Intervals between ticks, default is 5
          xAxisIntervals,
          //We want to include 100, to achieve the behavior that the chart
          //continues after age 100
          maxPayoutAgeToDisplay
        ),
        dataIterator: (tick) => tick,
      })

      //Draw Y axis using generated linear yScale
      const yAxis = chart?.drawAxisY({
        yScale,
        width,
        numberOfTicks: numOfTicksForY,
        //Don't show tick values, the Y axis is only used to draw Y guidelines
        tickFormatter: () => ``,
      })

      //Scale the payout dates using the generated time scale xScaleDate
      const scaledDateForX = (payoutsData: ForecastPayout) =>
        xScaleDate(dateParser(payoutsData.date))

      //Append the X axis to the main SVG Always assign classes or ids
      chart?.appendAxisXtoGraph({
        chartSvg: svg,
        xAxis,
        xAxisClass: xAxisCssClass,
        height,
      })

      //Append the X axis to the main SVG Always assign classes or ids
      chart?.appendAxisYtoGraph({
        chartSvg: svg,
        yAxis,
        yAxisClass: yAxisCssClass,
        width,
      })

      //Moves the  x axis ticks text up and down
      chart?.moveXticks({
        chartSvg: svg,
        pixels: axisDistanceFromGraph,
        axisSelector: `.${xAxisCssClass}`,
      })

      //Removes a randomly added path element to the Y axis
      chart?.removeGarbagePath(`.${yAxisCssClass}`)

      //Moves the Y axis ticks left or right
      chart?.moveYticks({
        chartSvg: svg,
        pixels: axisDistanceFromGraph,
        axisSelector: `.${yAxisCssClass}`,
      })

      /////Graph elements building and attaching to the graph svg starts from
      ///here/////

      //Scales the annuity payout amount using the generated yScale
      const annuityPayoutData = (payoutData: ForecastPayout) =>
        yScale(
          adjustForInflation(
            payoutData?.annuity?.amount,
            payoutData?.annuity.amount_inflation_adjusted,
            toggles?.inflation
          )
        )

      if (fixedAnnuityLineKey) {
        //Appends annuity line and area to the main svg
        chart?.drawGraphLineAndArea({
          svg,
          yScaledData: annuityPayoutData,
          //Smooths out the chart line
          xScaledData: scaledDateForX,
          scaledYStop: inflationAreaOnTop({
            inflation: toggles.inflation,
            height,
            yScaledData: (payout) => yScale(payout.annuity.amount),
          }),
          stylingOptions: fixedAnnuityStyle,
          lineKey: fixedAnnuityLineKey,
          areaKey: `${fixedAnnuityLineKey}-area`,
          data: results as unknown as Array<[number, number]>,
        })
      }

      const accountDepositPayoutData = (payoutData: ForecastPayout) =>
        yScale(
          adjustForInflation(
            payoutData?.deposit_account?.amount,
            payoutData?.deposit_account.amount_inflation_adjusted,
            toggles?.inflation
          )
        )

      if (bankDepositKey) {
        chart?.drawGraphLineAndArea({
          svg,
          yScaledData: accountDepositPayoutData,
          xScaledData: scaledDateForX,
          scaledYStop: inflationAreaOnTop({
            inflation: toggles.inflation,
            height,
            yScaledData: (payout) => yScale(payout.deposit_account.amount),
          }),
          stylingOptions: bankDepositsStyle,
          lineKey: bankDepositKey,
          areaKey: `${bankDepositKey}-area`,
          //The data is modified in order to show the line crashing, because we
          //simulate bank deposit account withdrawing so the account reaches
          //payouts 0 0 0... in an array, D3 only needs one 0 at the end of the
          //array to render a line crashing, which means the user has ran out of
          //money
          data: modifyResultsForDepositAccount(results) as unknown as Array<
            [number, number]
          >,
        })
      }

      //Scales the tontine payout amount using the generated yScale If inflation
      //is enabled then the `adjustForInflation` returns the values with
      //inflation applied and the yScale scales the amount with inflation
      const payoutData = (payoutData: ForecastPayout) =>
        yScale(
          adjustForInflation(
            payoutData?.tontine?.amount,
            payoutData?.tontine?.amount_inflation_adjusted,
            toggles?.inflation
          )
        )

      // Render legend for mobile only so hoverline
      // is on top of the legend
      if (
        legendData &&
        legendData?.length > 0 &&
        window?.matchMedia('(max-width: 900px)')?.matches
      ) {
        chart.renderLegend({
          chartSvg: svg,
          legendData,
          legendID: `tontinator-legend${mainSVGContainerID}`,
        })
      }

      ///////////////////TONTINE LINE and TONTINE PENSION PLAN LINES
      ///CONSTRUCTION////////////////////////////////

      //Line distance between two lines in percentage
      const lineDifferenceInPercentage = differenceInPercentage(
        lineWithHighestTotalPayouts,
        lineWithLowestTotalPayouts
      )

      //MAIN Iteration that goes trough each tontine line
      multipleLinePayouts.forEach(
        (linePayouts: Array<ForecastPayout>, payoutLineIndex: number) => {
          //Draws inflation area on top of a tontine line, exclusive only to the
          // marketing tontinator
          const inflationScaling = inflationAreaOnTop({
            inflation: toggles.inflation,
            height: height,
            yScaledData: (payout) => yScale(payout.tontine.amount),
          })

          //Cuts off the line area if there is another line below it, in order
          //not to elements overlapping with reach other
          const areaCutOff:
            | number
            | ((_: ForecastPayout, currentLinePayoutIndex: number) => number) =
            cutLineAreaUsingRelativeLine({
              lineWithLowestTotalPayouts,
              currentLinePayout: allLinesTotalPayouts[payoutLineIndex],
              chartHeight: height,
              relativeAreaToCutFrom: calculateCutAreaForLine(
                forecastData,
                yScale
              ),
              lineWithHighestTotalPayouts,
              currentPayoutLineLength: linePayouts?.length,
            })

          //Tontine lines and areas
          chart?.drawGraphLineAndArea({
            svg,
            yScaledData: payoutData,
            xScaledData: scaledDateForX,
            scaledYStop: toggles?.inflation ? inflationScaling : areaCutOff,
            stylingOptions:
              multipleTontineLineStyles?.[payoutLineIndex] ??
              tontineLineStyle ??
              ({} as StylingOptionsD3),
            lineKey: tontineLineKey + payoutLineIndex,
            areaKey: `${tontineLineKey + payoutLineIndex}-area`,
            data: linePayouts as unknown as Array<[number, number]>,
          })

          // Callouts for all lines, the reason we want to delay
          // the rendering is because there are scenarios where the line or
          // other elements cover the callout/s also the animation looks great
          // when they get delayed
          calloutRenderTimeoutId = setTimeout(() => {
            calculateAndRenderAnnotations({
              svg,
              mainSVGContainerId: mainSVGContainerID,
              payouts: linePayouts,
              xScaleDate: xScaleDate,
              yScale: yScale,
              forecastData: forecastData?.[payoutLineIndex],
              formatter,
              bankDepositsStyle: bankDepositsStyle,
              tontineLineStyle:
                multipleTontineLineStyles?.[payoutLineIndex] ??
                tontineLineStyle,
              fixedAnnuityStyle: fixedAnnuityStyle,
              toggles,
              liftAnnotationByY: liftAnnotationYValue(
                lineDifferenceInPercentage,
                0.01,
                20,
                lineWithHighestTotalPayouts,
                allLinesTotalPayouts[payoutLineIndex]
              ),
              chartWidth: width,
              hideMiddleAnnotations: payoutLineIndex > 0,
            })
            selectAll('text')?.raise()
          }, delayCalloutRenderMs)
        }
      )

      //Draws breakeven ages on the chart lines. Exclusive to the marketing
      //tontinator with other type of line other than tontine
      drawBreakevenDots({
        svg,
        xScaleDate: xScaleDate,
        yScale: yScale,
        tontineLineStyle,
        fixedAnnuityStyle,
        bankDepositsStyle,
        payouts: results,
        showBreakevenDots: !toggles.breakeven,
        toggles: toggles,
      })

      if (showFocusCircleOnPath || showHoveringMouseAnnotation) {
        //Hovering annotations
        chart?.crosshairAndAnnotation({
          mainSVGContainerId: mainSVGContainerID,
          chartSvg: svg,
          data: multipleLinePayouts,
          xScale: xScaleDate,
          yScale,
          focusPathID: `${tontineLineKey}${mainSVGContainerID}`,
          styling: multipleTontineLineStyles ?? [
            tontineLineStyle ?? ({} as StylingOptionsD3),
          ],
          options: {
            showFocusCircleOnPath,
            showHoveringMouseAnnotation,
          },
          bisectorDataFunction: (payout) => dateParser(payout.date),
          xDataToScale: 'date',
          yTontineData: hoveringAnnotationDataset({
            inflation: toggles.inflation,
            lineData: 'tontine',
          }),
          yAnnuityData: hoveringAnnotationDataset({
            inflation: toggles.inflation,
            lineData: 'annuity',
          }),
          yDepositAccountData: hoveringAnnotationDataset({
            inflation: toggles.inflation,
            lineData: 'deposit_account',
          }),
          formatter,
          annuityFocusPathID: `${tontineLineKey}${mainSVGContainerID}`,
          depositAccountFocusPathID: `${bankDepositKey}${mainSVGContainerID}`,
          annuityStyling: fixedAnnuityStyle,
          depositAccountStyling: bankDepositsStyle,
          height,
          width,
          toggles,
          onHoverOrTapStart,
          onHoverOrTapLeave,
          focusBallGroupID: `focus-ball-group-${mainSVGContainerID}`,
        })
      }
      //////////////////////////////////////////////////////////////

      // Render legend on desktop so hoverline is behind it
      if (
        legendData &&
        legendData?.length > 0 &&
        !window?.matchMedia('(max-width: 900px)')?.matches
      ) {
        chart.renderLegend({
          chartSvg: svg,
          legendData,
          legendID: `tontinator-legend${mainSVGContainerID}`,
        })
      }

      //Indents x axis ticks so they look nice true does not indent he last tick
      //since the results go up to 101 so it looks messed indented
      chart.indentFirstAndLastTickText(xAxisCssClass)

      //Makes sure the ALL text elements are in front of their backgrounds
      selectAll('text')?.raise().style('user-select', 'none')
    } //drawGraph() ends here

    //Returns the graph's container width and height when the container resizes
    //the graph also resizes following the container's new width and height
    const { width, height }: GraphDimensions = chart.getElementDimensions(
      `.${containerCssClass}`
    )

    //Draws the graph using the container's dimensions
    if (!isLoading && forecastData?.length > 0) {
      console.log(
        `%c>>> drawGraph trigged by resizing window or by fresh /tontinator response <<<`,
        'color:green'
      )
      drawGraph(width, height)
    }

    return () => {
      onUnmountCleanUp(mainSVGContainerID)
      clearTimeout(calloutRenderTimeoutId)
    }
  }, [
    width,
    height,
    redraw,
    toggles.inflation,
    toggles.percent,
    toggles.breakeven,
    toggles?.annuityLine,
    toggles?.depositLine,
    mainSVGContainerID,
    forecastData?.[0]?.view_id,
    settings?.fontIncreaseBy,
    //Will take care of when new data is available from API
    isLoading,
  ])
}
