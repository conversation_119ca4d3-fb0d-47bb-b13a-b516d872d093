import { timeFormat } from 'd3'
import dayjs from 'dayjs'
import { useEffect } from 'react'
import { chart } from '../D3Chart'
import { BalanceChartProps, Transaction } from '../types/Visualization.types'
import { dateParser } from '../utils/D3DataUtils'

/**
 * Draws user's contributions and nominal balance`
 */
export const useDrawBalanceLineChart = ({
  height,
  width,
  redraw,
  contributionsStyleOptions,
  nominalBalanceStyling,
  numOfTicksForX,
  numOfTicksForY,
  axisDistanceFromGraph,
  showLegend,
  mainSVGContainerID,
  containerCssClass,
  data,
  xAxisCssClass,
  yAxisCssClass,
  contributionLineKey,
  nominalBalanceLineKey,
  legendData,
  isLoading,
  formatter,
}: BalanceChartProps) => {
  useEffect(() => {
    const drawGraph = (width: number, height: number) => {
      //Create the graph SVG and give it initial params
      const svg = chart?.drawChartSvg({
        selector: `#${mainSVGContainerID}`,
        height,
        width,
      })

      //Generate the scales Scales return points needed by d3 to draw axis and
      //lines
      const xScale = chart?.generateTimeScaleX({
        data,
        maxRangeValue: width,
        mapData: (balance) => {
          //Use year month
          return parseDateForD3(balance?.transaction?.time)
        },
      })

      //Generates a y scale  using the highest available value in the data array
      const yScale = chart?.generateScaleY({
        domainMax: findHighestNominalBalance(data),
        rangeMax: height,
      })

      //Generate the axis
      const xAxis = chart?.drawAxisX({
        xScale,
        height,
        numberOfTicks: numOfTicksForX,
        //Shows only shorthand months January -> Jan
        tickFormatter: (date) => timeFormat('%b')(date as Date),
        data,
        dataIterator: (data) => parseDateForD3(data.transaction.time),
      })

      const yAxis = chart?.drawAxisY({
        yScale,
        width,
        numberOfTicks: numOfTicksForY,
        tickFormatter: (balance) => {
          return formatter?.(balance.valueOf() ?? '') ?? ''
        },
      })

      const scaledDatesForX = (balance: Transaction) =>
        xScale(parseDateForD3(balance?.transaction?.time))

      //Append the X axis to the main SVG Always assign classes or ids
      chart?.appendAxisXtoGraph({
        chartSvg: svg,
        xAxis,
        xAxisClass: xAxisCssClass,
        height,
      })

      //Moves the  x axis ticks text up and down
      chart?.moveXticks({
        chartSvg: svg,
        pixels: axisDistanceFromGraph,
        axisSelector: `.${xAxisCssClass}`,
      })

      //Append the Y axis to the main SVG Always assign classes or ids
      chart?.appendAxisYtoGraph({
        chartSvg: svg,
        yAxis,
        yAxisClass: yAxisCssClass,
        width,
      })

      //Removes a randomly added path element to the Y axis
      chart?.removeGarbagePath(`.${yAxisCssClass}`)

      //Moves the Y axis ticks left or right
      chart?.moveYticks({
        chartSvg: svg,
        pixels: axisDistanceFromGraph,
        axisSelector: `.${yAxisCssClass}`,
      })

      chart?.drawGraphLineAndArea({
        svg,
        yScaledData: (balance) => yScale(balance?.nominalBalance?.amount),
        xScaledData: scaledDatesForX,
        // Cut the area where contributions start, because nominal balance will
        // always be on top
        scaledYStop: (balance) => yScale(balance?.totalContribution?.amount),
        stylingOptions: nominalBalanceStyling,
        lineKey: nominalBalanceLineKey,
        areaKey: `${nominalBalanceLineKey}-area`,
        data: data as unknown as Array<[number, number]>,
      })

      chart?.drawGraphLineAndArea({
        svg,
        yScaledData: (balance) => yScale(balance?.totalContribution?.amount),
        xScaledData: scaledDatesForX,
        // Safely extend full height, because the total contribution amount will
        // only be equal or less than nominal balance, so the line and area for
        // that will always be below the nominal balance
        scaledYStop: height,
        stylingOptions: contributionsStyleOptions,
        lineKey: contributionLineKey,
        areaKey: `${contributionLineKey}-area`,
        data: data as unknown as Array<[number, number]>,
      })

      if (showLegend) {
        chart.renderLegend({
          chartSvg: svg,
          legendData,
          legendID: `nominal-balance-line-chart-legend`,
        })
      }

      //Indents x axis ticks so they look nice true does not indent he last tick
      //since the results go up to 101 so it looks messed indented
      chart.indentFirstAndLastTickText(xAxisCssClass)
    }

    //Returns the graph's container width and height
    //when the container resizes the graph also resizes
    const graphContainerDimensions = chart.getElementDimensions(
      `.${containerCssClass}`
    )

    if (!isLoading && data) {
      //Draws the graph using the container's dimensions
      drawGraph(
        graphContainerDimensions?.width,
        graphContainerDimensions?.height
      )
    }
  }, [
    height,
    width,
    data,
    redraw,
    numOfTicksForX,
    numOfTicksForY,
    axisDistanceFromGraph,
    nominalBalanceStyling?.stroke,
    nominalBalanceStyling?.color,
    nominalBalanceStyling?.fill,
    nominalBalanceStyling?.areaOpacity,
    contributionsStyleOptions?.stroke,
    contributionsStyleOptions?.color,
    contributionsStyleOptions?.fill,
    contributionsStyleOptions?.areaOpacity,
    showLegend,
  ])
}

const findHighestNominalBalance = (array: Array<Transaction>) => {
  const highestNominalBalance = array.reduce(
    (highest, { nominalBalance: { amount } }) => {
      return amount > highest ? amount : highest
    },
    0
  )

  return highestNominalBalance
}

const parseDateForD3 = (date: Date) => dateParser(dayjs(date).format('YYYY-MM'))
