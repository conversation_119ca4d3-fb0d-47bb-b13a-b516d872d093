//Types for utils functions in Visualization
import { BaseType, Selection } from 'd3'
import { SvgSelection } from '../types/D3Chart.types'
import { StylingOptionsD3 } from './Visualization.types'

/**
 * Custom D3 BaseType in order to satisfy the BaseType constraints that D3 has
 * in the `Selection` generic type
 */
type UnknownBaseType = BaseType & {
  // Add any custom properties if needed
}

/**
 * Generic D3 Svg element
 */
type D3SelectedSVG = SvgSelection

type FocusCircleParams = {
  chartSvg: Selection<SVGGElement, unknown, HTMLElement, unknown>
  id: string
  styling: StylingOptionsD3
  radius: number
  hidden: boolean
}

type ComparisonLineParams = {
  chartSvg: D3SelectedSVG
  x1: number
  x2: number
  y1: number
  y2: number
  styling?: Pick<
    StylingOptionsD3,
    'strokeWidth' | 'dashArray' | 'stroke' | 'textColor'
  >
  hidden: boolean
  mainSVGContainerId?: string
}

type RectangleWithTextParams = {
  chartSvg: D3SelectedSVG
  id: string
  text: string
  color: string
  textColor: string
  bottomTip?: boolean
  adjustDistanceForFirstAnnotation?: number
  tipLeftSide?: boolean
  hidden: boolean
  mainSVGContainerId?: string
}

type RectangleWithTextReturnType = {
  annotationGroup: Selection<SVGGElement, unknown, UnknownBaseType, unknown>
  textSvg: object
}

/**
 * Minimal properties that we use from the text boundary box
 */
type TextBoundaryBox = {
  x: number
  y: number
  width: number
  height: number
}

type BreakevenAdjustments = {
  tontineAdjustment: number
  depositAccountAdjustment: number
  annuityAdjustment: number
}

export type {
  FocusCircleParams,
  ComparisonLineParams,
  RectangleWithTextParams,
  RectangleWithTextReturnType,
  TextBoundaryBox,
  UnknownBaseType,
  BreakevenAdjustments,
}
