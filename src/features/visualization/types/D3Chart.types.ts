import {
  Area,
  BaseType,
  NumberValue,
  ScaleLinear,
  ScaleTime,
  Selection,
} from 'd3'
import {
  ForecastPayout,
  ForecastResult,
  FormatterFunction,
  StylingOptionsD3,
  TontinatorToggles,
} from './Visualization.types'

type TickFormatter = (domainValue: NumberValue, index: number) => string
type SvgSelection = Selection<BaseType, unknown, HTMLElement, unknown>
type LinearScale = ScaleLinear<number, number, never>
type TimeScale = ScaleTime<number, number, never>

type ScaleParams<T, U> = {
  data: Iterable<T>
  maxRangeValue: number
  mapData: (d: T) => U
}

type MoveAxisTicks = {
  chartSvg: SvgSelection
  pixels: number
  axisSelector: string
}

type StylingOptions = {
  styling?: Array<StylingOptionsD3>
  depositAccountStyling: StylingOptionsD3
  annuityStyling: StylingOptionsD3
}

type HoverOptions = {
  hideFocusCircles?: boolean
  showFocusCircleOnPath?: boolean
  showHoveringMouseAnnotation?: boolean
}

type AddAnnotationParams = {
  chartSvg: SvgSelection
  color: string
  xScale: number
  yScale: number
  text: string
  id: string
  textColor?: string
  transitionDuration: number
  firstAnnotation?: boolean
  lastAnnotation?: boolean
  tipOnTop?: boolean
  mainSVGContainerId: string
}

type CrosshairAndAnnotationParams<T> = {
  chartSvg: SvgSelection
  data: Array<Array<T>>
  xScale: ScaleTime<number, number>
  yScale: ScaleLinear<number, number>
  options: HoverOptions
  focusPathID: string
  xDataToScale: string
  yTontineData: string
  yAnnuityData: string
  yDepositAccountData: string
  bisectorDataFunction: (payout: T) => Date
  formatter?: FormatterFunction
  annuityFocusPathID: string
  depositAccountFocusPathID: string
  height: number
  width: number
  toggles: { percent: boolean; inflation: boolean }
  hideFocusCircles?: boolean
  hideCrosshair?: boolean
  hideAnnotations?: boolean
  focusBallGroupID: string
  mainSVGContainerId: string
  onHoverOrTapStart?: (event: MouseEvent) => void
  onHoverOrTapLeave?: () => void
} & StylingOptions

type DrawAxisX<T> = {
  xScale: TimeScale | LinearScale
  height: number
  numberOfTicks: number
  tickFormatter: TickFormatter
  data: Array<T>
  dataIterator: (d: T) => number | Date
}

type GenerateFocusCirclesParams<T> = {
  chartSvg: SvgSelection
  tontineFocusPathID: string
  depositAccountFocusPathID: string
  annuityFocusPathID: string
  hidden: boolean
  focusCirclesRadius: number
  focusGroupID?: string
  data: Array<T>
} & StylingOptions

type DrawGraphLineAndAreaParams<T> = {
  svg: SvgSelection
  yScaledData: (d: T) => number
  xScaledData: (d: T) => number
  scaledYStop: number | ((_: T, currentLinePayoutIndex: number) => number)
  stylingOptions: StylingOptionsD3
  lineKey: string
  areaKey: string
  data: Array<[number, number]>
}

type DrawCircleOnLineParams = {
  chartSvg: SvgSelection
  xScaledPosition: number
  yScaledPosition: number
  textInCircle: string
  stylingOptions: {
    color?: string
    textColor?: string
    radius?: string
  }
  circleID: string
  transitionDuration?: number
  hideDot?: boolean
}

type RenderAreaLineParams<T> = {
  chartSvg: SvgSelection
  id: string
  data: Array<T>
  drawArea: Area<T>
  fill?: string
  opacity?: number
  transitionDuration?: number
  containerWidth?: number
}

/**
 * Param types from the `drawBreakevenDots function`
 */
type DrawBreakevenDotsParams = {
  svg: SvgSelection
  xScaleDate: ScaleTime<number, number, never>
  yScale: ScaleLinear<number, number, never>
  payouts: Array<ForecastPayout>
  tontineLineStyle?: StylingOptionsD3
  fixedAnnuityStyle?: StylingOptionsD3
  bankDepositsStyle?: StylingOptionsD3
  showBreakevenDots: boolean
  toggles: TontinatorToggles
}

type RenderAnnotationsParams = {
  payouts: Array<ForecastPayout>
  xScaleDate: ScaleTime<number, number, never>
  yScale: ScaleLinear<number, number, never>
  formatter?: FormatterFunction
  toggles: TontinatorToggles
  forecastData: ForecastResult
  tontineLineStyle?: StylingOptionsD3
  fixedAnnuityStyle?: StylingOptionsD3
  bankDepositsStyle?: StylingOptionsD3
  svg: SvgSelection
  liftAnnotationByY: number
  chartWidth?: number
  mainSVGContainerId?: string
  hideMiddleAnnotations?: boolean
}

type InflationAreaParams = {
  inflation: boolean
  height: number
  yScaledData: (payouts: ForecastPayout) => number
}

export type {
  AddAnnotationParams,
  CrosshairAndAnnotationParams,
  DrawAxisX,
  DrawBreakevenDotsParams,
  DrawCircleOnLineParams,
  DrawGraphLineAndAreaParams,
  GenerateFocusCirclesParams,
  HoverOptions,
  InflationAreaParams,
  LinearScale,
  MoveAxisTicks,
  RenderAnnotationsParams,
  RenderAreaLineParams,
  ScaleParams,
  StylingOptions,
  SvgSelection,
  TickFormatter,
  TimeScale,
}
