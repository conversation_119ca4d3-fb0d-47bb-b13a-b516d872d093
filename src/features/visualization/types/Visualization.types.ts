//Types for only the visualization part of the app. These types are only used
//with D3 hooks and D3 graphs within the visualization directory

type FormatterFunction = (value: number, alpha3CountryCode?: string) => string
/**
 * Annotation value function params
 */
type AnnotationValueParams = {
  percentage: boolean
  value: number
  inflation: boolean
  inflationAmount: number
  forecastData: ForecastResult
  dataKey: string
  payoutOnAnnotation: ForecastPayout
  formatter?: FormatterFunction
}

type Transaction = {
  transaction: {
    time: Date
    "type'": string
    amount: {
      amount: number
      currency: string
    }
  }
  nominalBalance: {
    amount: number
    currency: string
  }
  totalContribution: {
    amount: number
    currency: string
  }
}

type PayoutProductInfo = {
  amount: number
  amount_inflation_adjusted: number
  breakeven: boolean
  '12mo_percent'?: number
}

/**
 * Response from the forecast API endpoint
 */
type ForecastPayout = {
  //Has to be unknown iterator key because the string index key is used in data
  //that comes from a promise and for some reason typescript complains about
  //this
  [dataKey: string]: unknown

  date: string
  age: {
    months: number
    total_months: number
    years: number
  }
  annuity: PayoutProductInfo
  deposit_account: PayoutProductInfo
  tontine: PayoutProductInfo
}

/**
 * Toggles buttons for the tontinator in order to hide show certain graph
 * elements
 */
type TontinatorToggles = {
  inflation: boolean
  breakeven: boolean
  percent: boolean
  depositLine: boolean
  annuityLine: boolean
}

type PayoutStats = {
  payout_percentage: number
  total_contributions: number
  total_payouts: number
  stats_age: {
    months: number
    total_months: number
    years: number
  }
}

/**
 * Forecast data response from `payout_forecast` API
 */
type ForecastResult = {
  [x: string]: unknown
  results: {
    payouts: Array<ForecastPayout>
    annual_inflation_rate: number
    currency: string
  }
  view_id: string
  stats: PayoutStats
}

/**
 * Any D3 graph dimensions
 */
type GraphDimensions = {
  height: number
  width: number
}

type LegendItem = {
  color: string
  text: string
  id: string
  lineToggled: boolean
  onClick?: () => void
}

/**
 * Base interface for all line charts
 */
interface LineChartProps {
  width?: number
  height?: number
  mainSVGContainerID: string
  containerCssClass: string
  drawingAnimation?: object
  redraw?: boolean
  numOfTicksForX: number
  numOfTicksForY: number
  axisDistanceFromGraph: number
  formatter?: FormatterFunction
  isLoading: boolean
  xAxisCssClass: string
  yAxisCssClass: string
  legendData: Array<LegendItem>
}

/**
 * Types for `<TontinatorLineChart>` and `useDrawTontinator` hook
 */
interface TontinatorLineChartProps extends LineChartProps {
  forecastData: Array<ForecastResult>
  tontineLineKey: string
  fixedAnnuityLineKey?: string
  bankDepositKey?: string
  tontineLineStyle?: StylingOptionsD3
  multipleTontineLineStyles?: Array<StylingOptionsD3>
  bankDepositsStyle?: StylingOptionsD3
  fixedAnnuityStyle?: StylingOptionsD3
  toggles: TontinatorToggles
  showVerticalHoveringLine?: boolean
  showFocusCircleOnPath?: boolean
  showHoveringMouseAnnotation?: boolean
  xAxisIntervals?: number
  onHoverOrTapStart?: () => void
  onHoverOrTapLeave?: () => void
}

interface BalanceChartProps extends LineChartProps {
  contributionsStyleOptions: StylingOptionsD3
  nominalBalanceStyling: StylingOptionsD3
  showLegend: boolean
  data: Array<Transaction>
  contributionLineKey: string
  nominalBalanceLineKey: string
  containerCssClass: string
}

//Will become obsolete because this will be done from the backend and I have no
//idea what the backend response will look like
type BreakevenData = {
  breakevenAge: number
  breakevenDate: Date
  breakevenAmount: number
}

//Will become obsolete because this will be done from the backend and I have no
//idea what the backend response will look like
type BreakevenAges = {
  tontine: BreakevenData
  annuities: BreakevenData
  depositAccount: BreakevenData
}

/**
 * Used for type checking the `amountToPercent` function
 */
type AmountToPercentParams = {
  forecastData: ForecastResult
  inflationToggle: boolean
  dataKey: string
  payoutOnAnnotation: ForecastPayout
  formatter?: FormatterFunction
}

type LineChartLines = 'tontine' | 'annuity' | 'deposit_account'

interface StylingOptionsD3<T = object> {
  color: string
  stroke: string
  fill: string
  areaOpacity: number
  dashArray: string
  showAnnotations: boolean
  arrowHead: boolean
  areaColor: string
  textColor?: string
  backgroundColor?: string
  inflationAreaOpacity: number
  additionalOptions?: T
  transitionDuration: number
  strokeWidth?: string
}

export type {
  AmountToPercentParams,
  AnnotationValueParams,
  BalanceChartProps,
  BreakevenAges,
  ForecastPayout,
  ForecastResult,
  FormatterFunction,
  GraphDimensions,
  LegendItem,
  LineChartLines,
  StylingOptionsD3,
  TontinatorLineChartProps,
  TontinatorToggles,
  Transaction,
}
