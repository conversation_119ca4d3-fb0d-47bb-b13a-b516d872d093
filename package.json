{"name": "mytontine-webapp", "version": "1.0.0", "description": "MyTontine webapp", "private": true, "type": "module", "scripts": {"dev": "vite", "serve": "vite preview", "build": "vite build", "type-check": "tsc --noEmit", "test-coverage:text": "nyc report --reporter text -t cypress/.nyc_output", "tc-report": "npm run type-check && type-coverage", "biome-check": "biome check", "format": "biome format --write", "format-check": "biome format", "lint": "biome lint", "lint:format": "npm run format-check && npm run lint-scss && npm run lint", "lint-scss": "stylelint src/**/*.scss", "cypress:run": "scripts/prepare.sh && VITE_INSTRUMENT_CODE=true VITE_ENVIRONMENT=development cypress run -P . -C cypress/cypress.config.ts", "cypress:ct": "npm run cypress:run -- --component --spec \"cypress/component/components/**/*.cy.tsx\"", "cypress:ut": "npm run cypress:run -- --component --spec \"cypress/component/unit/*.cy.tsx\"", "cypress-open": "cypress open -P . -C cypress/cypress.config.ts", "cypress-headless": "npm run cypress:run -- --spec \"./cypress/e2e/*.ts\" --headless && npm run test-coverage:text", "dev:mt": "scripts/prepare.sh && npm run lint:format && npm run type-check && scripts/dev-server.sh", "build:app": "scripts/prepare.sh && npm run tc-report && cd scripts && ./build.sh", "build-analyze": "INSTRUMENT_CODE=false vite-bundle-visualizer -i ./index.html -c ./vite.config.ts"}, "repository": {"type": "git", "url": "git+https://github.com/tontinetrust/tontine-gui.git"}, "author": "<EMAIL>", "license": "ISC", "bugs": {"url": "https://github.com/tontinetrust/tontine-gui/issues"}, "homepage": "https://github.com/tontinetrust/tontine-gui#readme", "dependencies": {"@docuseal/react": "^1.0.68", "@sentry/react": "^10.4.0", "@sentry/tracing": "^7.120.4", "@xstate/react": "^6.0.0", "axios": "^1.11.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "i18next": "^25.3.4", "i18next-browser-languagedetector": "^8.2.0", "i18next-chained-backend": "^4.6.2", "i18next-http-backend": "^3.0.2", "i18next-localstorage-backend": "^4.2.0", "i18next-locize-backend": "^7.0.4", "libphonenumber-js": "^1.12.17", "locize": "^4.0.14", "locize-lastused": "^4.0.2", "lottie-web": "^5.13.0", "mixpanel-browser": "^2.70.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.7.3", "react-router": "^7.9.1", "react-share": "^5.2.2", "react-toastify": "^11.0.5", "xstate": "^5.21.0"}, "devDependencies": {"@biomejs/biome": "2.2.4", "@cypress/code-coverage": "^3.14.6", "@percy/cli": "^1.31.2", "@percy/cypress": "^3.1.6", "@sentry/netlify-build-plugin": "^1.1.1", "@sentry/vite-plugin": "^4.1.0", "@statelyai/inspect": "^0.4.0", "@types/d3": "^7.4.3", "@types/mocha": "^10.0.10", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "cypress": "15.2.0", "globals": "^16.3.0", "netlify-cli": "^23.1.3", "nyc": "^17.1.0", "postcss-bem-linter": "^4.0.1", "regenerator-runtime": "^0.14.1", "sass": "^1.92.1", "stylelint": "^16.24.0", "stylelint-config-recommended-scss": "^16.0.1", "stylelint-scss": "^6.12.1", "stylelint-selector-bem-pattern": "^4.0.1", "terser": "^5.44.0", "type-coverage": "^2.29.7", "typescript": "^5.9.2", "vite": "7.1.5", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-istanbul": "^7.1.0"}, "typeCoverage": {"ignoreFiles": ["facetec/**/*", "cypress/coverage", "cypress/.nyc_output", "*.json", "coverage-ts/**/*", "src/public/**/*"], "strict": true, "atLeast": 95, "debug": false}}