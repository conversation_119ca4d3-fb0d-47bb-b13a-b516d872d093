<!doctype html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <!--FAVICON-->
  <link rel="icon" as="image" href="./favicon.svg" />

  <!-- SEO Meta Tags -->
  <title>MyTontine® - Pioneering the Future of Lifetime Income</title>
  <meta name="description"
    content="Download the MyTontine® app to open and manage your MyTontine® account and then get ready to Live Long & Prosper® with our award winning lifetime income plan." />
  <meta name="keywords" content="MyTontine, lifetime income, retirement planning, tontine, financial planning" />

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="MyTontine® - Pioneering the Future of Lifetime Income" />
  <meta property="og:description"
    content="Download the MyTontine® app to open and manage your MyTontine® account and then get ready to Live Long & Prosper® with our award winning lifetime income plan." />
  <meta property="og:image"
    content="https://cdn.sanity.io/images/hl9czw39/production/2f49578649383c50fd9114b1f284aa56b51a302f-1014x630.png" />
  <meta property="og:url" content="https://app.mytontine.com" />
  <meta property="og:type" content="website" />
  <meta property="og:site_name" content="MyTontine®" />

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="MyTontine® - Pioneering the Future of Lifetime Income" />
  <meta name="twitter:description"
    content="Download the MyTontine® app to open and manage your MyTontine® account and then get ready to Live Long & Prosper® with our award winning lifetime income plan." />
  <meta name="twitter:image"
    content="https://cdn.sanity.io/images/hl9czw39/production/2f49578649383c50fd9114b1f284aa56b51a302f-1014x630.png" />

  <!-- Additional SEO Meta Tags -->
  <meta name="robots" content="index, follow" />
  <meta name="author" content="TontineTrust" />
  <link rel="canonical" href="https://app.mytontine.com" />

  <!--NEEDED FOR REACT ROUTER-->
  <base href="/" />



  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Titillium+Web:ital,wght@0,400;0,600;1,400;1,600&display=swap"
    rel="stylesheet">



  <script type="module" src="/src/index.tsx"></script>
</head>

<body>
  <noscript> You need to enable JavaScript to run this app. </noscript>
  <div id="root"></div>
</body>

</html>