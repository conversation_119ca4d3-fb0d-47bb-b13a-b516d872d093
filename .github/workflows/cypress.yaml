# This is a self hosted action that runs on Azure
name: Cypress E2E tests with <PERSON><PERSON> <PERSON>

on:
  push:
    branches:
      - 'staging'

jobs:
  cypress-tests:
    runs-on:
      group: 'Robo Runner'
      labels: [robo-runner-new]
    strategy:
      matrix:
        node-version: [22.x]
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Cache node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Clean install frontend dependencies
        run: npm ci
      - name: Cypress E2E tests
        run: npx percy exec -s ./cypress/cypress.sh
        env:
          PERCY_TOKEN: ${{ secrets.PERCY_TOKEN}}
          VITE_LOCIZE_PROJECT_ID: ${{ secrets.VITE_LOCIZE_PROJECT_ID}}
          VITE_LOCIZE_API_KEY: ${{ secrets.VITE_LOCIZE_PROJECT_ID}}
