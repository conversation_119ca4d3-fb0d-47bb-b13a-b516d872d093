# Web build
/public/facetec-web-sdk
*.drawio

# MyTontine
dist
src/vite.config.ts
src/vite.config.ts.timestamp-*.mjs
src/vite.config.ts

# Cypress
cypress/screenshots/
cypress/.nyc_output
cypress/coverage
coverage

# Node
bower_components/
node_modules/

# Local Netlify folder
.netlify

# Sentry Auth Token
.env.sentry-build-plugin

# Sentry Config File
.env.sentry-build-plugin

# Enviorment variables file
.env
.env.local

# VS Code
.vscode

# TS type coverage
coverage-ts
