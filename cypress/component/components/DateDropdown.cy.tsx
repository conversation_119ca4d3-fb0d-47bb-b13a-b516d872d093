import DateDropdown from '../../../src/common/components/DateDropdown'
import { TESTING_IDS } from '../../support/ui-component-ids'

describe('DateDropdown.tsx', () => {
  const defaultValue = '2023-05-15'
  let onChangeStub: Cypress.Agent<sinon.SinonStub>

  beforeEach(() => {
    onChangeStub = cy.stub().as('onChangeStub')
  })

  it('renders day, month, and year dropdowns by default', () => {
    cy.mount(<DateDropdown value={defaultValue} onChange={onChangeStub} />)

    cy.getByDataID(TESTING_IDS.dateDropdownDay).should('exist')
    cy.getByDataID(TESTING_IDS.dateDropdownMonth).should('exist')
    cy.getByDataID(TESTING_IDS.dateDropdownYear).should('exist')
  })

  it('hides day dropdown when mode is monthYear', () => {
    cy.mount(
      <DateDropdown
        mode="monthYear"
        value={defaultValue}
        onChange={onChangeStub}
      />
    )

    cy.getByDataID(TESTING_IDS.dateDropdownDay).should('not.exist')
    cy.getByDataID(TESTING_IDS.dateDropdownMonth).should('exist')
    cy.getByDataID(TESTING_IDS.dateDropdownYear).should('exist')
  })

  it('formats date correctly on change', () => {
    cy.mount(<DateDropdown value={defaultValue} onChange={onChangeStub} />)

    cy.get('.date-dropdown__day')
      .find('.userInput__input-element--default')
      .click()
    cy.contains('.option-view', '20').click()

    cy.get('@onChangeStub').should('be.calledWith', '2023-05-20')
  })

  it('respects yearFrom and yearTo props', () => {
    cy.mount(
      <DateDropdown
        value={'2021-01-01'}
        yearFrom={2020}
        yearTo={2022}
        onChange={onChangeStub}
      />
    )

    cy.get('.date-dropdown__year')
      .find('.userInput__input-element--default')
      .click()

    cy.get('.date-dropdown__year')
      .find('.dropdown-menu')
      .find('.option-view')
      .should('have.length', 3)
    cy.contains('.option-view', '2020').should('exist')
    cy.contains('.option-view', '2022').should('exist')
  })

  it('disables interactions when readOnly is true', () => {
    cy.mount(
      <DateDropdown readOnly value={defaultValue} onChange={onChangeStub} />
    )

    cy.getByDataID(TESTING_IDS.dateDropdownDay).should('have.attr', 'readonly')
    cy.getByDataID(TESTING_IDS.dateDropdownMonth).should(
      'have.attr',
      'readonly'
    )
    cy.getByDataID(TESTING_IDS.dateDropdownYear).should('have.attr', 'readonly')
  })

  it('displays localized month names', () => {
    cy.mount(
      <DateDropdown locale="es-ES" value="2023-00-01" onChange={onChangeStub} />
    )

    cy.getByDataID(TESTING_IDS.dateDropdownMonth).click()

    cy.get('.date-dropdown__month')
      .find('.option-view__text')
      .should('contain', 'enero')
  })
})
