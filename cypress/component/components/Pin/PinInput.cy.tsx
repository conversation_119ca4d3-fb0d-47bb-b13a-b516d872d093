import React, { useState } from 'react'
import PinInput from '../../../../src/features/authentication/components/PinInput'

// A helper wrapper component to allow state updates during tests.
const PinInputWrapper = ({
  pinLength = 4,
  errorMessage,
  autoFocus,
  autoComplete,
  type,
  children,
}: {
  pinLength?: number
  errorMessage?: string
  autoFocus?: boolean
  autoComplete?: string
  type?: string
  children?: React.ReactNode
}) => {
  const [values, setValues] = useState<string[]>(Array(pinLength).fill(''))

  return (
    <PinInput
      label="Enter PIN"
      pinLength={pinLength}
      values={values}
      autoFocus={autoFocus}
      autoComplete={autoComplete}
      type={type}
      onChange={(newValues) => setValues(newValues)}
      errorMessage={errorMessage}
    >
      {children}
    </PinInput>
  )
}

describe('PinInput Component', () => {
  it('renders the correct number of input fields', () => {
    const pinLength = 4
    cy.mount(<PinInputWrapper pinLength={pinLength} />)
    cy.get('input').should('have.length', pinLength)
  })

  it('auto focuses on the first input field if autoFocus is true', () => {
    cy.mount(<PinInputWrapper autoFocus={true} />)
    cy.get('input').first().should('have.focus')
  })

  it('accepts a digit input and moves focus to the next field', () => {
    cy.mount(<PinInputWrapper pinLength={4} />)
    // Verify the first input starts with focus.
    cy.get('input').first().should('have.focus')
    // Simulate a keydown event for a digit (e.g. '1') on the first input.
    cy.get('input')
      .first()
      .trigger('keydown', {
        key: '1',
        preventDefault: () => undefined,
      })
    // The first input should now display "1" (as a controlled input value).
    cy.get('input').first().should('have.value', '1')
    // And the focus should move to the second input.
    cy.get('input').eq(1).should('have.focus')
  })

  it('clears the input on Backspace and moves focus to the previous field', () => {
    // Start with a pre-filled PIN
    const WrapperWithPrefilled = () => {
      const [values, setValues] = useState<string[]>(['1', '2', '', '', ''])
      return (
        <PinInput
          label="Enter PIN"
          pinLength={5}
          values={values}
          onChange={(newValues) => setValues(newValues)}
        />
      )
    }
    cy.mount(<WrapperWithPrefilled />)
    // Ensure the second input (index 1) has value "2"
    cy.get('input')
      .eq(1)
      .should('have.value', '2')
      .then(($input) => {
        // Trigger backspace to clear it.
        cy.wrap($input).trigger('keydown', {
          key: 'Backspace',
          preventDefault: () => undefined,
        })
      })
    // The second input should now be empty...
    cy.get('input').eq(1).should('have.value', '')
    // ...and focus should move to the first input.
    cy.get('input').first().should('have.focus')
  })

  it('handles paste events correctly, filling multiple fields', () => {
    const pinLength = 5
    cy.mount(<PinInputWrapper pinLength={pinLength} />)
    const pasteData = '12345'
    // Construct a fake paste event with a clipboardData object.
    const pasteEvent = {
      clipboardData: {
        getData: () => pasteData,
      },
      preventDefault: () => undefined,
    }
    // Trigger paste on the first input.
    cy.get('input')
      .first()
      .trigger('paste', pasteEvent)
      .then(() => {
        // Validate that all inputs got updated with the pasted digits.
        cy.get('input').then(($inputs) => {
          const values = [...$inputs].map((inp) => inp.value)
          expect(values).to.deep.eq(['1', '2', '3', '4', '5'])
        })
      })
  })

  it('applies error styles when errorMessage prop is provided', () => {
    cy.mount(<PinInputWrapper errorMessage="Invalid PIN" />)
    // Each input should have the error class. (Note: class names reflect the CSS module naming.)
    cy.get('input').each(($input) => {
      expect($input).to.have.class('pinInput__field--error')
    })
  })

  it('renders children if provided', () => {
    cy.mount(
      <PinInputWrapper>
        <div className="child-element">I am a child</div>
      </PinInputWrapper>
    )
    cy.get('.child-element').should('exist').and('contain', 'I am a child')
  })
})
