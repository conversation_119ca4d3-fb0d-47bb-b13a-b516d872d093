import { mount } from 'cypress/react'
import Filters from '../../../src/common/components/Filters'
import { TFunction } from '../../../src/common/types/CommonTypes.types'
import { filterRangeTypes } from '../../../src/common/utils/consts'
import { TESTING_IDS } from '../../support/ui-component-ids'

describe('Filters Component', () => {
  const mockData = [
    { id: 1, date: '2025-01-01', amount: 100 },
    { id: 2, date: '2025-02-01', amount: 200 },
    { id: 3, date: '2025-03-01', amount: 300 },
    { id: 4, date: '2025-04-01', amount: 300 },
    { id: 5, date: '2025-04-25', amount: 400 }, // Today (as of writing this test)
    { id: 6, date: '2025-04-24', amount: 500 }, // Yesterday
    { id: 7, date: '2025-04-18', amount: 600 }, // Within last 7 days
  ]

  beforeEach(() => {
    const defaultProps = {
      array: mockData,
      filterKey: {
        range: 'date',
      },
      defaultFromDate: '2025-01-01',
      defaultToDate: '2025-03-01',
      onFiltersApplied: cy.stub().as('onFiltersApplied'),
      onResetFilters: cy.stub().as('onResetFilters'),
      t: ((key: string) => key) as TFunction,
    }

    mount(<Filters {...defaultProps} />)
  })

  it('should render the filter toggle button', () => {
    cy.contains('STATEMENTS_PAGE_FILTER').should('exist')
  })

  it('should toggle filters visibility when clicked', () => {
    cy.getByDataID(TESTING_IDS.filterToggle).click()
    cy.getByDataID(TESTING_IDS.filterContainer).should('exist')
    cy.getByDataID(TESTING_IDS.filterToggle).click()
    cy.getByDataID(TESTING_IDS.filterContainer).should('not.exist')
  })

  it('should call onFiltersApplied when date range is selected', () => {
    cy.getByDataID(TESTING_IDS.filterToggle).click()
    cy.getByDataID(TESTING_IDS.dateRangeFilter).click()
    cy.contains(filterRangeTypes[0].label).click()
    cy.get('@onFiltersApplied').should('have.been.calledOnce')
  })

  it('should show custom date pickers when custom range is selected', () => {
    cy.getByDataID(TESTING_IDS.filterToggle).click()
    cy.getByDataID(TESTING_IDS.dateRangeFilter).click()
    cy.contains('FILTER_DATETYPE_CUSTOM_RANGE').click()
    cy.contains('FROM_DATE_RANGE_INPUT').should('exist')
    cy.contains('BANKING.PAYOUT_HISTORY_FILTER_TO').should('exist')
  })

  it('should call onResetFilters when reset button is clicked', () => {
    cy.getByDataID(TESTING_IDS.filterToggle).click()
    cy.getByDataID(TESTING_IDS.resetFilters).click()
    cy.get('@onResetFilters').should('have.been.calledOnce')
  })

  it('should filter data based on selected date range type', () => {
    const now = new Date(2025, 3, 25).getTime()
    cy.clock(now)
    cy.getByDataID(TESTING_IDS.filterToggle).click()

    cy.getByDataID(TESTING_IDS.dateRangeFilter).click()
    cy.contains('FILTER_DATETYPE_TODAY').click()
    cy.get('@onFiltersApplied').should('have.been.calledWith', [mockData[4]])

    cy.getByDataID(TESTING_IDS.dateRangeFilter).click()
    cy.contains('FILTER_DATETYPE_YESTERDAY').click()
    cy.get('@onFiltersApplied').should('have.been.calledWith', [mockData[5]])

    cy.getByDataID(TESTING_IDS.dateRangeFilter).click()
    cy.contains('FILTER_DATETYPE_LAST_7_DAYS').click()
    cy.get('@onFiltersApplied').should('have.been.calledWith', [
      mockData[4],
      mockData[5],
      mockData[6],
    ])

    cy.getByDataID(TESTING_IDS.dateRangeFilter).click()
    cy.contains('FILTER_DATETYPE_LAST_30_DAYS').click()
    cy.get('@onFiltersApplied').should('have.been.calledWith', [
      mockData[3],
      mockData[4],
      mockData[5],
      mockData[6],
    ])
  })
})
