import Dropdown from '../../../src/common/components/Dropdown'

const options = [
  { value: 'option1', label: 'Best car' },
  { value: 'option2', label: 'Best airplane' },
  { value: 'option3', label: 'Best house' },
]

describe('Dropdown.tsx', () => {
  it(`renders dropdown menu when input is clicked on and closes it 
  when clicked outside dropdown area`, () => {
    cy.mount(
      <Dropdown
        value={''}
        onChange={cy.stub()}
        label={'New options'}
        options={options}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
        searchBy={['value']}
      />
    )

    cy.get('.userInput__input-element--default').click()
    cy.get('.dropdown-menu').as('dropdown menu').should('be.visible')
    cy.get('body').click()
    cy.get('@dropdown menu').should('not.be.visible')
  })

  it('renders and calls onChange when value changes with valueOnChange key', () => {
    const onChangeStub = cy.stub()

    cy.mount(
      <Dropdown
        value={''}
        options={options}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
        searchBy={['value']}
        onChange={onChangeStub}
      />
    )

    cy.get('.userInput__input-element--default').click().as('search box')

    // Click option 2
    cy.get(':nth-child(2) > .option-view')
      .click()
      .then(() => {
        expect(onChangeStub).to.be.calledWith('option2')
        // Asserts if the selected value matches what the user sees
        cy.get('.userInput__jsx .option-view .option-view__text').should(
          'have.text',
          options[1].label
        )
      })
  })

  it('renders and calls onChange when value changes, WITHOUT `valueOnChange` key', () => {
    const onChangeStub = cy.stub()

    cy.mount(
      <Dropdown
        value={''}
        options={options}
        itemKey={{
          displayKey: 'label',
        }}
        searchBy={['value']}
        onChange={onChangeStub}
      />
    )

    cy.get('.userInput__input-element--default').click()

    // Click option 2
    cy.get(':nth-child(2) > .option-view')
      .click()
      .then(() => {
        expect(onChangeStub).to.be.calledWith(options[1])
      })
  })

  it('renders not found message if the search query did not find any options', () => {
    cy.mount(
      <Dropdown
        onChange={cy.stub()}
        value={''}
        options={options}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
        searchBy={['value']}
      />
    )
    cy.get('.userInput__input-element--default').as('search box').click()
    cy.get('@search box').type('something that does not exist')
    cy.get('.option-view').should('have.class', 'option-view--no-data')
  })

  it('closes dropdown menu when option is selected and selected style is applied on selected item', () => {
    cy.mount(
      <Dropdown
        options={options}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
        searchBy={['value']}
        onChange={cy.stub()}
        value={'option2'}
      />
    )
    // Assert if the dropdown menu closes after an option has been picked
    cy.get('.userInput__input-element--default').as('search box').click()
    cy.get('.dropdown-menu').as('dropdown menu').should('be.visible')
    cy.get(':nth-child(2) > .option-view').click()
    cy.get('@dropdown menu').should('not.be.visible')

    // Assert selected style after option has been selected
    cy.get('@search box').click()
    cy.get(':nth-child(2) > .option-view').should(
      'have.class',
      'option-view--selected'
    )
  })

  it('renders error message if there is an error', () => {
    const testErrorObject = {
      valid: false,
      message: 'Fake error message',
      i18nKey: 'ERROR_GENERIC',
    }

    cy.mount(
      <Dropdown
        value={'option2'}
        options={options}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
        searchBy={['value']}
        onChange={cy.stub()}
        validatorFunction={cy.stub()}
        errorMessage={testErrorObject}
      />
    )

    cy.get('.error-text').should('exist')
  })

  it('renders dropdown without searchBy params and has placeholder', () => {
    cy.mount(
      <Dropdown
        value={''}
        onChange={cy.stub()}
        searchBy={[]}
        label={'New options'}
        options={options}
        placeholder={'Cool placeholder'}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
      />
    )
    cy.get('.userInput__input-element--default').should(
      'have.attr',
      //TODO: Place holder value check somehow
      'placeholder'
    )
  })

  it('searches by passed in array of search key and always finds the desired option', () => {
    const halfTypedSearchQuery = 'air'
    const valueSearchKey = 'option2'
    const correctItemDisplayedToUser = options[1].label

    cy.mount(
      <Dropdown
        value={''}
        onChange={cy.stub()}
        label={'New options'}
        options={options}
        searchBy={['value', 'label']}
        placeholder={'Cool placeholder'}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
      />
    )

    cy.log('Search by "label" key')
    cy.get('.userInput__input-element--default').as('search box').click()
    cy.get('@search box').type(halfTypedSearchQuery)
    cy.get('.option-view__text').should('have.text', correctItemDisplayedToUser)

    cy.log('Search by "value" key')
    cy.get('@search box').clear()
    cy.get('@search box').type(valueSearchKey)
    cy.get('.option-view__text').should('have.text', correctItemDisplayedToUser)
    cy.log('Click the found option')
    cy.get('.option-view').click({ force: true })
  })

  it('does not render a divider on the last element in the dropdown menu', () => {
    cy.mount(
      <Dropdown
        value={''}
        onChange={cy.stub()}
        label={'New options'}
        options={options}
        searchBy={['value', 'label']}
        placeholder={'Cool placeholder'}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
      />
    )
    cy.get(`:nth-child(${options?.length}) > .divider > .divider__line`).should(
      'not.exist'
    )
  })

  it('enter readonly mode and input is not clickable or searchable', () => {
    cy.mount(
      <Dropdown
        readOnly
        value={options[0].value}
        label={'New options'}
        options={options}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
        searchBy={['value']}
        onChange={cy.stub()}
      />
    )

    // Assert if the input has the readonly attribute
    cy.get('input').should('have.attr', 'readonly')
  })

  it('resets the search query when the menu is closed', () => {
    cy.mount(
      <Dropdown
        value={''}
        onChange={console.log}
        label={'New options'}
        options={options}
        searchBy={['value', 'label']}
        placeholder={'Cool placeholder'}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
      />
    )
    cy.get('.userInput__input-element--default').as('search box').click()
    cy.get('@search box').type('hello')
    cy.get('body').click()
    cy.get('@search box').should('have.text', '')
  })

  it('should not close the dropdown menu if the search input is clicked again', () => {
    cy.mount(
      <Dropdown
        value={''}
        onChange={console.log}
        label={'New options'}
        options={options}
        searchBy={['value', 'label']}
        placeholder={'Cool placeholder'}
        itemKey={{
          displayKey: 'label',
          valueOnChange: 'value',
        }}
      />
    )
    cy.get('.userInput__input-element--default').as('search box').click()
    cy.get('.dropdown-menu').as('dropdown menu').should('be.visible')
    cy.get('@search box').type('hello')
    cy.get('.userInput__input-element--default').click()
    cy.get('.dropdown-menu').as('dropdown menu').should('be.visible')
  })
})
