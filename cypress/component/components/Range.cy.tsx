import Range from '../../../src/common/components/Range'

//Not a useful test, will leave it for now
describe('RangeSlider.jsx', () => {
  it('calls the onChange function when the slider is dragged', () => {
    const onChange = cy.stub().as('onChange')

    cy.mount(
      <Range value={0} onChange={onChange} steps={[0, 25, 50, 75, 100]} />
    )

    //Simulate dragging a range thumb
    cy.get('.sliderInput__range').invoke('val', 2).trigger('change')
  })
})
