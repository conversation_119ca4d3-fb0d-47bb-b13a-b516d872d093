import {
  adjustAndConvertToAgeMonthString,
  birthAgeAndMonthsFromFutureDate,
  calculateRetirementValues,
  dobToYearsAndMonthsOld,
  isLeapYear,
  modifyContributionAgeAndRetirementAge,
} from '../../../src/common/utils/UtilFunctions'
import { calculateYearForRetirementAge } from '../../../src/features/dashboard/utils/UtilFunctions'

//Util function only for testing purposes

describe('TSUtilFunction.ts - function: birthAgeAndMonthsFromFutureDate', () => {
  it('returns correct age and months for dob: 1970-01-01', () => {
    expect(
      birthAgeAndMonthsFromFutureDate('1970-01-01', '2023-01-01')
    ).to.deep.equal({
      age: 53,
      months: 0,
    })
  })

  it('returns correct age and months for dob: 1997-02-27 future: 2024-02-03 ', () => {
    const startDateISO = '1997-02-27'
    const targetDateISO = '2024-02-03'

    expect(
      birthAgeAndMonthsFromFutureDate(startDateISO, targetDateISO)
    ).to.deep.equal({
      age: 26,
      months: 11,
    })
  })

  it('returns correct age and months for dob: 2015-08-20 future: 2040-07-10 ', () => {
    const startDateISO = '2015-08-20'
    const targetDateISO = '2040-07-10'

    expect(
      birthAgeAndMonthsFromFutureDate(startDateISO, targetDateISO)
    ).to.deep.equal({
      age: 24,
      months: 10,
    })
  })

  it('returns correct age and months for dob: 2020-05-15 future: 2045-10-20', () => {
    const startDateISO = '2020-05-15'
    const targetDateISO = '2045-10-20'

    expect(
      birthAgeAndMonthsFromFutureDate(startDateISO, targetDateISO)
    ).to.deep.equal({
      age: 25,
      months: 5,
    })
  })

  it('returns correct age and months for dob: 2000-02-15 future: 2030-03-05', () => {
    const startDateISO = '2000-02-15'
    const targetDateISO = '2030-03-05'

    expect(
      birthAgeAndMonthsFromFutureDate(startDateISO, targetDateISO)
    ).to.deep.equal({
      age: 30,
      months: 0,
    })
  })

  it('returns correct age and months for dob: 2022-04-25 future: 2047-11-30', () => {
    const startDateISO = '2022-04-25'
    const targetDateISO = '2047-11-30'

    expect(
      birthAgeAndMonthsFromFutureDate(startDateISO, targetDateISO)
    ).to.deep.equal({
      age: 25,
      months: 7,
    })
  })
})

describe('function: isLeapYear', () => {
  it('returns true for a leap year 2020', () => {
    expect(isLeapYear(2020)).to.equal(true)
  })

  it('returns false for a non-leap year 2021', () => {
    expect(isLeapYear(2021)).to.equal(false)
  })

  it('returns true for a year 2000 divisible by 400', () => {
    expect(isLeapYear(2000)).to.equal(true)
  })

  it('returns false for a year 1900 divisible by 100 but not 400', () => {
    expect(isLeapYear(1900)).to.equal(false)
  })
})

describe('function: modifyContributionAgeAndRetirementAge', () => {
  it('modifies the retirement age if it is equal to the contribution age', () => {
    expect(modifyContributionAgeAndRetirementAge(65, 7, 65, 7)).to.deep.equal({
      age: 65,
      month: 8,
    })
  })

  it('handles month overflow if retirement month is greater than 11', () => {
    expect(modifyContributionAgeAndRetirementAge(65, 11, 65, 11)).to.deep.equal(
      {
        age: 66,
        month: 0,
      }
    )
  })

  it('does not modify the retirement age if contribution age is less than retirement age', () => {
    expect(modifyContributionAgeAndRetirementAge(60, 5, 65, 7)).to.deep.equal({
      age: 65,
      month: 7,
    })
  })

  it('does not handle month overflow if retirement month is exactly 11 while retAge === contAge', () => {
    expect(modifyContributionAgeAndRetirementAge(65, 7, 65, 11)).to.deep.equal({
      age: 65,
      month: 11,
    })
  })
})

describe('function: dobToYearsAndMonthsOld sys time: 2023', () => {
  beforeEach(() => {
    //Mock system time
    const now = new Date(2023, 11, 11).getTime()
    cy.log('Testing if it were date', now)
    cy.clock(now)
  })

  it('should calculate correct years old for birth year 1990', () => {
    const birthYear = 1990
    const dateOfBirth = `${birthYear}-01-01`
    const result = dobToYearsAndMonthsOld(dateOfBirth)

    expect(result.yearsOld).to.equal(33)
    expect(result.monthBornOn).to.equal(1)
    expect(result.monthsOld).to.equal(11)
    expect(result.birthYear).to.equal(birthYear)
  })

  it('should calculate correct years old for birth year 1968', () => {
    const birthYear = 1968
    const dateOfBirth = `${birthYear}-01-01`
    const result = dobToYearsAndMonthsOld(dateOfBirth)

    expect(result.yearsOld).to.equal(55)
    expect(result.monthBornOn).to.equal(1)
    expect(result.monthsOld).to.equal(11)
    expect(result.birthYear).to.equal(birthYear)
  })
})

describe('function: dobToYearsAndMonthsOld sys time: 2024', () => {
  beforeEach(() => {
    //Mock system time
    const now = new Date(2024, 5, 11)
    cy.log('Testing if it were date', now.toISOString())
    cy.clock(now.getTime())
  })

  it('should calculate correct years old for birth year 1968', () => {
    const birthYear = 1968
    const dateOfBirth = `${birthYear}-01-01`
    const result = dobToYearsAndMonthsOld(dateOfBirth)

    expect(result.yearsOld).to.equal(56)
    expect(result.monthBornOn).to.equal(1)
    expect(result.monthsOld).to.equal(5)
    expect(result.birthYear).to.equal(birthYear)
  })

  it('should calculate correct years old for birth year 1990', () => {
    const birthYear = 1990
    const dateOfBirth = `${birthYear}-01-01`
    const result = dobToYearsAndMonthsOld(dateOfBirth)

    expect(result.yearsOld).to.equal(34)
    expect(result.monthBornOn).to.equal(1)
    expect(result.monthsOld).to.equal(5)
    expect(result.birthYear).to.equal(birthYear)
  })
})

describe('function: adjustAndConvertToAgeMonthString', () => {
  beforeEach(() => {
    //Mocking does not seem to work for this test suite.
    //Works only for the function `adjustAndConvertToAgeMonthString` and not the
    //functions that use dates inside of it
    const now = new Date(2024, 11, 11)
    cy.log('Testing if it were date', now.toISOString())
    cy.clock(now.getTime())
  })

  it('modifies retirementAge and contributionAge if they are equal in AgeMonth format', () => {
    const result = adjustAndConvertToAgeMonthString(
      {
        age: 65,
        month: 7,
      },
      {
        age: 65,
        month: 7,
      }
    )
    expect(result.contributionAge).to.deep.equal({
      age: 65,
      month: 7,
    })
    expect(result.retirementAge).to.deep.equal({
      age: 65,
      month: 8,
    })
  })

  it('does not modify retirementAge and contributionAge if they are not equal', () => {
    const result = adjustAndConvertToAgeMonthString(
      {
        age: 65,
        month: 7,
      },
      {
        age: 60,
        month: 5,
      }
    )
    expect(result.contributionAge).to.deep.equal({
      age: 60,
      month: 5,
    })
    expect(result.retirementAge).to.deep.equal({
      age: 65,
      month: 7,
    })
  })

  it('handles input as AgeMonthString for retirementAge:65-0 and contributionAge:60-5', () => {
    const result = adjustAndConvertToAgeMonthString(
      {
        age: 65,
        month: 0,
      },
      {
        age: 60,
        month: 5,
      }
    )
    expect(result.contributionAge).to.deep.equal({
      age: 60,
      month: 5,
    })
    expect(result.retirementAge).to.deep.equal({
      age: 65,
      month: 0,
    })
  })
})

describe('function: calculateYearForRetirementAge | October 1, 2023', () => {
  beforeEach(() => {
    // Set the date to October 1, 2023
    const now = new Date(2023, 9, 1).getTime()
    cy.log('Testing if it were date', now)
    cy.clock(now)
  })

  it('calculates retirement year and month correctly when currentAge: 30-0 retAge: 65-0', () => {
    const contributionAge = {
      age: 30,
      month: 0,
    }
    const retirementAge = {
      age: 65,
      month: 0,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2058,
      retirementMonth: 10,
    })
  })

  it('calculates retirement year and month correctly when currentAge: 30-1 retAge: 65-0', () => {
    const contributionAge = {
      age: 30,
      month: 1,
    }
    const retirementAge = {
      age: 65,
      month: 0,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2058,
      retirementMonth: 9,
    })
  })

  it('calculates retirement year and month correctly when currentAge: 30-11 retAge: 65-0', () => {
    const contributionAge = {
      age: 30,
      month: 11,
    }
    const retirementAge = {
      age: 65,
      month: 0,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2057,
      retirementMonth: 11,
    })
  })

  it('calculates retirement year and month correctly when currentAge: 30-11 retAge: 65-2', () => {
    const contributionAge = {
      age: 30,
      month: 11,
    }
    const retirementAge = {
      age: 65,
      month: 2,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2058,
      retirementMonth: 1,
    })
  })

  it('calculates retirement year and month correctly when currentAge: 65-3 retAge: 66-4', () => {
    const contributionAge = {
      age: 65,
      month: 3,
    }
    const retirementAge = {
      age: 66,
      month: 4,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2024,
      retirementMonth: 11,
    })
  })
})

describe('function: calculateYearForRetirementAge | December 1, 2023 ', () => {
  beforeEach(() => {
    // Set the date to December 1, 2023
    const now = new Date(2023, 11, 1).getTime()
    cy.log('Testing if it were date', now)
    cy.clock(now)
  })

  it('calculates retirement year and month correctly when currentAge: 30-0 retAge: 65-0', () => {
    const contributionAge = {
      age: 30,
      month: 0,
    }
    const retirementAge = {
      age: 65,
      month: 0,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2058,
      retirementMonth: 12,
    })
  })

  it('calculates retirement year and month correctly when currentAge: 65-3 retAge: 65-4', () => {
    const contributionAge = {
      age: 65,
      month: 3,
    }
    const retirementAge = {
      age: 65,
      month: 4,
    }
    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2024,
      retirementMonth: 1,
    })
  })

  it('calculates retirement year and month correctly when currentAge: 65-3 retAge: 66-4', () => {
    const contributionAge = {
      age: 65,
      month: 3,
    }
    const retirementAge = {
      age: 66,
      month: 4,
    }

    const conValues = contributionAge
    const retValues = retirementAge

    expect(
      calculateYearForRetirementAge(contributionAge, retirementAge)
    ).to.deep.equal({
      yearsOldOnRetirement: retValues.age,
      monthsOldOnRetirement: retValues.month,
      yearsOldNow: conValues.age,
      monthsOldNow: conValues.month,
      retirementYear: 2025,
      retirementMonth: 1,
    })
  })
})

describe('function: calculateRetirementValues', () => {
  it('calculates accurately with day', () => {
    const user_details = {
      date_of_birth: '1992-03-15',
    }
    const retirementData = {
      year: 2057,
      month: 2,
      day: 15,
    }

    expect(
      calculateRetirementValues(user_details, retirementData)
    ).to.deep.equal({
      age: 64,
      months: 11,
    })
  })
})
