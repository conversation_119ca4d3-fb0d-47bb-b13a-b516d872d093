import {
  isValidACHRoutingNumber,
  isValidBankAccountNumber,
} from '../../../src/features/banking/utils/BankingValidators'

describe('BankingValidators.ts - function: isValidACHRoutingNumber', () => {
  it('should return false if the routing number length is not 9', () => {
    expect(isValidACHRoutingNumber('123456')).to.be.false
  })

  it('should return false if the routing number is not valid', () => {
    expect(isValidACHRoutingNumber('*********')).to.be.false
  })

  it('should return false if the routing number is random characters and numbers', () => {
    expect(isValidACHRoutingNumber('063112abc')).to.be.false
  })

  it('should return false if the routing number is random characters', () => {
    expect(isValidACHRoutingNumber('asdfg')).to.be.false
  })

  it('should return true if the routing number is valid AMERIS BANK', () => {
    //AMERIS BANK	24 SECOND AVE SE	MOULTRIE	GA
    expect(isValidACHRoutingNumber('*********')).to.be.true
  })
  it('should return true if the routing number is valid AMERIS BANK', () => {
    //CENTRAL BANK 	P.O. BOX 1207	SAVANNAH	TN
    expect(isValidACHRoutingNumber('*********')).to.be.true
  })

  it('should return true if the routing number is valid US BANK', () => {
    // US BANK EP-MN-WN1A	ST. PAUL	MN
    expect(isValidACHRoutingNumber('*********')).to.be.true
  })
})

describe('BankingValidators.ts - function: isValidBankAccountNumber', () => {
  it('should return false if the bank account number length is less than 5', () => {
    expect(isValidBankAccountNumber('1234')).to.be.false
  })

  it('should return false if the bank account number length is more than 17', () => {
    expect(isValidBankAccountNumber('*****************8')).to.be.false
  })

  it('should return false if the bank account number contains non-numeric characters', () => {
    expect(isValidBankAccountNumber('1234abcd')).to.be.false
  })

  it('should return true if the bank account number is valid', () => {
    expect(isValidBankAccountNumber('*****************')).to.be.true
  })
})
