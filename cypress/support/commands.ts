/// <reference types="cypress" />

import { API } from '../../src/common/api/API'
import { MAGIC_LOGIN_PARAM } from '../../src/common/utils/consts'
import { ACCOUNT_MENU, PRIVATE } from '../../src/routes/Route'
import { AdminMagicToken, MagicToken } from './consts'
import { TESTING_IDS, TestID } from './ui-component-ids'
import { screenSizes } from './visual-config'

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
declare global {
  // biome-ignore lint/style/noNamespace: <>
  namespace Cypress {
    interface Chainable {
      /**
       *  Authenticates using magic-tokens which takes in fake dev auth
       *  tokens. `fixture` is used to mock the response for the `UserDetails`
       */
      loginWithMagicToken(
        magicToken: MagicToken | AdminMagicToken,
        fixture?: string | object
      ): Chainable<JQuery<HTMLElement>>
      /**
       * Intercepts the edit user account details  request
       */
      editUserDetails(): Chainable<JQuery<HTMLElement>>
      /**
       * Selects an UI element by the custom `data-testid` attribute if provided
       */
      getByDataID(value: TestID): Chainable<JQuery<HTMLElement>>
      /**
       * Takes a snapshot of the page using the percy cypress addon. Only the
       * snapshot name is need. Screen size settings can be found in `support/visual-config.ts`
       */
      snapshot(snapshotName: string): Chainable<JQuery<HTMLElement>>
      /**
       * Clicks the desktop nav menu item that leads to the Account section only after
       * the user account info has been fetched, which is a trigger for the UI
       * to render
       */
      goToAccountSectionIfLoggedIn(): Chainable<JQuery<HTMLElement>>
      /**
       * Sets the user agent for the browser/cypress window
       */
      setUserAgent(userAgent: string): Chainable<JQuery<HTMLElement>>
    }
  }
}

Cypress.Commands.add('setUserAgent', (userAgent) => {
  Cypress.on('window:before:load', (win) => {
    Object.defineProperty(win.navigator, 'userAgent', {
      value: userAgent,
      configurable: true,
    })
  })
})

Cypress.Commands.add('loginWithMagicToken', (magicToken, fixture) => {
  cy.intercept(API.loginMagicLinkNewTab, fixture).as('magicLogin')

  const loggedInRoute = PRIVATE.MYTT_DASHBOARD

  cy.visit(`/${MAGIC_LOGIN_PARAM}/${magicToken}`)

  cy.wait('@magicLogin').then(() => {
    cy.location('pathname').should('contain', loggedInRoute)
  })
})

Cypress.Commands.add('editUserDetails', () => {
  cy.intercept({
    url: API.editUserDetails,
  }).as('editUserDetails')
})

Cypress.Commands.add('getByDataID', (selector, ...args) => {
  return cy.get(`[data-testid=${selector}]`, ...args)
})

Cypress.Commands.add('goToAccountSectionIfLoggedIn', () => {
  cy.getByDataID(TESTING_IDS.rootAccount).click({ force: true })
  cy.location('pathname').should('eq', ACCOUNT_MENU.PERSONAL_DETAILS)
})

Cypress.Commands.add('snapshot', (snapshotName) => {
  cy.percySnapshot(snapshotName, screenSizes)
})
