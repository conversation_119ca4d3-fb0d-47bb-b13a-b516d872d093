import { TESTING_IDS } from './ui-component-ids'

//Util function to help with testing
const fillAllInputFieldsWithData = ({
  skipCheckingCheckbox,
  skipTypingName,
  skipTypingLastname,
  skipTypingEmail,
}: {
  skipCheckingCheckbox?: boolean
  skipTypingName?: boolean
  skipTypingLastname?: boolean
  skipTypingEmail?: boolean
}) => {
  if (!skipTypingName) {
    cy.getByDataID(TESTING_IDS.firstNameInput)
      .as('first name input field')
      .type('DeletemeFirstName')
  }

  if (!skipTypingLastname) {
    cy.getByDataID(TESTING_IDS.lastNameInput).type('DeletemeLastName')
  }
  if (!skipTypingEmail) {
    cy.getByDataID(TESTING_IDS.emailInput).type(`<EMAIL>`)

    if (!skipCheckingCheckbox) {
      cy.get('.checkbox__body').check()
    }
  }
}

export { fillAllInputFieldsWithData }
