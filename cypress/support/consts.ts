export type MagicToken =
  | 'georgelivewell'
  | 'fakenoadminwritekyc'
  | 'fakenoadminreadkyc'
  | 'fakenoadminwritenokyc'
  | 'fakenoadminreadnokyc'
  | 'fakenoadminwritekycnopin'
  | 'fakenoadminwritenokycnopin'
  | 'fakenoadminreadkycnopinref'
  | 'fakenoadminreadnokycnopin'
  | 'georgewatanabe'

export type AdminMagicToken =
  | 'fakeadminwritekyc'
  | 'fakeadminreadkyc'
  | 'fakeadminwritenokyc'
  | 'fakeadminreadnokyc'
  | 'fakeadminadminkyc'

type CamelCaseMagicToken =
  | 'georgeLivewell'
  | 'fakeNoAdminWriteKyc'
  | 'fakeNoAdminReadKyc'
  | 'fakeNoAdminWriteNoKyc'
  | 'fakeNoAdminReadNoKyc'
  | 'fakeNoAdminWriteKycNoPin'
  | 'fakeNoAdminWriteNoKycNoPin'
  | 'fakeNoAdminReadKycNoPinRef'
  | 'fakeNoAdminReadNoKycNoPin'
  | 'georgeWatanabe'

type CamelCaseAdminMagicToken =
  | 'fakeAdminWriteKyc'
  | 'fakeAdminReadKyc'
  | 'fakeAdminWriteNoKyc'
  | 'fakeAdminReadNoKyc'
  | 'fakeAdminAdminKyc'

/**
 * Magic tokens for end users with different states of their account
 */
const MAGIC_TOKEN: Record<CamelCaseMagicToken, MagicToken> = {
  georgeLivewell: 'georgelivewell',
  georgeWatanabe: 'georgewatanabe',
  fakeNoAdminWriteKyc: 'fakenoadminwritekyc',
  fakeNoAdminReadKyc: 'fakenoadminreadkyc',
  fakeNoAdminWriteNoKyc: 'fakenoadminwritenokyc',
  fakeNoAdminReadNoKyc: 'fakenoadminreadnokyc',
  fakeNoAdminWriteKycNoPin: 'fakenoadminwritekycnopin',
  fakeNoAdminWriteNoKycNoPin: 'fakenoadminwritenokycnopin',
  fakeNoAdminReadKycNoPinRef: 'fakenoadminreadkycnopinref',
  fakeNoAdminReadNoKycNoPin: 'fakenoadminreadnokycnopin',
}

/**
 * Magic tokens meant only for in house users that have super privileges and can
 * access the admin dashboard for the webapp
 */
const ADMIN_MAGIC_TOKEN: Record<CamelCaseAdminMagicToken, AdminMagicToken> = {
  fakeAdminWriteKyc: 'fakeadminwritekyc',
  fakeAdminReadKyc: 'fakeadminreadkyc',
  fakeAdminWriteNoKyc: 'fakeadminwritenokyc',
  fakeAdminReadNoKyc: 'fakeadminreadnokyc',
  fakeAdminAdminKyc: 'fakeadminadminkyc',
}

export { MAGIC_TOKEN, ADMIN_MAGIC_TOKEN }
