import task from '@cypress/code-coverage/task'
import { defineConfig } from 'cypress'

// Webapp dev server port
const port = process.env.VITE_PORT ?? 8080

export default defineConfig({
  video: false,
  screenshotOnRunFailure: false,
  component: {
    // Gotta be separate setup e2e and compo, otherwise does not work
    // I have no idea why
    setupNodeEvents(on, config) {
      task(on, config)
      return config
    },
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },

  e2e: {
    baseUrl: `http://0.0.0.0:${port}`,
    setupNodeEvents(on, config) {
      task(on, config)
      return config
    },
  },
})
