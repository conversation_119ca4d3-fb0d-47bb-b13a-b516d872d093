import { fillAllInputFieldsWithData } from '../support/utils'

describe('Referral program anon redeeming with sign up', () => {
  // Works, but the backend generates a random code each time
  it(`referral link takes the user to the register form and signs up successfully`, () => {
    // Fake static referral code
    cy.visit(`/u/Ld0000`, {
      failOnStatusCode: false,
    })

    cy.get('.register').as('register form').should('be.visible')

    fillAllInputFieldsWithData({})

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.enabled')
      .click({ force: true })

    cy.get('.register').as('register form')

    cy.get('.confirmationModal').as('success modal').should('be.visible')
  })
})
