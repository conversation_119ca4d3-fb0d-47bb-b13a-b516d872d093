import { API } from '../../src/common/api/API'
import { PUBLIC } from '../../src/routes/Route'
import { TESTING_IDS } from '../support/ui-component-ids'
import { fillAllInputFieldsWithData } from '../support/utils'

const clickTroughTontinatorFlowAndSnapshot = (skipSnapshot?: boolean) => {
  cy.get('.home__onboarding-buttons .button--primary')
    .as('how it works button')
    .click({ force: true })

  cy.get('.onboarding-form').should('be.visible')
  if (!skipSnapshot) {
    cy.snapshot('Sex form snapshot')
  }
  cy.get('.navigation-btns__container > .button--primary')
    .as('sex form next btn')
    .click({ force: true })

  cy.get('.onboarding-form').should('be.visible')
  if (!skipSnapshot) {
    cy.snapshot('Age form')
  }
  cy.get('.navigation-btns__container > .button--primary')
    .as('age form next btn')
    .click({ force: true })

  cy.get('.onboarding-form').should('be.visible')
  if (!skipSnapshot) {
    cy.snapshot('Contribution form')
  }
  cy.get('.navigation-btns__container > .button--primary')
    .as('contribution form next btn')
    .click({ force: true })
}

describe('Tontinator anon flow', () => {
  it('completes the flow successfully and a register modal is shown in the end', () => {
    cy.visit(PUBLIC.HOME, {
      failOnStatusCode: false,
    })

    clickTroughTontinatorFlowAndSnapshot()

    //This is the goal of the test, to reach the end of the flow and get the
    //register modal
    cy.get('.register').as('modal register form').should('be.visible')
    cy.snapshot('Tontinator register form')
  })

  it('register successfully from the register modal with forecast params', () => {
    cy.intercept({
      url: API.register,
    }).as('register')

    cy.visit(PUBLIC.HOME, {
      failOnStatusCode: false,
    })

    clickTroughTontinatorFlowAndSnapshot(true)

    //This is the goal of the test, to reach the end of the flow and get the
    //register modal
    cy.get('.register').as('modal register form').should('be.visible')

    fillAllInputFieldsWithData({})

    cy.wait(1_000)
    cy.getByDataID(TESTING_IDS.registerButton)
      .should('be.enabled')
      .click({ force: true })

    cy.wait('@register').then(({ response }) => {
      //Assert if account is successfully registered
      expect(response?.statusCode).to.be.equal(200)
      cy.get('.confirmationModal').as('success modal').should('be.visible')

      cy.snapshot('Tontinator flow successful registration')
    })
  })

  it('tontinator request occurs in the background', () => {
    cy.intercept({
      url: API.tontinatorForecast,
    }).as('tontinator request')

    cy.visit(PUBLIC.HOME, {
      failOnStatusCode: false,
    })

    clickTroughTontinatorFlowAndSnapshot(true)

    cy.wait('@tontinator request').then(({ response }) => {
      expect(response?.statusCode).to.be.equal(200)
      cy.log(`${API.tontinatorForecast}`, JSON.stringify(response?.body))
      expect(
        (response as { body: Array<{ results: Array<object> }> })?.body[0]
          .results
      ).to.not.equal(null)
    })
  })
})
