import { API } from '../../src/common/api/API'
import { PRIVATE, PUBLIC } from '../../src/routes/Route'
import { TESTING_IDS } from '../support/ui-component-ids'
import { fillAllInputFieldsWithData } from '../support/utils'

it('redirects to login page when trying to access a private route unauthenticated', () => {
  describe('Public to private routes', () => {
    cy.visit(PRIVATE.ACCOUNT, {
      failOnStatusCode: false,
    })

    cy.location('pathname').should('eq', PUBLIC.SIGN_IN)
  })
})

describe('Home page', () => {
  it('home page buttons lead to their respective pages', () => {
    cy.visit(PUBLIC.HOME, {
      failOnStatusCode: false,
    })

    cy.snapshot('Home page')

    //Buttons on the bottom of the page
    cy.getByDataID(TESTING_IDS.howItWorksBtn).as('how it works btn')

    cy.getByDataID(TESTING_IDS.signUpHomeBtn)
      .as('sign up btn')
      .should('be.visible')
    //Top login button on the nav bar
    cy.getByDataID(TESTING_IDS.loginBtnDesktop)
      .should('be.visible')
      .as('top nav bar login btn')
      .click({ force: true })

    cy.get('.login-form').should('be.visible')

    cy.snapshot('Login page')
    //After visiting the login page click the logo and go back to the home page
    cy.get('.navBar__icon').as('mytt nav bar logo').click({ force: true })

    //Starts the tontinator flow
    cy.get('@how it works btn').click({ force: true })
    cy.get('.onboarding-form').should('be.visible')

    cy.getByDataID(TESTING_IDS.signUpHomeBtn)
      .should('be.visible')
      .as('top nav bar login btn')
      .click({ force: true })

    cy.get('.register').as('register form').should('be.visible')
    cy.snapshot('Register page')

    //After visiting the sign up page click the back button
    cy.get('.button--back--light').click({ force: true })

    cy.get('.home').should('be.visible')
  })
})

describe('Login page', () => {
  it('login with email and login with face buttons are disabled on initial load', () => {
    cy.visit(PUBLIC.SIGN_IN, {
      failOnStatusCode: false,
    })

    cy.getByDataID(TESTING_IDS.emailInput)
      .as('email input field')
      .should('be.visible')

    cy.get('.button--primary')
      .as('login with facetec btn')
      .should('be.disabled')

    cy.get('.button--alternative')
      .as('login with your email')
      .should('be.disabled')
  })

  it('login with email button send a login email and success modal appears', () => {
    const emailToType = '<EMAIL>'

    cy.intercept({
      url: API.sendEmailMagicLinkNewTab,
    }).as('sendEmailMagicLinkNewTab')

    cy.visit(PUBLIC.SIGN_IN, {
      failOnStatusCode: false,
    })

    cy.getByDataID(TESTING_IDS.emailInput)
      .as('email input field')
      .should('be.visible')

    cy.get('@email input field').type(emailToType)

    cy.get('.button--primary').as('login with facetec btn').should('be.enabled')

    cy.get('.button--alternative')
      .as('login with your email')
      .should('be.enabled')
      .click({ force: true })

    cy.wait('@sendEmailMagicLinkNewTab').then(({ response }) => {
      //Should always show success modal no matter what
      cy.get('.confirmationModal')
        .as('email sent success modal')
        .should('be.visible')

      cy.get('.confirmationModal__explainer')
        .as('modal content')
        .should('include.text', emailToType)

      //Check if the request really succeeded
      expect(response?.statusCode).equal(200)
    })

    cy.snapshot('Login form email sent')
  })

  it('login with email and login with face buttons are disabled when input validation does not pass', () => {
    cy.visit(PUBLIC.SIGN_IN, {
      failOnStatusCode: false,
    })

    cy.getByDataID(TESTING_IDS.emailInput)
      .as('email input field')
      .should('be.visible')

    cy.get('@email input field').type('georgelievewell.com')

    cy.get('.userInput__wrapper input').should(
      'have.class',
      TESTING_IDS.inputErrorState
    )

    cy.get('.userInput .error-text')
      .as('email field error txt')
      .should('be.visible')

    cy.get('.button--primary')
      .as('login with facetec btn')
      .should('be.disabled')
    cy.get('.button--alternative')
      .as('login with your email')
      .should('be.disabled')

    cy.snapshot('Login form error state')
  })

  it('login with facetec disabled when unsupported browser user agent is detected', () => {
    const emailToType = '<EMAIL>'
    const unsupportedUserAgent = 'Web Browser'

    cy.setUserAgent(unsupportedUserAgent)
    cy.visit(PUBLIC.SIGN_IN)
    cy.getByDataID(TESTING_IDS.emailInput)
      .as('email input field')
      .should('be.visible')
    cy.get('@email input field').type(emailToType)
    cy.getByDataID(TESTING_IDS.faceScanLoginButton)
      .as('login with facetec btn')
      .should('be.disabled')
    cy.getByDataID(TESTING_IDS.faceScanBrowserErrorText)
      .as('unsupported browser error message')
      .should('be.visible')
    cy.getByDataID(TESTING_IDS.faceScanBrowserErrorLink)
      .as('unsupported browser error link')
      .click()
    cy.snapshot('Supported browsers container')
  })
})

describe('Register page', () => {
  it('submit button is disabled and TOC checkbox is unchecked', () => {
    cy.visit(PUBLIC.SIGN_UP, {
      failOnStatusCode: false,
    })

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.disabled')

    cy.get('.checkbox__body').as('TOC checkbox').should('not.be.checked')
  })

  it('submit buttons is disabled of all fields have valid data but TOC checkbox is unchecked', () => {
    cy.visit(PUBLIC.SIGN_UP, {
      failOnStatusCode: false,
    })
    fillAllInputFieldsWithData({ skipCheckingCheckbox: true })

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.disabled')
  })

  it('successful sign up with all passed validation', () => {
    // Using the variables from MyTontine config, does not work with
    // interceptors here for some reason
    cy.intercept({
      url: API.register,
    }).as('register')

    cy.visit(PUBLIC.SIGN_UP, {
      failOnStatusCode: false,
    })

    fillAllInputFieldsWithData({})

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.enabled')
      .click({ force: true })

    cy.wait('@register').then(({ request, response }) => {
      cy.log(`Sent to /create`, JSON.stringify(request?.body, null, 2))
      expect(response?.statusCode).to.equal(200)
      expect(request?.body).to.haveOwnProperty('web_signup').equal(false)
    })
  })

  it(`typing all fields then deleting data from one 
  renders an error and disables register button`, () => {
    cy.visit(PUBLIC.SIGN_UP, {
      failOnStatusCode: false,
    })

    fillAllInputFieldsWithData({})

    cy.get('@first name input field').clear()

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.disabled')

    cy.get('.error-text')
      .as('first name input field err msg')
      .should('be.visible')

    cy.snapshot('Register form error state')
  })

  it(`register button becomes enabled and error message is wiped
  after user retypes their first name`, () => {
    cy.visit(PUBLIC.SIGN_UP, {
      failOnStatusCode: false,
    })

    fillAllInputFieldsWithData({})

    cy.get('@first name input field').clear()

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.disabled')

    cy.get('.error-text')
      .as('first name input field err msg')
      .should('be.visible')

    cy.get('.userInput__input-element--error').type('DeletemeFirstName')

    cy.get('@first name input field err msg').should('not.exist')

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.enabled')

    cy.get('@register button').click({ force: true })

    cy.get('.confirmationModal').as('success modal').should('be.visible')
  })

  it(`user successfully signs up with non existent referral code`, () => {
    cy.visit(`/u/notexcisting`, {
      failOnStatusCode: false,
    })

    fillAllInputFieldsWithData({})

    cy.get('.navigation-btns__container > .button--primary')
      .as('register button')
      .should('be.enabled')
      .click({ force: true })

    cy.get('.register').as('register form')

    cy.get('.confirmationModal').as('success modal').should('be.visible')
  })
})

describe('T&C modal tests anon', () => {
  beforeEach(() => {
    cy.visit(PUBLIC.SIGN_UP, {
      failOnStatusCode: false,
    })
  })

  it('should fetch latest version of T&C', () => {
    cy.intercept({
      url: `${API.getAgreement}/TermsAndConditions/latest`,
    }).as('termsAndConditions')

    cy.wait('@termsAndConditions').then(({ response }) => {
      cy.log(`Fetched ${JSON.stringify(response?.body)}`)
      expect(response?.statusCode).to.be.equal(200)
    })
  })

  it('signs T&C from the T&C modal and both checkboxes are checked', () => {
    cy.getByDataID(TESTING_IDS.termsLabel).click({ force: true })
    cy.get('.pageContent__container').should('be.visible')
    cy.get('.agreementContent > .checkbox > .checkbox__body').check()
    cy.get('.pageContent__container').should('not.exist')
    cy.get(':nth-child(1) > .checkbox__body').should('be.checked')
  })

  it('checking the T&C checkbox also checks it on the modal and if unchecked unchecks it on the sign up page', () => {
    cy.get(':nth-child(1) > .checkbox__body').check()
    cy.getByDataID(TESTING_IDS.termsLabel).click({ force: true })
    cy.get('.pageContent__container').should('be.visible')
    cy.get('.agreementContent > .checkbox > .checkbox__body').should(
      'be.checked'
    )
    cy.get('.agreementContent > .checkbox > .checkbox__body').uncheck()
    cy.get('.modal__close-icon').click({ force: true })
    cy.get(':nth-child(1) > .checkbox__body').should('not.be.checked')
  })
})
