import { sentryVitePlugin } from '@sentry/vite-plugin'
import react from '@vitejs/plugin-react'
import { defineConfig, PluginOption } from 'vite'
import istanbul from 'vite-plugin-istanbul'
import { istanbulConfig } from './src/config/instrumenting'

const envs = {
  // Needs to be like this in order not fail tests that rely on
  //  CSS classes
  // https://github.com/TontineTrust/mytontine-webapp/issues/3439
  environment: process.env.VITE_ENVIRONMENT,
  port: process.env.VITE_PORT,
  host: process.env.VITE_HOST,
  buildVariation: process.env.VITE_BUILD_VARIATION,
  instrumentCode: process.env.VITE_INSTRUMENT_CODE
    ? JSON.parse(process.env.VITE_INSTRUMENT_CODE)
    : false,
}

console.log('Starting server with envs:')
console.table(envs)

/**
 * Vite plugins, react must always be present!
 */
const plugins: PluginOption[] = [react()]

// Plugins added only in staging or prod env
if (envs.environment && envs.environment !== 'development') {
  plugins.push(
    sentryVitePlugin({
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: 'tontinetrust',
      project: 'my-tontine-web-application',
      debug: true,
      telemetry: false,
      sourcemaps: {
        // Deletes all sources maps
        filesToDeleteAfterUpload: ['**/*.js.map'],
      },
    })
  )
}

// Plugins to be included only in dev env
if (envs.environment === 'development') {
  if (envs?.instrumentCode) {
    plugins.push(
      istanbul({
        ...istanbulConfig,
      })
    )
  }
}

export default defineConfig(() => {
  return {
    //Shared general options with plugins array
    base: './',
    root: '.',
    // Plugins array is defined at the very top line 20
    // to be precise
    plugins,
    // Dev server runs with vite preview, give a preview of the app in
    // production
    server: {
      port: envs?.port ? Number(envs.port) : undefined,
      // Do not use ?? here
      host: envs?.host || '0.0.0.0',
    },

    // Preview server options, runs only when there is a bundle to serve
    preview: {
      port: envs?.port ? Number(envs.port) : undefined,
      // Do not use ?? here
      host: envs?.host || '0.0.0.0',
    },

    css: {
      modules: {
        generateScopedName:
          envs?.environment === 'development' || !envs?.environment
            ? `[local]`
            : // CSS File names are only hashed in production/staging
              `[hash:base64:5]`,
      },
    },

    // Build options when the app is built in production mode
    build: {
      // Source maps are included BUT deleted after uploaded to sentry!
      // Source maps must be always enabled for easier sentry debugging!
      sourcemap: true,
      rollupOptions: {
        output: {
          assetFileNames: '[hash:10].[ext]',
          chunkFileNames: '[hash:10].js',
          //Creates chunks from all the external dependencies the project uses
          //Dependencies are grouped by vendor, if a dependency is more than
          //100kB it is not bundled into a chunk and it is loaded as a
          //independent chunk, for example `ibantools` is loaded independently
          manualChunks: {
            'libphonenumber-js': ['libphonenumber-js'],
            'sentry-react': ['@sentry/react'],
            xstate: ['xstate', '@xstate/react'],
            d3: ['d3'],
            'react-dom': ['react-dom'],
            'react-router': ['react-router'],
            toastify: ['react-toastify'],
            i18next: ['react-i18next', 'i18next-http-backend', 'i18next'],
            locize: ['locize-lastused', 'locize'],
            i18nUtils: [
              'i18next-localstorage-backend',
              'i18next-chained-backend',
              'i18next-browser-languagedetector',
            ],
            'lottie-web': ['lottie-web/build/player/lottie_light'],
            'react-utils': ['axios', 'dayjs', '@docuseal/react'],
            'react-share': ['react-share'],
            'mixpanel-browser': ['mixpanel-browser'],
          },
        },
      },
    },
  }
})
