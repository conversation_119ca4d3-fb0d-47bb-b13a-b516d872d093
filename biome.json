{"$schema": "https://biomejs.dev/schemas/2.2.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "staging"}, "files": {"ignoreUnknown": false, "includes": ["**", "!.vscode/**/*", "!**/facetec-web-sdk/**/*", "!**/public/**/*", "!**/__test__/**/*", "!my-tontine/dist/**/*", "!**/.netlify/**/*", "!**/*.d.ts"]}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "off", "useLiteralKeys": "off"}, "correctness": {"noUndeclaredVariables": "error", "noUnusedVariables": "error", "noUnusedImports": "error", "useExhaustiveDependencies": "off"}, "style": {"noUnusedTemplateLiteral": "off", "useNodejsImportProtocol": "off", "useImportType": "off", "noParameterAssign": "warn", "useBlockStatements": "off", "noNamespace": "error", "noParameterProperties": "error", "noNestedTernary": "error"}, "suspicious": {"noEmptyBlockStatements": "error", "noArrayIndexKey": "off", "noGlobalIsNan": "off"}, "a11y": {"useKeyWithClickEvents": "off", "noStaticElementInteractions": "off", "useSemanticElements": "off"}}, "includes": ["**", "!**/public/**/*", "!**/__test__/**/*", "!my-tontine/dist/**/*", "!**/facetec-web-sdk/**/*", "!.vscode/**/*", "!**/.netlify/**/*", "!**/coverage/**/*", "!**/coverage-ts/**/*", "!**/*.d.ts"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single"}, "globals": ["JSX", "React", "InstallTrigger"]}, "overrides": [{"includes": ["cypress/**/*.ts"], "linter": {"rules": {"suspicious": {"noDebugger": "off"}}}}, {"includes": ["**/*.jsx", "**/*.tsx"], "linter": {"rules": {"correctness": {"useHookAtTopLevel": "error"}}}}, {"includes": ["cypress/**/*"], "javascript": {"globals": ["<PERSON><PERSON><PERSON><PERSON>", "cy", "expect", "describe", "it", "mount", "context", "wrap", "beforeEach", "Cypress", "sinon"]}}]}